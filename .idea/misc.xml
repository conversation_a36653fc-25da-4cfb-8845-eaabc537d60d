<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/META-INF/maven/org.jcraft/jorbis/pom.xml" />
        <option value="$PROJECT_DIR$/META-INF/maven/net.java.jutils/jutils/pom.xml" />
        <option value="$PROJECT_DIR$/META-INF/maven/com.badlogicgames.gdx/gdx/pom.xml" />
        <option value="$PROJECT_DIR$/META-INF/maven/com.badlogicgames.gdx/gdx-freetype/pom.xml" />
        <option value="$PROJECT_DIR$/META-INF/maven/com.badlogicgames.gdx/gdx-backend-lwjgl/pom.xml" />
        <option value="$PROJECT_DIR$/META-INF/maven/com.badlogicgames.jlayer/jlayer/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" project-jdk-name="1.8" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/out" />
  </component>
</project>