package a.a.a;

/* compiled from: BitstreamException.java */
/* loaded from: naruto-game.jar:a/a/a/c.class */
public final class c extends h {

    /* renamed from: a  reason: collision with root package name */
    private int f7a;

    public c(String str, Throwable th) {
        super(str, th);
        this.f7a = 256;
    }

    public c(int i, Throwable th) {
        this("Bitstream errorcode " + Integer.toHexString(i), th);
        this.f7a = i;
    }

    public final int a() {
        return this.f7a;
    }
}