package a.a.a;

/* compiled from: OutputBuffer.java */
/* loaded from: naruto-game.jar:a/a/a/m.class */
public final class m {

    /* renamed from: a  reason: collision with root package name */
    private int f36a;

    /* renamed from: b  reason: collision with root package name */
    private byte[] f37b;
    private int[] c;
    private boolean d = false;

    public m(int i, boolean z) {
        this.f36a = i;
        this.f37b = new byte[i * 2304];
        this.c = new int[i];
        b();
    }

    public final void a(int i, float[] fArr) {
        short s;
        byte b2;
        int i2;
        int i3 = 0;
        while (i3 < 32) {
            int i4 = i3;
            i3++;
            float f = fArr[i4];
            if (f > 32767.0f) {
                s = Short.MAX_VALUE;
            } else {
                s = f < -32768.0f ? Short.MIN_VALUE : (short) f;
            }
            short s2 = s;
            if (this.d) {
                b2 = (byte) (s2 >>> 8);
                i2 = s2;
            } else {
                b2 = (byte) s2;
                i2 = s2 >>> 8;
            }
            this.f37b[this.c[i]] = b2;
            this.f37b[this.c[i] + 1] = (byte) i2;
            int[] iArr = this.c;
            iArr[i] = iArr[i] + (this.f36a << 1);
        }
    }

    public final byte[] a() {
        return this.f37b;
    }

    public final int b() {
        try {
            int i = this.f36a - 1;
            int i2 = this.c[i] - (i << 1);
            for (int i3 = 0; i3 < this.f36a; i3++) {
                int i4 = i3;
                this.c[i4] = i4 << 1;
            }
            return i2;
        } catch (Throwable th) {
            for (int i5 = 0; i5 < this.f36a; i5++) {
                int i6 = i5;
                this.c[i6] = i6 << 1;
            }
            throw th;
        }
    }
}