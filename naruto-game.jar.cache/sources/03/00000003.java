package a.a.a;

/* compiled from: Crc16.java */
/* loaded from: naruto-game.jar:a/a/a/d.class */
public final class d {

    /* renamed from: a  reason: collision with root package name */
    private static short f8a = -32763;

    /* renamed from: b  reason: collision with root package name */
    private short f9b = -1;

    public final void a(int i, int i2) {
        int i3;
        int i4 = 1 << (i2 - 1);
        do {
            if (((this.f9b & 32768) == 0) ^ ((i & i4) == 0)) {
                this.f9b = (short) (this.f9b << 1);
                this.f9b = (short) (this.f9b ^ f8a);
            } else {
                this.f9b = (short) (this.f9b << 1);
            }
            i3 = i4 >>> 1;
            i4 = i3;
        } while (i3 != 0);
    }

    public final short a() {
        short s = this.f9b;
        this.f9b = (short) -1;
        return s;
    }
}