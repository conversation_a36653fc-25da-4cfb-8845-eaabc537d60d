package a.a.a;

import com.badlogic.gdx.net.HttpStatus;
import org.lwjgl.opengl.LinuxKeycodes;

/* compiled from: LayerIIIDecoder.java */
/* loaded from: naruto-game.jar:a/a/a/k.class */
public final class k implements a.a.a.f {

    /* renamed from: b  reason: collision with root package name */
    private int[] f21b;
    private int[] d;
    private float[][][] e;
    private float[][][] f;
    private float[] g;
    private float[][] h;
    private float[][] i;
    private int[] j;
    private a.a.a.b k;
    private g l;
    private n m;
    private n n;
    private m o;
    private int p;
    private a.a.a.a q;
    private a r;
    private f[] s;
    private f[] t;
    private int u;
    private int v;
    private int w;
    private int x;
    private int y;
    private int z;
    private int A;
    private b[] P;
    private static float[] R;
    private static float[][] S;
    private static float[] T;
    private static int[][] U;
    private static final float[] V;
    private static final float[] W;
    private static float[][] X;
    private c Y;
    private static int[][][] Z;
    private static final int[][] N = {new int[]{0, 0, 0, 0, 3, 1, 1, 1, 2, 2, 2, 3, 3, 3, 4, 4}, new int[]{0, 1, 2, 3, 0, 1, 2, 3, 1, 2, 3, 1, 2, 3, 2, 3}};
    private static int[] O = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 3, 3, 3, 2, 0};
    private static float[] Q = {1.0f, 0.70710677f, 0.5f, 0.35355338f, 0.25f, 0.17677669f, 0.125f, 0.088388346f, 0.0625f, 0.044194173f, 0.03125f, 0.022097087f, 0.015625f, 0.011048543f, 0.0078125f, 0.0055242716f, 0.00390625f, 0.0027621358f, 0.001953125f, 0.0013810679f, 9.765625E-4f, 6.9053395E-4f, 4.8828125E-4f, 3.4526698E-4f, 2.4414062E-4f, 1.7263349E-4f, 1.2207031E-4f, 8.6316744E-5f, 6.1035156E-5f, 4.3158372E-5f, 3.0517578E-5f, 2.1579186E-5f, 1.5258789E-5f, 1.0789593E-5f, 7.6293945E-6f, 5.3947965E-6f, 3.8146973E-6f, 2.6973983E-6f, 1.9073486E-6f, 1.3486991E-6f, 9.536743E-7f, 6.7434956E-7f, 4.7683716E-7f, 3.3717478E-7f, 2.3841858E-7f, 1.6858739E-7f, 1.1920929E-7f, 8.4293696E-8f, 5.9604645E-8f, 4.2146848E-8f, 2.9802322E-8f, 2.1073424E-8f, 1.4901161E-8f, 1.0536712E-8f, 7.450581E-9f, 5.268356E-9f, 3.7252903E-9f, 2.634178E-9f, 1.8626451E-9f, 1.317089E-9f, 9.313226E-10f, 6.585445E-10f, 4.656613E-10f, 3.2927225E-10f};

    /* renamed from: a  reason: collision with root package name */
    private double f20a = 1.3333333333333333d;
    private int c = 0;
    private float[] B = new float[32];
    private float[] C = new float[32];
    private final int[] D = new int[4];
    private int[] E = {0};
    private int[] F = {0};
    private int[] G = {0};
    private int[] H = {0};
    private int[] I = new int[576];
    private float[] J = new float[576];
    private float[] K = new float[18];
    private float[] L = new float[36];
    private int M = 0;

    /* JADX INFO: Access modifiers changed from: package-private */
    /* compiled from: LayerIIIDecoder.java */
    /* loaded from: naruto-game.jar:a/a/a/k$d.class */
    public static class d {

        /* renamed from: a  reason: collision with root package name */
        public int f28a = 0;

        /* renamed from: b  reason: collision with root package name */
        public int f29b = 0;
        public int c = 0;
        public int d = 0;
        public int e = 0;
        public int f = 0;
        public int g = 0;
        public int j = 0;
        public int k = 0;
        public int l = 0;
        public int m = 0;
        public int n = 0;
        public int[] h = new int[3];
        public int[] i = new int[3];
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    /* compiled from: LayerIIIDecoder.java */
    /* loaded from: naruto-game.jar:a/a/a/k$f.class */
    public static class f {

        /* renamed from: a  reason: collision with root package name */
        public int[] f32a = new int[23];

        /* renamed from: b  reason: collision with root package name */
        public int[][] f33b = new int[3][13];
    }

    /* JADX WARN: Type inference failed for: r0v121, types: [int[], int[][]] */
    public k(a.a.a.b bVar, g gVar, n nVar, n nVar2, m mVar, int i) {
        o.a();
        this.d = new int[580];
        this.e = new float[2][32][18];
        this.f = new float[2][32][18];
        this.g = new float[576];
        this.h = new float[2][576];
        this.i = new float[2][576];
        this.j = new int[2];
        this.s = new f[2];
        this.s[0] = new f();
        this.s[1] = new f();
        this.t = this.s;
        this.P = new b[9];
        this.P[0] = new b(new int[]{0, 6, 12, 18, 24, 30, 36, 44, 54, 66, 80, 96, 116, 140, 168, 200, 238, 284, 336, 396, 464, 522, 576}, new int[]{0, 4, 8, 12, 18, 24, 32, 42, 56, 74, 100, 132, 174, 192});
        this.P[1] = new b(new int[]{0, 6, 12, 18, 24, 30, 36, 44, 54, 66, 80, 96, 114, 136, 162, 194, LinuxKeycodes.XK_egrave, 278, 330, 394, 464, 540, 576}, new int[]{0, 4, 8, 12, 18, 26, 36, 48, 62, 80, 104, 136, 180, 192});
        this.P[2] = new b(new int[]{0, 6, 12, 18, 24, 30, 36, 44, 54, 66, 80, 96, 116, 140, 168, 200, 238, 284, 336, 396, 464, 522, 576}, new int[]{0, 4, 8, 12, 18, 26, 36, 48, 62, 80, 104, 134, 174, 192});
        this.P[3] = new b(new int[]{0, 4, 8, 12, 16, 20, 24, 30, 36, 44, 52, 62, 74, 90, 110, 134, 162, 196, 238, 288, 342, 418, 576}, new int[]{0, 4, 8, 12, 16, 22, 30, 40, 52, 66, 84, 106, 136, 192});
        this.P[4] = new b(new int[]{0, 4, 8, 12, 16, 20, 24, 30, 36, 42, 50, 60, 72, 88, 106, 128, 156, 190, 230, 276, 330, 384, 576}, new int[]{0, 4, 8, 12, 16, 22, 28, 38, 50, 64, 80, 100, 126, 192});
        this.P[5] = new b(new int[]{0, 4, 8, 12, 16, 20, 24, 30, 36, 44, 54, 66, 82, 102, 126, 156, 194, 240, 296, 364, 448, 550, 576}, new int[]{0, 4, 8, 12, 16, 22, 30, 42, 58, 78, 104, 138, 180, 192});
        this.P[6] = new b(new int[]{0, 6, 12, 18, 24, 30, 36, 44, 54, 66, 80, 96, 116, 140, 168, 200, 238, 284, 336, 396, 464, 522, 576}, new int[]{0, 4, 8, 12, 18, 26, 36, 48, 62, 80, 104, 134, 174, 192});
        this.P[7] = new b(new int[]{0, 6, 12, 18, 24, 30, 36, 44, 54, 66, 80, 96, 116, 140, 168, 200, 238, 284, 336, 396, 464, 522, 576}, new int[]{0, 4, 8, 12, 18, 26, 36, 48, 62, 80, 104, 134, 174, 192});
        this.P[8] = new b(new int[]{0, 12, 24, 36, 48, 60, 72, 88, 108, 132, 160, 192, LinuxKeycodes.XK_egrave, 280, 336, HttpStatus.SC_BAD_REQUEST, 476, 566, 568, 570, 572, 574, 576}, new int[]{0, 8, 16, 24, 36, 52, 72, 96, 124, 160, 162, 164, 166, 192});
        if (U == null) {
            U = new int[9];
            for (int i2 = 0; i2 < 9; i2++) {
                U[i2] = a(this.P[i2].f25b);
            }
        }
        this.Y = new c(new int[]{0, 6, 11, 16, 21}, new int[]{0, 6, 12});
        this.f21b = new int[54];
        this.k = bVar;
        this.l = gVar;
        this.m = nVar;
        this.n = nVar2;
        this.o = mVar;
        this.p = 0;
        this.v = 0;
        this.x = this.l.f() == 3 ? 1 : 2;
        this.u = this.l.a() == 1 ? 2 : 1;
        this.A = this.l.d() + (this.l.a() == 1 ? 3 : this.l.a() == 2 ? 6 : 0);
        if (this.x == 2) {
            switch (this.p) {
                case 1:
                case 3:
                    this.z = 0;
                    this.y = 0;
                    break;
                case 2:
                    this.z = 1;
                    this.y = 1;
                    break;
                default:
                    this.y = 0;
                    this.z = 1;
                    break;
            }
        } else {
            this.z = 0;
            this.y = 0;
        }
        for (int i3 = 0; i3 < 2; i3++) {
            for (int i4 = 0; i4 < 576; i4++) {
                this.h[i3][i4] = 0.0f;
            }
        }
        int[] iArr = this.j;
        this.j[1] = 576;
        iArr[0] = 576;
        this.q = new a.a.a.a();
        this.r = new a();
    }

    /* JADX WARN: Code restructure failed: missing block: B:145:0x1019, code lost:
        if (r1 != 1) goto L268;
     */
    /* JADX WARN: Code restructure failed: missing block: B:158:0x1125, code lost:
        if (r0 == 3) goto L343;
     */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r2v132 */
    @Override // a.a.a.f
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final void a() {
        /*
            Method dump skipped, instructions count: 7441
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: a.a.a.k.a():void");
    }

    private void a(int i, int i2, int i3) {
        if (i == 0) {
            this.i[0][i3] = 1.0f;
            this.i[1][i3] = 1.0f;
        } else if ((i & 1) != 0) {
            this.i[0][i3] = S[i2][(i + 1) >>> 1];
            this.i[1][i3] = 1.0f;
        } else {
            this.i[0][i3] = 1.0f;
            this.i[1][i3] = S[i2][i >>> 1];
        }
    }

    private void a(float[][] fArr, int i, int i2) {
        d dVar = this.r.c[i].f31b[i2];
        if (dVar.e != 0 && dVar.f == 2) {
            for (int i3 = 0; i3 < 576; i3++) {
                this.g[i3] = 0.0f;
            }
            if (dVar.g != 0) {
                for (int i4 = 0; i4 < 36; i4++) {
                    int i5 = i4 % 18;
                    this.g[i4] = fArr[(i4 - i5) / 18][i5];
                }
                for (int i6 = 3; i6 < 13; i6++) {
                    int i7 = this.P[this.A].f25b[i6];
                    int i8 = this.P[this.A].f25b[i6 + 1] - i7;
                    int i9 = (i7 << 2) - i7;
                    int i10 = 0;
                    int i11 = 0;
                    while (i10 < i8) {
                        int i12 = i9 + i10;
                        int i13 = i9 + i11;
                        int i14 = i12 % 18;
                        this.g[i13] = fArr[(i12 - i14) / 18][i14];
                        int i15 = i12 + i8;
                        int i16 = i13 + 1;
                        int i17 = i15 % 18;
                        this.g[i16] = fArr[(i15 - i17) / 18][i17];
                        int i18 = i15 + i8;
                        int i19 = i18 % 18;
                        this.g[i16 + 1] = fArr[(i18 - i19) / 18][i19];
                        i10++;
                        i11 += 3;
                    }
                }
                return;
            }
            for (int i20 = 0; i20 < 576; i20++) {
                int i21 = U[this.A][i20];
                int i22 = i21 % 18;
                this.g[i20] = fArr[(i21 - i22) / 18][i22];
            }
            return;
        }
        for (int i23 = 0; i23 < 576; i23++) {
            int i24 = i23 % 18;
            this.g[i23] = fArr[(i23 - i24) / 18][i24];
        }
    }

    private void a(int i) {
        if (this.x == 1) {
            for (int i2 = 0; i2 < 32; i2++) {
                for (int i3 = 0; i3 < 18; i3 += 3) {
                    this.f[0][i2][i3] = this.e[0][i2][i3];
                    this.f[0][i2][i3 + 1] = this.e[0][i2][i3 + 1];
                    this.f[0][i2][i3 + 2] = this.e[0][i2][i3 + 2];
                }
            }
            return;
        }
        d dVar = this.r.c[0].f31b[i];
        int h = this.l.h();
        boolean z = this.l.f() == 1 && (h & 2) != 0;
        boolean z2 = this.l.f() == 1 && (h & 1) != 0;
        boolean z3 = this.l.a() == 0 || this.l.a() == 2;
        int i4 = dVar.d & 1;
        for (int i5 = 0; i5 < 576; i5++) {
            this.I[i5] = 7;
            this.J[i5] = 0.0f;
        }
        if (z2) {
            if (dVar.e != 0 && dVar.f == 2) {
                if (dVar.g != 0) {
                    int i6 = 0;
                    for (int i7 = 0; i7 < 3; i7++) {
                        int i8 = 2;
                        int i9 = 12;
                        while (i9 >= 3) {
                            int i10 = this.P[this.A].f25b[i9];
                            int i11 = this.P[this.A].f25b[i9 + 1] - i10;
                            int i12 = (((i10 << 2) - i10) + ((i7 + 1) * i11)) - 1;
                            while (i11 > 0) {
                                if (this.e[1][i12 / 18][i12 % 18] != 0.0f) {
                                    i8 = i9;
                                    i9 = -10;
                                    i11 = -10;
                                }
                                i11--;
                                i12--;
                            }
                            i9--;
                        }
                        int i13 = i8 + 1;
                        int i14 = i13;
                        if (i13 > i6) {
                            i6 = i14;
                        }
                        while (i14 < 12) {
                            int i15 = this.P[this.A].f25b[i14];
                            int i16 = this.P[this.A].f25b[i14 + 1] - i15;
                            int i17 = ((i15 << 2) - i15) + (i7 * i16);
                            while (i16 > 0) {
                                this.I[i17] = this.t[1].f33b[i7][i14];
                                if (this.I[i17] != 7) {
                                    if (z3) {
                                        a(this.I[i17], i4, i17);
                                    } else {
                                        this.J[i17] = T[this.I[i17]];
                                    }
                                }
                                i17++;
                                i16--;
                            }
                            i14++;
                        }
                        int i18 = this.P[this.A].f25b[10];
                        int i19 = ((i18 << 2) - i18) + (i7 * (this.P[this.A].f25b[11] - i18));
                        int i20 = this.P[this.A].f25b[11];
                        int i21 = this.P[this.A].f25b[12] - i20;
                        int i22 = ((i20 << 2) - i20) + (i7 * i21);
                        while (i21 > 0) {
                            int[] iArr = this.I;
                            iArr[i22] = iArr[i19];
                            if (z3) {
                                this.i[0][i22] = this.i[0][i19];
                                this.i[1][i22] = this.i[1][i19];
                            } else {
                                float[] fArr = this.J;
                                fArr[i22] = fArr[i19];
                            }
                            i22++;
                            i21--;
                        }
                    }
                    if (i6 <= 3) {
                        int i23 = 2;
                        int i24 = 17;
                        int i25 = -1;
                        while (i23 >= 0) {
                            if (this.e[1][i23][i24] != 0.0f) {
                                i25 = (i23 << 4) + (i23 << 1) + i24;
                                i23 = -1;
                            } else {
                                i24--;
                                if (i24 < 0) {
                                    i23--;
                                    i24 = 17;
                                }
                            }
                        }
                        int i26 = 0;
                        while (this.P[this.A].f24a[i26] <= i25) {
                            i26++;
                        }
                        int i27 = this.P[this.A].f24a[i26];
                        for (int i28 = i26; i28 < 8; i28++) {
                            for (int i29 = this.P[this.A].f24a[i28 + 1] - this.P[this.A].f24a[i28]; i29 > 0; i29--) {
                                this.I[i27] = this.t[1].f32a[i28];
                                if (this.I[i27] != 7) {
                                    if (z3) {
                                        a(this.I[i27], i4, i27);
                                    } else {
                                        this.J[i27] = T[this.I[i27]];
                                    }
                                }
                                i27++;
                            }
                        }
                    }
                } else {
                    for (int i30 = 0; i30 < 3; i30++) {
                        int i31 = -1;
                        int i32 = 12;
                        while (i32 >= 0) {
                            int i33 = this.P[this.A].f25b[i32];
                            int i34 = this.P[this.A].f25b[i32 + 1] - i33;
                            int i35 = (((i33 << 2) - i33) + ((i30 + 1) * i34)) - 1;
                            while (i34 > 0) {
                                if (this.e[1][i35 / 18][i35 % 18] != 0.0f) {
                                    i31 = i32;
                                    i32 = -10;
                                    i34 = -10;
                                }
                                i34--;
                                i35--;
                            }
                            i32--;
                        }
                        for (int i36 = i31 + 1; i36 < 12; i36++) {
                            int i37 = this.P[this.A].f25b[i36];
                            int i38 = this.P[this.A].f25b[i36 + 1] - i37;
                            int i39 = ((i37 << 2) - i37) + (i30 * i38);
                            while (i38 > 0) {
                                this.I[i39] = this.t[1].f33b[i30][i36];
                                if (this.I[i39] != 7) {
                                    if (z3) {
                                        a(this.I[i39], i4, i39);
                                    } else {
                                        this.J[i39] = T[this.I[i39]];
                                    }
                                }
                                i39++;
                                i38--;
                            }
                        }
                        int i40 = this.P[this.A].f25b[10];
                        int i41 = this.P[this.A].f25b[11];
                        int i42 = ((i40 << 2) - i40) + (i30 * (i41 - i40));
                        int i43 = this.P[this.A].f25b[12] - i41;
                        int i44 = ((i41 << 2) - i41) + (i30 * i43);
                        while (i43 > 0) {
                            int[] iArr2 = this.I;
                            iArr2[i44] = iArr2[i42];
                            if (z3) {
                                this.i[0][i44] = this.i[0][i42];
                                this.i[1][i44] = this.i[1][i42];
                            } else {
                                float[] fArr2 = this.J;
                                fArr2[i44] = fArr2[i42];
                            }
                            i44++;
                            i43--;
                        }
                    }
                }
            } else {
                int i45 = 31;
                int i46 = 17;
                int i47 = 0;
                while (i45 >= 0) {
                    if (this.e[1][i45][i46] != 0.0f) {
                        i47 = (i45 << 4) + (i45 << 1) + i46;
                        i45 = -1;
                    } else {
                        i46--;
                        if (i46 < 0) {
                            i45--;
                            i46 = 17;
                        }
                    }
                }
                int i48 = 0;
                while (this.P[this.A].f24a[i48] <= i47) {
                    i48++;
                }
                int i49 = this.P[this.A].f24a[i48];
                for (int i50 = i48; i50 < 21; i50++) {
                    for (int i51 = this.P[this.A].f24a[i50 + 1] - this.P[this.A].f24a[i50]; i51 > 0; i51--) {
                        this.I[i49] = this.t[1].f32a[i50];
                        if (this.I[i49] != 7) {
                            if (z3) {
                                a(this.I[i49], i4, i49);
                            } else {
                                this.J[i49] = T[this.I[i49]];
                            }
                        }
                        i49++;
                    }
                }
                int i52 = this.P[this.A].f24a[20];
                for (int i53 = 576 - this.P[this.A].f24a[21]; i53 > 0 && i49 < 576; i53--) {
                    int[] iArr3 = this.I;
                    iArr3[i49] = iArr3[i52];
                    if (z3) {
                        this.i[0][i49] = this.i[0][i52];
                        this.i[1][i49] = this.i[1][i52];
                    } else {
                        float[] fArr3 = this.J;
                        fArr3[i49] = fArr3[i52];
                    }
                    i49++;
                }
            }
        }
        int i54 = 0;
        for (int i55 = 0; i55 < 32; i55++) {
            for (int i56 = 0; i56 < 18; i56++) {
                if (this.I[i54] == 7) {
                    if (z) {
                        this.f[0][i55][i56] = (this.e[0][i55][i56] + this.e[1][i55][i56]) * 0.70710677f;
                        this.f[1][i55][i56] = (this.e[0][i55][i56] - this.e[1][i55][i56]) * 0.70710677f;
                    } else {
                        this.f[0][i55][i56] = this.e[0][i55][i56];
                        this.f[1][i55][i56] = this.e[1][i55][i56];
                    }
                } else if (z2) {
                    if (z3) {
                        this.f[0][i55][i56] = this.e[0][i55][i56] * this.i[0][i54];
                        this.f[1][i55][i56] = this.e[0][i55][i56] * this.i[1][i54];
                    } else {
                        this.f[1][i55][i56] = this.e[0][i55][i56] / (1.0f + this.J[i54]);
                        this.f[0][i55][i56] = this.f[1][i55][i56] * this.J[i54];
                    }
                }
                i54++;
            }
        }
    }

    private void a(int i, int i2) {
        int i3;
        d dVar = this.r.c[i].f31b[i2];
        if (dVar.e != 0 && dVar.f == 2 && dVar.g == 0) {
            return;
        }
        if (dVar.e != 0 && dVar.g != 0 && dVar.f == 2) {
            i3 = 18;
        } else {
            i3 = 558;
        }
        for (int i4 = 0; i4 < i3; i4 += 18) {
            for (int i5 = 0; i5 < 8; i5++) {
                int i6 = (i4 + 17) - i5;
                int i7 = i4 + 18 + i5;
                float f2 = this.g[i6];
                float f3 = this.g[i7];
                this.g[i6] = (f2 * V[i5]) - (f3 * W[i5]);
                this.g[i7] = (f3 * V[i5]) + (f2 * W[i5]);
            }
        }
    }

    private void b(int i, int i2) {
        d dVar = this.r.c[i].f31b[i2];
        int i3 = 0;
        while (i3 < 576) {
            int i4 = (dVar.e == 0 || dVar.g == 0 || i3 >= 36) ? dVar.f : 0;
            float[] fArr = this.g;
            for (int i5 = 0; i5 < 18; i5++) {
                this.K[i5] = fArr[i5 + i3];
            }
            a(this.K, this.L, i4);
            for (int i6 = 0; i6 < 18; i6++) {
                fArr[i6 + i3] = this.K[i6];
            }
            float[][] fArr2 = this.h;
            fArr[i3 + 0] = this.L[0] + fArr2[i][i3];
            fArr2[i][i3] = this.L[18];
            fArr[i3 + 1] = this.L[1] + fArr2[i][i3 + 1];
            fArr2[i][i3 + 1] = this.L[19];
            fArr[i3 + 2] = this.L[2] + fArr2[i][i3 + 2];
            fArr2[i][i3 + 2] = this.L[20];
            fArr[i3 + 3] = this.L[3] + fArr2[i][i3 + 3];
            fArr2[i][i3 + 3] = this.L[21];
            fArr[i3 + 4] = this.L[4] + fArr2[i][i3 + 4];
            fArr2[i][i3 + 4] = this.L[22];
            fArr[i3 + 5] = this.L[5] + fArr2[i][i3 + 5];
            fArr2[i][i3 + 5] = this.L[23];
            fArr[i3 + 6] = this.L[6] + fArr2[i][i3 + 6];
            fArr2[i][i3 + 6] = this.L[24];
            fArr[i3 + 7] = this.L[7] + fArr2[i][i3 + 7];
            fArr2[i][i3 + 7] = this.L[25];
            fArr[i3 + 8] = this.L[8] + fArr2[i][i3 + 8];
            fArr2[i][i3 + 8] = this.L[26];
            fArr[i3 + 9] = this.L[9] + fArr2[i][i3 + 9];
            fArr2[i][i3 + 9] = this.L[27];
            fArr[i3 + 10] = this.L[10] + fArr2[i][i3 + 10];
            fArr2[i][i3 + 10] = this.L[28];
            fArr[i3 + 11] = this.L[11] + fArr2[i][i3 + 11];
            fArr2[i][i3 + 11] = this.L[29];
            fArr[i3 + 12] = this.L[12] + fArr2[i][i3 + 12];
            fArr2[i][i3 + 12] = this.L[30];
            fArr[i3 + 13] = this.L[13] + fArr2[i][i3 + 13];
            fArr2[i][i3 + 13] = this.L[31];
            fArr[i3 + 14] = this.L[14] + fArr2[i][i3 + 14];
            fArr2[i][i3 + 14] = this.L[32];
            fArr[i3 + 15] = this.L[15] + fArr2[i][i3 + 15];
            fArr2[i][i3 + 15] = this.L[33];
            fArr[i3 + 16] = this.L[16] + fArr2[i][i3 + 16];
            fArr2[i][i3 + 16] = this.L[34];
            fArr[i3 + 17] = this.L[17] + fArr2[i][i3 + 17];
            fArr2[i][i3 + 17] = this.L[35];
            i3 += 18;
        }
    }

    private static void a(float[] fArr, float[] fArr2, int i) {
        if (i == 2) {
            fArr2[0] = 0.0f;
            fArr2[1] = 0.0f;
            fArr2[2] = 0.0f;
            fArr2[3] = 0.0f;
            fArr2[4] = 0.0f;
            fArr2[5] = 0.0f;
            fArr2[6] = 0.0f;
            fArr2[7] = 0.0f;
            fArr2[8] = 0.0f;
            fArr2[9] = 0.0f;
            fArr2[10] = 0.0f;
            fArr2[11] = 0.0f;
            fArr2[12] = 0.0f;
            fArr2[13] = 0.0f;
            fArr2[14] = 0.0f;
            fArr2[15] = 0.0f;
            fArr2[16] = 0.0f;
            fArr2[17] = 0.0f;
            fArr2[18] = 0.0f;
            fArr2[19] = 0.0f;
            fArr2[20] = 0.0f;
            fArr2[21] = 0.0f;
            fArr2[22] = 0.0f;
            fArr2[23] = 0.0f;
            fArr2[24] = 0.0f;
            fArr2[25] = 0.0f;
            fArr2[26] = 0.0f;
            fArr2[27] = 0.0f;
            fArr2[28] = 0.0f;
            fArr2[29] = 0.0f;
            fArr2[30] = 0.0f;
            fArr2[31] = 0.0f;
            fArr2[32] = 0.0f;
            fArr2[33] = 0.0f;
            fArr2[34] = 0.0f;
            fArr2[35] = 0.0f;
            int i2 = 0;
            for (int i3 = 0; i3 < 3; i3++) {
                int i4 = i3 + 15;
                fArr[i4] = fArr[i4] + fArr[i3 + 12];
                int i5 = i3 + 12;
                fArr[i5] = fArr[i5] + fArr[i3 + 9];
                int i6 = i3 + 9;
                fArr[i6] = fArr[i6] + fArr[i3 + 6];
                int i7 = i3 + 6;
                fArr[i7] = fArr[i7] + fArr[i3 + 3];
                int i8 = i3 + 3;
                fArr[i8] = fArr[i8] + fArr[i3 + 0];
                int i9 = i3 + 15;
                fArr[i9] = fArr[i9] + fArr[i3 + 9];
                int i10 = i3 + 9;
                fArr[i10] = fArr[i10] + fArr[i3 + 3];
                float f2 = fArr[i3 + 12] * 0.5f;
                float f3 = fArr[i3 + 6] * 0.8660254f;
                float f4 = fArr[i3 + 0] + f2;
                float f5 = fArr[i3 + 0] - fArr[i3 + 12];
                float f6 = f4 + f3;
                float f7 = f4 - f3;
                float f8 = fArr[i3 + 15] * 0.5f;
                float f9 = fArr[i3 + 9] * 0.8660254f;
                float f10 = fArr[i3 + 3] + f8;
                float f11 = f10 + f9;
                float f12 = (f10 - f9) * 1.9318516f;
                float f13 = (fArr[i3 + 3] - fArr[i3 + 15]) * 0.70710677f;
                float f14 = f11 * 0.5176381f;
                float f15 = f6 + f14;
                float f16 = f6 - f14;
                float f17 = f5 + f13;
                float f18 = f5 - f13;
                float f19 = f7 + f12;
                float f20 = f7 - f12;
                float f21 = f15 * 0.5043145f;
                float f22 = f17 * 0.5411961f;
                float f23 = f19 * 0.6302362f;
                float f24 = f20 * 0.8213398f;
                float f25 = f18 * 1.306563f;
                float f26 = f16 * 3.830649f;
                float f27 = (-f21) * 0.7933533f;
                float f28 = (-f21) * 0.6087614f;
                float f29 = (-f22) * 0.9238795f;
                float f30 = (-f22) * 0.38268343f;
                float f31 = (-f23) * 0.9914449f;
                float f32 = (-f23) * 0.13052619f;
                float f33 = f25 * 0.38268343f;
                float f34 = f26 * 0.6087614f;
                float f35 = (-f26) * 0.7933533f;
                int i11 = i2 + 6;
                fArr2[i11] = fArr2[i11] + (f24 * 0.13052619f);
                int i12 = i2 + 7;
                fArr2[i12] = fArr2[i12] + f33;
                int i13 = i2 + 8;
                fArr2[i13] = fArr2[i13] + f34;
                int i14 = i2 + 9;
                fArr2[i14] = fArr2[i14] + f35;
                int i15 = i2 + 10;
                fArr2[i15] = fArr2[i15] + ((-f25) * 0.9238795f);
                int i16 = i2 + 11;
                fArr2[i16] = fArr2[i16] + ((-f24) * 0.9914449f);
                int i17 = i2 + 12;
                fArr2[i17] = fArr2[i17] + f31;
                int i18 = i2 + 13;
                fArr2[i18] = fArr2[i18] + f29;
                int i19 = i2 + 14;
                fArr2[i19] = fArr2[i19] + f27;
                int i20 = i2 + 15;
                fArr2[i20] = fArr2[i20] + f28;
                int i21 = i2 + 16;
                fArr2[i21] = fArr2[i21] + f30;
                int i22 = i2 + 17;
                fArr2[i22] = fArr2[i22] + f32;
                i2 += 6;
            }
            return;
        }
        fArr[17] = fArr[17] + fArr[16];
        fArr[16] = fArr[16] + fArr[15];
        fArr[15] = fArr[15] + fArr[14];
        fArr[14] = fArr[14] + fArr[13];
        fArr[13] = fArr[13] + fArr[12];
        fArr[12] = fArr[12] + fArr[11];
        fArr[11] = fArr[11] + fArr[10];
        fArr[10] = fArr[10] + fArr[9];
        fArr[9] = fArr[9] + fArr[8];
        fArr[8] = fArr[8] + fArr[7];
        fArr[7] = fArr[7] + fArr[6];
        fArr[6] = fArr[6] + fArr[5];
        fArr[5] = fArr[5] + fArr[4];
        fArr[4] = fArr[4] + fArr[3];
        fArr[3] = fArr[3] + fArr[2];
        fArr[2] = fArr[2] + fArr[1];
        fArr[1] = fArr[1] + fArr[0];
        fArr[17] = fArr[17] + fArr[15];
        fArr[15] = fArr[15] + fArr[13];
        fArr[13] = fArr[13] + fArr[11];
        fArr[11] = fArr[11] + fArr[9];
        fArr[9] = fArr[9] + fArr[7];
        fArr[7] = fArr[7] + fArr[5];
        fArr[5] = fArr[5] + fArr[3];
        fArr[3] = fArr[3] + fArr[1];
        float f36 = fArr[0] + fArr[0];
        float f37 = f36 + fArr[12];
        float f38 = f37 + (fArr[4] * 1.8793852f) + (fArr[8] * 1.5320889f) + (fArr[16] * 0.34729636f);
        float f39 = ((((f36 + fArr[4]) - fArr[8]) - fArr[12]) - fArr[12]) - fArr[16];
        float f40 = ((f37 - (fArr[4] * 0.34729636f)) - (fArr[8] * 1.8793852f)) + (fArr[16] * 1.5320889f);
        float f41 = ((f37 - (fArr[4] * 1.5320889f)) + (fArr[8] * 0.34729636f)) - (fArr[16] * 1.8793852f);
        float f42 = (((fArr[0] - fArr[4]) + fArr[8]) - fArr[12]) + fArr[16];
        float f43 = fArr[6] * 1.7320508f;
        float f44 = (fArr[2] * 1.9696155f) + f43 + (fArr[10] * 1.2855753f) + (fArr[14] * 0.6840403f);
        float f45 = ((fArr[2] - fArr[10]) - fArr[14]) * 1.7320508f;
        float f46 = (((fArr[2] * 1.2855753f) - f43) - (fArr[10] * 0.6840403f)) + (fArr[14] * 1.9696155f);
        float f47 = (((fArr[2] * 0.6840403f) - f43) + (fArr[10] * 1.9696155f)) - (fArr[14] * 1.2855753f);
        float f48 = fArr[1] + fArr[1];
        float f49 = f48 + fArr[13];
        float f50 = f49 + (fArr[5] * 1.8793852f) + (fArr[9] * 1.5320889f) + (fArr[17] * 0.34729636f);
        float f51 = ((((f48 + fArr[5]) - fArr[9]) - fArr[13]) - fArr[13]) - fArr[17];
        float f52 = ((f49 - (fArr[5] * 0.34729636f)) - (fArr[9] * 1.8793852f)) + (fArr[17] * 1.5320889f);
        float f53 = ((f49 - (fArr[5] * 1.5320889f)) + (fArr[9] * 0.34729636f)) - (fArr[17] * 1.8793852f);
        float f54 = ((((fArr[1] - fArr[5]) + fArr[9]) - fArr[13]) + fArr[17]) * 0.70710677f;
        float f55 = fArr[7] * 1.7320508f;
        float f56 = (fArr[3] * 1.9696155f) + f55 + (fArr[11] * 1.2855753f) + (fArr[15] * 0.6840403f);
        float f57 = ((fArr[3] - fArr[11]) - fArr[15]) * 1.7320508f;
        float f58 = (((fArr[3] * 1.2855753f) - f55) - (fArr[11] * 0.6840403f)) + (fArr[15] * 1.9696155f);
        float f59 = (((fArr[3] * 0.6840403f) - f55) + (fArr[11] * 1.9696155f)) - (fArr[15] * 1.2855753f);
        float f60 = f38 + f44;
        float f61 = (f50 + f56) * 0.5019099f;
        float f62 = f60 + f61;
        float f63 = f60 - f61;
        float f64 = f39 + f45;
        float f65 = (f51 + f57) * 0.5176381f;
        float f66 = f64 + f65;
        float f67 = f64 - f65;
        float f68 = f40 + f46;
        float f69 = (f52 + f58) * 0.55168897f;
        float f70 = f68 + f69;
        float f71 = f68 - f69;
        float f72 = f41 + f47;
        float f73 = (f53 + f59) * 0.61038727f;
        float f74 = f72 + f73;
        float f75 = f72 - f73;
        float f76 = f42 + f54;
        float f77 = f42 - f54;
        float f78 = f41 - f47;
        float f79 = (f53 - f59) * 0.8717234f;
        float f80 = f78 + f79;
        float f81 = f78 - f79;
        float f82 = f40 - f46;
        float f83 = (f52 - f58) * 1.1831008f;
        float f84 = f82 + f83;
        float f85 = f82 - f83;
        float f86 = f39 - f45;
        float f87 = (f51 - f57) * 1.9318516f;
        float f88 = f86 + f87;
        float f89 = f86 - f87;
        float f90 = f38 - f44;
        float f91 = (f50 - f56) * 5.7368565f;
        float f92 = f90 + f91;
        float f93 = f90 - f91;
        float[] fArr3 = X[i];
        fArr2[0] = (-f93) * fArr3[0];
        fArr2[1] = (-f89) * fArr3[1];
        fArr2[2] = (-f85) * fArr3[2];
        fArr2[3] = (-f81) * fArr3[3];
        fArr2[4] = (-f77) * fArr3[4];
        fArr2[5] = (-f75) * fArr3[5];
        fArr2[6] = (-f71) * fArr3[6];
        fArr2[7] = (-f67) * fArr3[7];
        fArr2[8] = (-f63) * fArr3[8];
        fArr2[9] = f63 * fArr3[9];
        fArr2[10] = f67 * fArr3[10];
        fArr2[11] = f71 * fArr3[11];
        fArr2[12] = f75 * fArr3[12];
        fArr2[13] = f77 * fArr3[13];
        fArr2[14] = f81 * fArr3[14];
        fArr2[15] = f85 * fArr3[15];
        fArr2[16] = f89 * fArr3[16];
        fArr2[17] = f93 * fArr3[17];
        fArr2[18] = f92 * fArr3[18];
        fArr2[19] = f88 * fArr3[19];
        fArr2[20] = f84 * fArr3[20];
        fArr2[21] = f80 * fArr3[21];
        fArr2[22] = f76 * fArr3[22];
        fArr2[23] = f74 * fArr3[23];
        fArr2[24] = f70 * fArr3[24];
        fArr2[25] = f66 * fArr3[25];
        fArr2[26] = f62 * fArr3[26];
        fArr2[27] = f62 * fArr3[27];
        fArr2[28] = f66 * fArr3[28];
        fArr2[29] = f70 * fArr3[29];
        fArr2[30] = f74 * fArr3[30];
        fArr2[31] = f76 * fArr3[31];
        fArr2[32] = f80 * fArr3[32];
        fArr2[33] = f84 * fArr3[33];
        fArr2[34] = f88 * fArr3[34];
        fArr2[35] = f92 * fArr3[35];
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    /* compiled from: LayerIIIDecoder.java */
    /* loaded from: naruto-game.jar:a/a/a/k$b.class */
    public static class b {

        /* renamed from: a  reason: collision with root package name */
        public int[] f24a;

        /* renamed from: b  reason: collision with root package name */
        public int[] f25b;

        public b() {
            this.f24a = new int[23];
            this.f25b = new int[14];
        }

        public b(int[] iArr, int[] iArr2) {
            this.f24a = iArr;
            this.f25b = iArr2;
        }
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    /* compiled from: LayerIIIDecoder.java */
    /* loaded from: naruto-game.jar:a/a/a/k$e.class */
    public static class e {

        /* renamed from: a  reason: collision with root package name */
        public int[] f30a = new int[4];

        /* renamed from: b  reason: collision with root package name */
        public d[] f31b = new d[2];

        public e() {
            this.f31b[0] = new d();
            this.f31b[1] = new d();
        }
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    /* compiled from: LayerIIIDecoder.java */
    /* loaded from: naruto-game.jar:a/a/a/k$a.class */
    public static class a {

        /* renamed from: a  reason: collision with root package name */
        public int f22a = 0;

        /* renamed from: b  reason: collision with root package name */
        public int f23b = 0;
        public e[] c = new e[2];

        public a() {
            this.c[0] = new e();
            this.c[1] = new e();
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v1, types: [int[], int[][]] */
    /* JADX WARN: Type inference failed for: r0v12, types: [float[], float[][]] */
    /* JADX WARN: Type inference failed for: r0v20, types: [float[], float[][]] */
    /* JADX WARN: Type inference failed for: r0v22, types: [int[][], int[][][]] */
    static {
        float[] fArr = new float[8192];
        for (int i = 0; i < 8192; i++) {
            int i2 = i;
            fArr[i2] = (float) Math.pow(i2, 1.3333333333333333d);
        }
        R = fArr;
        S = new float[]{new float[]{1.0f, 0.8408964f, 0.70710677f, 0.59460354f, 0.5f, 0.4204482f, 0.35355338f, 0.29730177f, 0.25f, 0.2102241f, 0.17677669f, 0.14865088f, 0.125f, 0.10511205f, 0.088388346f, 0.07432544f, 0.0625f, 0.052556027f, 0.044194173f, 0.03716272f, 0.03125f, 0.026278013f, 0.022097087f, 0.01858136f, 0.015625f, 0.013139007f, 0.011048543f, 0.00929068f, 0.0078125f, 0.0065695033f, 0.0055242716f, 0.00464534f}, new float[]{1.0f, 0.70710677f, 0.5f, 0.35355338f, 0.25f, 0.17677669f, 0.125f, 0.088388346f, 0.0625f, 0.044194173f, 0.03125f, 0.022097087f, 0.015625f, 0.011048543f, 0.0078125f, 0.0055242716f, 0.00390625f, 0.0027621358f, 0.001953125f, 0.0013810679f, 9.765625E-4f, 6.9053395E-4f, 4.8828125E-4f, 3.4526698E-4f, 2.4414062E-4f, 1.7263349E-4f, 1.2207031E-4f, 8.6316744E-5f, 6.1035156E-5f, 4.3158372E-5f, 3.0517578E-5f, 2.1579186E-5f}};
        T = new float[]{0.0f, 0.2679492f, 0.57735026f, 1.0f, 1.7320508f, 3.732051f, 1.0E11f, -3.732051f, -1.7320508f, -1.0f, -0.57735026f, -0.2679492f, 0.0f, 0.2679492f, 0.57735026f, 1.0f};
        V = new float[]{0.8574929f, 0.881742f, 0.94962865f, 0.9833146f, 0.9955178f, 0.9991606f, 0.9998992f, 0.99999315f};
        W = new float[]{-0.51449573f, -0.47173196f, -0.31337744f, -0.1819132f, -0.09457419f, -0.040965583f, -0.014198569f, -0.0036999746f};
        X = new float[]{new float[]{-0.016141215f, -0.05360318f, -0.100707136f, -0.16280818f, -0.5f, -0.38388735f, -0.6206114f, -1.1659756f, -3.8720753f, -4.225629f, -1.519529f, -0.97416484f, -0.73744076f, -1.2071068f, -0.5163616f, -0.45426053f, -0.40715656f, -0.3696946f, -0.3387627f, -0.31242222f, -0.28939587f, -0.26880082f, -0.5f, -0.23251417f, -0.21596715f, -0.20004979f, -0.18449493f, -0.16905846f, -0.15350361f, -0.13758625f, -0.12103922f, -0.20710678f, -0.084752575f, -0.06415752f, -0.041131172f, -0.014790705f}, new float[]{-0.016141215f, -0.05360318f, -0.100707136f, -0.16280818f, -0.5f, -0.38388735f, -0.6206114f, -1.1659756f, -3.8720753f, -4.225629f, -1.519529f, -0.97416484f, -0.73744076f, -1.2071068f, -0.5163616f, -0.45426053f, -0.40715656f, -0.3696946f, -0.33908543f, -0.3151181f, -0.29642227f, -0.28184548f, -0.5411961f, -0.2621323f, -0.25387916f, -0.2329629f, -0.19852729f, -0.15233535f, -0.0964964f, -0.03342383f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f}, new float[]{-0.0483008f, -0.15715657f, -0.28325045f, -0.42953748f, -1.2071068f, -0.8242648f, -1.1451749f, -1.769529f, -4.5470223f, -3.489053f, -0.7329629f, -0.15076515f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f}, new float[]{0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, -0.15076514f, -0.7329629f, -3.489053f, -4.5470223f, -1.769529f, -1.1451749f, -0.8313774f, -1.306563f, -0.54142016f, -0.46528974f, -0.4106699f, -0.3700468f, -0.3387627f, -0.31242222f, -0.28939587f, -0.26880082f, -0.5f, -0.23251417f, -0.21596715f, -0.20004979f, -0.18449493f, -0.16905846f, -0.15350361f, -0.13758625f, -0.12103922f, -0.20710678f, -0.084752575f, -0.06415752f, -0.041131172f, -0.014790705f}};
        Z = new int[][]{new int[]{new int[]{6, 5, 5, 5}, new int[]{9, 9, 9, 9}, new int[]{6, 9, 9, 9}}, new int[]{new int[]{6, 5, 7, 3}, new int[]{9, 9, 12, 6}, new int[]{6, 9, 12, 6}}, new int[]{new int[]{11, 10, 0, 0}, new int[]{18, 18, 0, 0}, new int[]{15, 18, 0, 0}}, new int[]{new int[]{7, 7, 7, 0}, new int[]{12, 12, 12, 0}, new int[]{6, 15, 12, 0}}, new int[]{new int[]{6, 6, 6, 3}, new int[]{12, 9, 9, 6}, new int[]{6, 12, 9, 6}}, new int[]{new int[]{8, 8, 5, 0}, new int[]{15, 12, 9, 0}, new int[]{6, 18, 9, 0}}};
    }

    private static int[] a(int[] iArr) {
        int i = 0;
        int[] iArr2 = new int[576];
        for (int i2 = 0; i2 < 13; i2++) {
            int i3 = iArr[i2];
            int i4 = iArr[i2 + 1];
            for (int i5 = 0; i5 < 3; i5++) {
                for (int i6 = i3; i6 < i4; i6++) {
                    int i7 = i;
                    i++;
                    iArr2[(3 * i6) + i5] = i7;
                }
            }
        }
        return iArr2;
    }

    /* compiled from: LayerIIIDecoder.java */
    /* loaded from: naruto-game.jar:a/a/a/k$c.class */
    class c {

        /* renamed from: a  reason: collision with root package name */
        private int[] f26a;

        /* renamed from: b  reason: collision with root package name */
        private int[] f27b;

        public c(int[] iArr, int[] iArr2) {
            this.f26a = iArr;
            this.f27b = iArr2;
        }
    }
}