package com.donglh.narutoninjasaga.e;

import java.util.Vector;
/* compiled from: Task.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/e/av.class */
public final class av {
    public int a;
    public String b;
    public int c;
    public int d;
    public int e;
    public int f;
    public int g;
    public String h;
    public String i;
    public String j;
    public int k;
    public int l;
    public int m;
    public int n;
    public String o;
    public Vector p = new Vector();

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x0145: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:36:0x0144 */
    public final com.donglh.narutoninjasaga.d.q a() {
        Exception a;
        try {
            if (this.o != null && this.o.length() > 0) {
                String[] split = this.o.split("@");
                com.donglh.narutoninjasaga.d.q qVar = new com.donglh.narutoninjasaga.d.q();
                qVar.c = Short.parseShort(split[0]);
                qVar.q = Boolean.parseBoolean(split[1]);
                qVar.g = Long.parseLong(split[2]);
                qVar.b(Integer.parseInt(split[3]));
                qVar.f = Byte.parseByte(split[4]);
                qVar.p = Byte.parseByte(split[5]);
                if (split.length > 6) {
                    qVar.h = split[6];
                    if (qVar.h().e != 2 && qVar.h().e != com.donglh.narutoninjasaga.d.d.a().G) {
                        int i = 0;
                        while (true) {
                            if (i >= f.c().U.length) {
                                break;
                            }
                            p pVar = f.c().U[i];
                            if (pVar.e != com.donglh.narutoninjasaga.d.d.a().G || pVar.f != qVar.h().f || pVar.i != qVar.h().i) {
                                i++;
                            } else {
                                qVar.c = pVar.a;
                                break;
                            }
                        }
                    }
                    if (qVar.h().f == 13) {
                        qVar.f = com.donglh.narutoninjasaga.d.d.a().I;
                        switch (qVar.f) {
                            case 1:
                                qVar.h = "54,0,500;62,0,500";
                                break;
                            case 2:
                                qVar.h = "55,0,500;58,0,500";
                                break;
                            case 3:
                                qVar.h = "56,0,500;59,0,500";
                                break;
                            case 4:
                                qVar.h = "57,0,500;60,0,500";
                                break;
                            case 5:
                                qVar.h = "53,0,500;61,0,500";
                                break;
                        }
                    }
                }
                return qVar;
            }
            return null;
        } catch (Exception e) {
            aw.a(a);
            return null;
        }
    }

    public final boolean b() {
        return n.n().aD >= this.p.size();
    }
}
