package com.donglh.narutoninjasaga.e;
/* compiled from: ItemOptionTemplate.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/e/o.class */
public final class o {
    public short a;
    public String b;
    public byte c;
    public byte d;
    public String e;

    public o(int i) {
        this.a = (short) i;
    }

    public final int[] a() {
        if (this.e.length() == 0) {
            return new int[0];
        }
        String[] split = this.e.split(";");
        int length = split.length;
        int i = 17;
        i = (this.a == 207 || this.a == 208) ? 19 : 19;
        if (this.c == 8) {
            i = length;
        }
        if (length > i) {
            length = i;
        }
        int[] iArr = new int[length];
        for (int i2 = 0; i2 < iArr.length; i2++) {
            iArr[i2] = Integer.parseInt(split[i2]);
        }
        return iArr;
    }
}
