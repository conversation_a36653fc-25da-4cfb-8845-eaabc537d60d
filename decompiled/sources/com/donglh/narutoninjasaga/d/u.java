package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.Input;
import java.util.Vector;
/* compiled from: LangLa_a1.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/u.class */
public final class u extends he {
    public q[] a;
    private fx[] e;
    private dg f;
    public int b;
    private int g;
    public boolean c;
    public gt d;

    public u(com.donglh.narutoninjasaga.e.ai aiVar) {
        super(aiVar, new String[]{com.donglh.narutoninjasaga.c.a.ba, com.donglh.narutoninjasaga.c.a.F});
        this.a = new q[3];
        this.e = new fx[3];
        this.e[0] = new fx((byte) 1, 94, a_() + 20, 30, 30, 30, 1, 1);
        this.e[1] = new fx((byte) 1, 174, a_() + 20, 30, 30, 30, 1, 1);
        this.e[2] = new fx((byte) 1, 134, a_() + 65, 30, 30, 30, 1, 1);
        this.f = a(this.aM - 70, this.aN - 33, com.donglh.narutoninjasaga.c.a.ba, this, 0, -8);
        a(this.f, 0);
    }

    @Override // com.donglh.narutoninjasaga.d.et
    public final void a() {
        this.A = new q[d.a().W.length];
        int i = 0;
        for (int i2 = 0; i2 < d.a().W.length; i2++) {
            if (d.a().W[i2] != null && d.a().W[i2].B()) {
                this.A[i] = d.a().W[i2];
                i++;
            }
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void b() {
        try {
            super.b();
            if (this.i.b == 0) {
                if (this.a[0] != null) {
                    this.a[1] = this.a[0].a();
                    this.a[1].q = true;
                    int i = this.a[1].p + 1;
                    int i2 = i;
                    if (i > 19) {
                        i2 = 19;
                    }
                    this.a[1].a(i2);
                } else if (!this.c) {
                    this.a[1] = null;
                }
                for (int i3 = 0; i3 < this.e.length; i3++) {
                    this.e[i3].a();
                }
            } else {
                a();
            }
            if (this.d != null) {
                this.d.b();
                if (this.d.h()) {
                    this.d = null;
                }
            }
            if (this.g > 0) {
                this.g--;
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) this);
        }
    }

    /* JADX WARN: Type inference failed for: r0v28, types: [com.donglh.narutoninjasaga.d.gt, java.lang.Exception] */
    @Override // com.donglh.narutoninjasaga.d.he, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        ?? r0;
        try {
            if (q() <= 0) {
                a(lVar, (int) this.aY, (int) this.aZ, (int) this.aM, (int) this.aN, com.donglh.narutoninjasaga.c.a.o, (byte) 2, false);
            } else {
                super.a(lVar);
            }
            if (this.i.b == 0) {
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.aW, 4 + ((this.aM - 8) / 2), a_() + 9, 2, -10831436, -16777216);
                a(lVar, this.e[0].aY, this.e[0].aZ, this.a[0], this.e[0].i >= 0, com.donglh.narutoninjasaga.c.a.rK[3]);
                a(lVar, this.e[1].aY, this.e[1].aZ, this.a[1], this.e[1].i >= 0, "Xem Thử");
                a(lVar, this.e[2].aY, this.e[2].aZ, this.a[2], this.e[2].i >= 0, "Mảnh");
                com.donglh.narutoninjasaga.e.r.a(lVar, 95, 0, 142 - ((com.donglh.narutoninjasaga.e.f.c().i / 3) % 3), a_() + 25, 20);
                if (this.a[0] != null) {
                    kk.b(kk.c, lVar, "(+" + ((int) this.a[0].p) + ")", 101, a_() + 60, 33, -1, -16777216);
                }
                if (this.a[1] != null) {
                    kk.b(kk.c, lVar, "(+" + ((int) this.a[1].p) + ")", 181, a_() + 60, 33, -1, -16777216);
                }
                if (this.c) {
                    kk.c(kk.c, lVar, com.donglh.narutoninjasaga.c.a.jO, 24, a_() + Input.Keys.BUTTON_START, 0, -2560, -16777216);
                    kk.c(kk.c, lVar, com.donglh.narutoninjasaga.c.a.eh, 24, a_() + 124, 0, -2560, -16777216);
                } else {
                    kk.c(kk.c, lVar, com.donglh.narutoninjasaga.c.a.ej, 24, a_() + Input.Keys.BUTTON_START, 0, -1, -16777216);
                    kk.c(kk.c, lVar, com.donglh.narutoninjasaga.c.a.eo, 24, a_() + 124, 0, -1, -16777216);
                }
                if (this.d != null) {
                    r0 = this.d;
                    r0.b(lVar, this.e[1].aY + (this.e[1].f / 2), this.e[1].aZ + (this.e[1].f / 2));
                }
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
        }
    }

    @Override // com.donglh.narutoninjasaga.d.he, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        Vector c = super.c();
        if (this.i.b == 0) {
            for (int i = 0; i < this.e.length; i++) {
                c.addElement(this.e[i].a(i + 1001, this));
            }
        }
        return c;
    }

    @Override // com.donglh.narutoninjasaga.d.he, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        if (this.i.b == q() && guVar.b == 0) {
            d();
        }
        super.a(guVar, i, i2);
        if (this.i.b == 0) {
            switch (guVar.b) {
                case 0:
                    e();
                    return;
                case 1001:
                    this.b = 1;
                    a(guVar.j, guVar.j.i);
                    if (this.a[0] == null) {
                        this.n = b(com.donglh.narutoninjasaga.c.a.es, this, guVar.j.aY + 32, guVar.j.aZ, 100);
                        return;
                    } else {
                        this.n = a(guVar, this, this.a[0]);
                        return;
                    }
                case 1002:
                    this.b = 2;
                    a(guVar.j, guVar.j.i);
                    if (this.a[1] == null) {
                        this.n = b(com.donglh.narutoninjasaga.c.a.ev, this, guVar.j.aY + 32, guVar.j.aZ, 100);
                        return;
                    } else {
                        this.n = a(guVar, this, this.a[1]);
                        return;
                    }
                case 1003:
                    this.b = 3;
                    a(guVar.j, guVar.j.i);
                    if (this.a[2] == null) {
                        this.n = b(com.donglh.narutoninjasaga.c.a.ey, this, guVar.j.aY + 32, guVar.j.aZ, 100);
                        return;
                    } else {
                        this.n = a(guVar, this, this.a[2]);
                        return;
                    }
                case 2001:
                    e();
                    return;
                default:
                    return;
            }
        }
    }

    private void a(fw fwVar, int i) {
        for (int i2 = 0; i2 < this.e.length; i2++) {
            this.e[i2].i = -1;
        }
        fwVar.i = i;
    }

    @Override // com.donglh.narutoninjasaga.d.el
    public final void a(int i) {
        super.a(i);
        if (this.c) {
            this.c = false;
            for (int i2 = 0; i2 < this.a.length; i2++) {
                this.a[i2] = null;
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.ej
    public final void d() {
        super.d();
        for (int i = 0; i < this.a.length; i++) {
            if (i != 1) {
                if (this.a[i] != null) {
                    d.a().g(this.a[i].r)[this.a[i].e] = this.a[i];
                }
            }
            this.a[i] = null;
        }
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x0082: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:15:0x0081 */
    private void e() {
        Exception a;
        try {
            if (this.c) {
                com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.eA, -2560);
            } else if (this.a[0] != null && this.a[1] != null && this.a[2] != null) {
                com.donglh.narutoninjasaga.e.ak akVar = new com.donglh.narutoninjasaga.e.ak((byte) -104);
                akVar.a(0);
                akVar.a(this.a[0].r);
                akVar.b(this.a[0].e);
                akVar.b(this.a[2].e);
                akVar.l();
            } else {
                com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.eC, -65536);
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a);
        }
    }
}
