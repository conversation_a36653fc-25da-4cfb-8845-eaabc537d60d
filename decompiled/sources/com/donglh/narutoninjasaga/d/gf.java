package com.donglh.narutoninjasaga.d;
/* compiled from: LangLa_iz.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/gf.class */
public final class gf {
    private int a = -1;
    private int b;
    private com.donglh.narutoninjasaga.e.aa c;
    private com.donglh.narutoninjasaga.e.ar d;
    private d e;
    private j f;
    private boolean g;

    /* JADX WARN: Multi-variable type inference failed */
    public gf(com.donglh.narutoninjasaga.e.ar arVar, d dVar, j jVar) {
        this.d = arVar;
        this.c = (com.donglh.narutoninjasaga.e.aa) com.donglh.narutoninjasaga.e.f.c().J.get(Short.valueOf(arVar.d));
        this.e = dVar;
        this.f = jVar;
        if (dVar.z() != null) {
            this.b = 1;
        } else {
            this.b = 0;
        }
        try {
            if (dVar.equals(d.a())) {
                try {
                    if (!dVar.c()) {
                        return;
                    }
                    dVar.F();
                    if (jVar == null || !(jVar instanceof d)) {
                        com.donglh.narutoninjasaga.e.ak akVar = new com.donglh.narutoninjasaga.e.ak((byte) 61);
                        akVar.b(arVar.d);
                        if (jVar != null) {
                            akVar.b(jVar.aE);
                        }
                        akVar.l();
                        return;
                    }
                    com.donglh.narutoninjasaga.e.ak akVar2 = new com.donglh.narutoninjasaga.e.ak((byte) 20);
                    akVar2.b(arVar.d);
                    akVar2.c(jVar.aE);
                    akVar2.l();
                } catch (Exception e) {
                    com.donglh.narutoninjasaga.e.aw.a((Exception) dVar);
                }
            }
        } catch (Exception unused) {
        }
    }

    public final void a() {
        int i;
        int i2;
        gt gtVar;
        try {
            this.a++;
            if (this.a >= this.c.d[this.b].length) {
                if (this.d.d == 39) {
                    com.donglh.narutoninjasaga.e.n.n().y();
                }
                this.e.ad = null;
                if (this.e.c()) {
                    this.e.y();
                } else {
                    this.e.aG = (byte) 0;
                }
                if (!this.g && this.f != null && this.c.b > 0) {
                    gt gtVar2 = new gt(this.c.b, this.f.aY, this.f.aZ);
                    if (this.c.c == 1) {
                        gtVar2 = new gt(this.c.b, this.f.P(), this.f.Q());
                    }
                    com.donglh.narutoninjasaga.e.n.n().L.add(gtVar2);
                }
                if (this.f != null && (this.f instanceof jq)) {
                    jq jqVar = (jq) this.f;
                    this.f.a(this.e);
                    if (this.f.aY < this.e.aY) {
                        jqVar.F = -2;
                    } else {
                        jqVar.F = 2;
                    }
                    if (this.f.aZ < this.e.aZ) {
                        jqVar.G = -1;
                    } else {
                        jqVar.G = 1;
                    }
                    if (this.f.aG != 3) {
                        this.f.G();
                        return;
                    }
                    return;
                }
                return;
            }
            this.e.c = this.c.d[this.b][this.a].a;
            if (this.c.d[this.b][this.a].c > 0) {
                if (this.e.P == 3) {
                    gt gtVar3 = new gt(this.c.d[this.b][this.a].c, this.e.aY - this.c.d[this.b][this.a].d, this.e.aZ + this.c.d[this.b][this.a].e);
                    gtVar = gtVar3;
                    gtVar3.d = true;
                } else {
                    gtVar = new gt(this.c.d[this.b][this.a].c, this.e.aY + this.c.d[this.b][this.a].d, this.e.aZ + this.c.d[this.b][this.a].e);
                }
                com.donglh.narutoninjasaga.e.n.n().L.add(gtVar);
            }
            if (this.c.d[this.b][this.a].f > 0 && this.f != null) {
                this.g = true;
                if (this.e.P == 3) {
                    i = this.e.aY + this.c.d[this.b][this.a].g;
                    i2 = this.e.aZ + this.c.d[this.b][this.a].h;
                } else {
                    i = this.e.aY - this.c.d[this.b][this.a].g;
                    i2 = this.e.aZ + this.c.d[this.b][this.a].h;
                }
                int i3 = com.donglh.narutoninjasaga.e.f.c().o / 2;
                com.donglh.narutoninjasaga.e.n.n().J.add(new fj(this.c.d[this.b][this.a].f, i, i2, this.f, this.c.b, this.c.c));
                if (new fj(this.c.d[this.b][this.a].f, i, i2, this.f, this.c.b, this.c.c).a().a == 5) {
                    com.donglh.narutoninjasaga.e.n.n().J.add(new fj(this.c.d[this.b][this.a].f, this.f.aY + i3, this.f.aZ, new fl(this.f.aY, this.f.aZ), this.c.b, this.c.c));
                    com.donglh.narutoninjasaga.e.n.n().J.add(new fj(this.c.d[this.b][this.a].f, this.f.aY + i3, this.f.aZ + i3, new fl(this.f.aY, this.f.aZ), this.c.b, this.c.c));
                    com.donglh.narutoninjasaga.e.n.n().J.add(new fj(this.c.d[this.b][this.a].f, this.f.aY + i3, this.f.aZ - i3, new fl(this.f.aY, this.f.aZ), this.c.b, this.c.c));
                    com.donglh.narutoninjasaga.e.n.n().J.add(new fj(this.c.d[this.b][this.a].f, this.f.aY - i3, this.f.aZ, new fl(this.f.aY, this.f.aZ), this.c.b, this.c.c));
                    com.donglh.narutoninjasaga.e.n.n().J.add(new fj(this.c.d[this.b][this.a].f, this.f.aY - i3, this.f.aZ + i3, new fl(this.f.aY, this.f.aZ), this.c.b, this.c.c));
                    com.donglh.narutoninjasaga.e.n.n().J.add(new fj(this.c.d[this.b][this.a].f, this.f.aY - i3, this.f.aZ - i3, new fl(this.f.aY, this.f.aZ), this.c.b, this.c.c));
                    com.donglh.narutoninjasaga.e.n.n().J.add(new fj(this.c.d[this.b][this.a].f, this.f.aY, this.f.aZ + i3, new fl(this.f.aY, this.f.aZ), this.c.b, this.c.c));
                    com.donglh.narutoninjasaga.e.n.n().J.add(new fj(this.c.d[this.b][this.a].f, this.f.aY, this.f.aZ - i3, new fl(this.f.aY, this.f.aZ), this.c.b, this.c.c));
                }
            }
        } catch (Exception unused) {
            this.e.ad = null;
            this.e.y();
        }
    }
}
