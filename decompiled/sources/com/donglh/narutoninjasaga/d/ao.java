package com.donglh.narutoninjasaga.d;

import java.util.Vector;
/* compiled from: LangLa_at.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/ao.class */
public final class ao extends he {
    public q a;
    public q[] b;
    private fx[] e;
    private dg f;
    public int c;
    private int g;
    public gt d;

    public ao(com.donglh.narutoninjasaga.e.ai aiVar) {
        super(aiVar, new String[]{com.donglh.narutoninjasaga.c.a.bN, com.donglh.narutoninjasaga.c.a.F});
        this.b = new q[2];
        this.e = new fx[2];
        this.g = 0;
        this.e[0] = new fx((byte) 1, 124, a_() + 20, 30, 30, 30, 1, 1);
        this.e[1] = new fx((byte) 1, 14, a_() + 20, 96, 96, 32, 1, 2);
        this.f = a(this.aM - 70, this.aN - 33, com.donglh.narutoninjasaga.c.a.jR, this, 0, -8);
        a(this.f, 0);
    }

    @Override // com.donglh.narutoninjasaga.d.el
    public final void a(int i) {
        super.a(i);
        if (this.a != null) {
            this.a = null;
        }
    }

    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void b() {
        super.b();
        if (this.i.b == 0 && this.a == null) {
            q qVar = null;
            int i = 0;
            for (int i2 = 0; i2 < this.b.length; i2++) {
                if (this.b[i2] != null) {
                    i++;
                    if (qVar == null || this.b[i2].h().i > qVar.h().i) {
                        qVar = this.b[i2];
                    }
                }
            }
            if (i >= 2 && com.donglh.narutoninjasaga.e.aw.b(this.b[0].h().i - this.b[1].h().i) == 20) {
                this.a = qVar.a();
                s[] N = this.a.N();
                Vector vector = new Vector();
                s sVar = null;
                for (int i3 = 0; i3 < N.length; i3++) {
                    if (N[i3].a[0] == 128) {
                        sVar = N[i3];
                    }
                    vector.add(N[i3]);
                }
                s sVar2 = N[N.length - 1];
                if (this.a.h().i == 35) {
                    if (sVar2.a().c == 3) {
                        vector.add(new s("144,50,50"));
                    } else {
                        vector.add(new s("143,15,15"));
                    }
                    if (sVar2.a().c != 4) {
                        s sVar3 = sVar;
                        sVar3.e(sVar3.f() + 1000);
                        this.a.h = q.a(vector);
                    }
                } else if (this.a.h().i == 49) {
                    if (sVar2.a().c == 4) {
                        vector.add(new s("145,10,10"));
                    } else if (sVar2.a().c == 3) {
                        vector.add(new s("144,80,80"));
                    } else {
                        vector.add(new s("143,25,25"));
                    }
                    if (sVar2.a().c != 5) {
                        s sVar4 = sVar;
                        sVar4.e(sVar4.f() + 1000);
                        this.a.h = q.a(vector);
                    }
                }
            }
        }
        if (this.d != null) {
            this.d.b();
            if (this.d.h()) {
                this.d = null;
            }
        }
        if (this.g > 0) {
            this.g--;
        }
    }

    @Override // com.donglh.narutoninjasaga.d.he, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        if (q() <= 0) {
            a(lVar, (int) this.aY, (int) this.aZ, (int) this.aM, (int) this.aN, com.donglh.narutoninjasaga.c.a.o, (byte) 2, false);
        } else {
            super.a(lVar);
        }
        if (this.i.b == 0) {
            kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.jS, 14, a_() + 9, 0, -10831436, -16777216);
            a(lVar, this.e[0].aY, this.e[0].aZ, this.a, this.e[0].i >= 0, com.donglh.narutoninjasaga.c.a.U);
            com.donglh.narutoninjasaga.e.r.a(lVar, 95, 0, 92 - ((com.donglh.narutoninjasaga.e.f.c().i / 3) % 3), a_() + 25, 20);
            if (this.g > 0 && this.g % 14 > 2) {
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.jT, 14, a_() + 125, 0, -2560, -16777216);
            }
            kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.jU, 14, a_() + 65, 0, -1, -16777216);
            kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.jV, 14, a_() + 80, 0, -1, -16777216);
            kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.jW, 14, a_() + 95, 0, -1, -16777216);
            a(lVar, this.e[1]);
            for (int i = 0; i < this.e[1].g; i++) {
                for (int i2 = 0; i2 < this.e[1].o; i2++) {
                    if (i2 % 2 == 0) {
                        b(lVar, i2 * this.e[1].f, i * this.e[1].f, this.b[(i * this.e[1].o) + i2], (i * this.e[1].g) + i2 == this.e[1].i, com.donglh.narutoninjasaga.c.a.rT[0]);
                    } else {
                        b(lVar, i2 * this.e[1].f, i * this.e[1].f, this.b[(i * this.e[1].o) + i2], (i * this.e[1].g) + i2 == this.e[1].i, com.donglh.narutoninjasaga.c.a.rT[1]);
                    }
                }
            }
            b(lVar);
            if (this.d != null) {
                this.d.b(lVar, this.e[0].aY + (this.e[0].f / 2), this.e[0].aZ + (this.e[0].f / 2));
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.he, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        Vector c = super.c();
        if (this.i.b == 0) {
            for (int i = 0; i < this.e.length; i++) {
                c.addElement(this.e[i].a(i + 1001, this));
            }
        }
        return c;
    }

    @Override // com.donglh.narutoninjasaga.d.he, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        if (this.i.b == q() && guVar.b == 0) {
            d();
        }
        super.a(guVar, i, i2);
        if (this.i.b == 0) {
            switch (guVar.b) {
                case 0:
                    a(true);
                    return;
                case 1001:
                    this.c = 0;
                    a(guVar.j, guVar.j.i);
                    this.n = a(guVar, this, this.a);
                    return;
                case 1002:
                    this.c = 1;
                    a(guVar.j, guVar.j.i);
                    this.n = a(guVar, this, this.b[guVar.j.i]);
                    return;
                case 2001:
                    a(false);
                    return;
                default:
                    return;
            }
        }
    }

    private void a(fw fwVar, int i) {
        for (int i2 = 0; i2 < this.e.length; i2++) {
            this.e[i2].i = -1;
        }
        fwVar.i = i;
    }

    @Override // com.donglh.narutoninjasaga.d.ej
    public final void d() {
        super.d();
        for (int i = 0; i < this.b.length; i++) {
            if (this.b[i] != null) {
                d.a().g(this.b[i].r)[this.b[i].e] = this.b[i];
                this.b[i] = null;
            }
        }
    }

    private void a(boolean z) {
        try {
            if (z) {
                com.donglh.narutoninjasaga.e.f.c().am.a(com.donglh.narutoninjasaga.c.a.jX, 2001, this);
                return;
            }
            com.donglh.narutoninjasaga.e.ak akVar = new com.donglh.narutoninjasaga.e.ak((byte) -96);
            int i = 0;
            for (int i2 = 0; i2 < this.b.length; i2++) {
                if (this.b[i2] != null) {
                    i++;
                }
            }
            akVar.a(i);
            for (int i3 = 0; i3 < this.b.length; i3++) {
                if (this.b[i3] != null) {
                    akVar.a(this.b[i3].r);
                    akVar.b(this.b[i3].e);
                }
            }
            akVar.l();
        } catch (Exception unused) {
        }
    }
}
