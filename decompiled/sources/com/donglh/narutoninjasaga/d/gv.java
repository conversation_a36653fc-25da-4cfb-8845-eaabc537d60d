package com.donglh.narutoninjasaga.d;

import java.util.Vector;
/* compiled from: LangLa_k.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/gv.class */
public final class gv extends em {
    private es a;
    private ae b;

    public gv(com.donglh.narutoninjasaga.e.ai aiVar, ae aeVar) {
        this.j = (byte) 2;
        this.s = aiVar;
        this.b = aeVar;
        this.u = false;
        d(180, 137);
        this.a = new es(10, 45, 100, "", this, 0);
        this.a.a = 9;
        a(this.a);
        a(26, this.aN - 35, com.donglh.narutoninjasaga.c.a.e, this, 1001, -8);
        a(this.aM - 86, this.aN - 35, com.donglh.narutoninjasaga.c.a.t, this, 1002, -8);
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a(int i, Object obj, co coVar) {
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a_(int i) {
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        a(lVar, 0, 0, this.aM, this.aN, 80, 55, 56);
        kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.bt, 10, 30, 0, -1, -16777216);
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void b() {
    }

    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        super.c();
        Vector vector = new Vector();
        vector.addElement(new gu(0, 0, 0, this.aM, this.aN, this.t, this));
        return vector;
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        boolean z;
        switch (guVar.b) {
            case 1001:
                if (this.a.j() <= 0) {
                    com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.fu, -65536);
                    z = false;
                } else if (this.a.j() > d.a().B) {
                    com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.fv, -65536);
                    z = false;
                } else {
                    d.a().B -= this.a.j();
                    this.b.c += this.a.j();
                    z = true;
                }
                if (!z) {
                    return;
                }
                break;
            case 1002:
                break;
            default:
                return;
        }
        p();
    }
}
