package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.graphics.Pixmap;
import java.util.Vector;
/* compiled from: LangLa_mg.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/iq.class */
public final class iq extends cl {
    private fw b;
    private hz[] c;
    private boolean e;
    private static com.donglh.narutoninjasaga.e.az f;
    private boolean g;
    public static int a;
    private Vector d = new Vector();
    private String h = "";
    private int i = -1;

    public iq(int i, int i2, cn cnVar) {
        g(i, i2);
        a_(90, 31);
        this.l = cnVar;
        this.b = new fw((byte) 1, 0, 0, 90, 54, 9, 0);
        e();
    }

    public final void a() {
        com.donglh.narutoninjasaga.e.ay.a().a(13, !com.donglh.narutoninjasaga.e.ay.a().a(13));
        db.a().h();
        e();
    }

    public final void c(boolean z) {
        com.donglh.narutoninjasaga.e.ay.a().a(13, z);
        e();
    }

    public final void e() {
        this.d.clear();
        this.i = -1;
        Vector vector = new Vector();
        if (!com.donglh.narutoninjasaga.e.ay.a().a(13)) {
            this.h = com.donglh.narutoninjasaga.c.a.iI;
            if (com.donglh.narutoninjasaga.e.n.n().aC < com.donglh.narutoninjasaga.e.f.c().Q.length) {
                com.donglh.narutoninjasaga.e.av avVar = com.donglh.narutoninjasaga.e.f.c().Q[com.donglh.narutoninjasaga.e.n.n().aC];
                vector.addElement("c#cyan" + this.h + "c#white" + avVar.b);
                com.donglh.narutoninjasaga.e.am amVar = com.donglh.narutoninjasaga.e.f.c().L[avVar.d];
                if (com.donglh.narutoninjasaga.e.n.n().aD < 0) {
                    vector.addElement("c#white" + com.donglh.narutoninjasaga.c.a.iJ + "c#task" + amVar.g + "c#white" + com.donglh.narutoninjasaga.c.a.iL);
                    this.d.addElement(new dp(avVar.d, -1, -1, avVar.e, avVar.f, avVar.g));
                } else if (d.a().i() < avVar.c) {
                    vector.addElement("c#red" + com.donglh.narutoninjasaga.c.a.iM + avVar.c);
                } else if (com.donglh.narutoninjasaga.e.n.n().aD >= avVar.p.size()) {
                    vector.addElement("c#white" + com.donglh.narutoninjasaga.c.a.iJ + "c#task" + amVar.g + "c#white" + com.donglh.narutoninjasaga.c.a.iP);
                    this.d.addElement(new dp(avVar.d, -1, -1, avVar.e, avVar.f, avVar.g));
                } else {
                    com.donglh.narutoninjasaga.e.au auVar = (com.donglh.narutoninjasaga.e.au) avVar.p.elementAt(com.donglh.narutoninjasaga.e.n.n().aD);
                    this.d.addElement(new dp(auVar.d, auVar.b(), auVar.e, auVar.f, auVar.g, auVar.h));
                    if (auVar.i <= 1) {
                        vector.addElement("c#blue- c#task" + auVar.b);
                    } else {
                        vector.addElement("c#blue- c#task" + auVar.b + " " + com.donglh.narutoninjasaga.e.n.n().aE + "/" + auVar.i);
                    }
                }
            } else {
                vector.addElement("c#cyan" + this.h + "c#white" + com.donglh.narutoninjasaga.c.a.iK);
            }
        } else {
            this.h = com.donglh.narutoninjasaga.c.a.lj;
            if (a == 0) {
                if (!a(vector, this.d, this.h) && !b(vector, this.d, this.h) && !c(vector, this.d, this.h)) {
                    vector.addElement("c#yellow" + this.h + "c#white" + com.donglh.narutoninjasaga.c.a.iR);
                }
            } else if (a == 1) {
                if (!b(vector, this.d, this.h) && !a(vector, this.d, this.h) && !c(vector, this.d, this.h)) {
                    vector.addElement("c#yellow" + this.h + "c#white" + com.donglh.narutoninjasaga.c.a.iR);
                }
            } else if (a == 2) {
                if (!c(vector, this.d, this.h) && !a(vector, this.d, this.h) && !b(vector, this.d, this.h)) {
                    vector.addElement("c#yellow" + this.h + "c#white" + com.donglh.narutoninjasaga.c.a.iR);
                }
            } else if (a == 3) {
                if (!d(vector, this.d, this.h) && !e(vector, this.d, this.h) && !f(vector, this.d, this.h) && !a(vector, this.d, this.h) && !b(vector, this.d, this.h) && !c(vector, this.d, this.h)) {
                    vector.addElement("c#yellow" + this.h + "c#white" + com.donglh.narutoninjasaga.c.a.iR);
                }
            } else if (a == 4) {
                if (!e(vector, this.d, this.h) && !f(vector, this.d, this.h) && !d(vector, this.d, this.h) && !a(vector, this.d, this.h) && !b(vector, this.d, this.h) && !c(vector, this.d, this.h)) {
                    vector.addElement("c#yellow" + this.h + "c#white" + com.donglh.narutoninjasaga.c.a.iR);
                }
            } else if (a == 5 && !f(vector, this.d, this.h) && !e(vector, this.d, this.h) && !d(vector, this.d, this.h) && !a(vector, this.d, this.h) && !b(vector, this.d, this.h) && !c(vector, this.d, this.h)) {
                vector.addElement("c#yellow" + this.h + "c#white" + com.donglh.narutoninjasaga.c.a.iR);
            }
        }
        Vector vector2 = new Vector();
        int i = -1;
        for (int i2 = 0; i2 < vector.size(); i2++) {
            String valueOf = String.valueOf(vector.elementAt(i2));
            if (valueOf.contains("c#task")) {
                i++;
            }
            Vector a2 = kk.a(kk.a, valueOf, this.b.aM - 8);
            int i3 = 0;
            for (int i4 = 0; i4 < a2.size(); i4++) {
                String str = (String) a2.elementAt(i4);
                if (str.contains("c#task")) {
                    i3++;
                }
                vector2.add(new hz(1, str));
            }
            if (i3 > 1) {
                dp dpVar = (dp) this.d.elementAt(i);
                for (int i5 = 1; i5 < i3; i5++) {
                    this.d.insertElementAt(dpVar, i);
                    i++;
                }
            }
        }
        int i6 = 0;
        this.c = new hz[vector2.size()];
        for (int i7 = 0; i7 < vector2.size(); i7++) {
            this.c[i7] = (hz) vector2.elementAt(i7);
            if (this.c[i7].b.contains("c#red")) {
                this.i = i7;
            }
            kk kkVar = kk.a;
            int c = kk.c(kk.a, this.c[i7].b);
            if (c > i6) {
                i6 = c;
            }
        }
        int length = this.c.length;
        int i8 = length;
        if (length < 3) {
            i8 = 3;
        }
        a_(i6 + 10, i8 * 11);
        this.b.a(this.b.e, this.b.f, this.c.length);
        this.g = false;
    }

    public static boolean a(Vector vector, Vector vector2, String str) {
        if (d.a().r > 0) {
            vector.addElement("c#yellow" + str + "c#white" + com.donglh.narutoninjasaga.c.a.iN + (10 - d.a().r) + "/10");
            if (com.donglh.narutoninjasaga.e.n.n().aG == null) {
                vector.addElement("c#white" + com.donglh.narutoninjasaga.c.a.iJ + "c#task" + com.donglh.narutoninjasaga.e.f.c().L[102].g + "c#white" + com.donglh.narutoninjasaga.c.a.iL);
                vector2.addElement(new dp(102, -1, -1, -1, 0, 0));
                return true;
            }
            String str2 = com.donglh.narutoninjasaga.e.n.n().aG.l;
            if (com.donglh.narutoninjasaga.e.n.n().aG.i > 1) {
                str2 = str2 + " " + com.donglh.narutoninjasaga.e.n.n().aG.b + "/" + com.donglh.narutoninjasaga.e.n.n().aG.i;
                if (com.donglh.narutoninjasaga.e.n.n().aG.a == 12) {
                    str2 = str2 + "c#white (" + com.donglh.narutoninjasaga.e.f.c().N[com.donglh.narutoninjasaga.e.n.n().aG.f].b + ")";
                }
            }
            if (com.donglh.narutoninjasaga.e.n.n().aG.a()) {
                vector.addElement("c#white" + com.donglh.narutoninjasaga.c.a.iJ + "c#task" + com.donglh.narutoninjasaga.e.f.c().L[102].g + "c#white" + com.donglh.narutoninjasaga.c.a.iP);
                vector2.addElement(new dp(102, -1, -1, -1, 0, 0));
                return true;
            }
            vector.addElement("c#blue  - c#task" + str2);
            vector2.addElement(new dp(com.donglh.narutoninjasaga.e.n.n().aG.d, com.donglh.narutoninjasaga.e.n.n().aG.c, com.donglh.narutoninjasaga.e.n.n().aG.e, com.donglh.narutoninjasaga.e.n.n().aG.f, com.donglh.narutoninjasaga.e.n.n().aG.g, com.donglh.narutoninjasaga.e.n.n().aG.h));
            return true;
        }
        return false;
    }

    public static boolean b(Vector vector, Vector vector2, String str) {
        if (d.a().s > 0) {
            vector.addElement("c#yellow" + str + "c#white" + com.donglh.narutoninjasaga.c.a.iQ + (d.a().t - d.a().s) + "/" + d.a().t);
            if (com.donglh.narutoninjasaga.e.n.n().aH == null) {
                vector.addElement("c#white" + com.donglh.narutoninjasaga.c.a.iJ + "c#task" + com.donglh.narutoninjasaga.e.f.c().L[98].g + "c#white" + com.donglh.narutoninjasaga.c.a.iL);
                vector2.addElement(new dp(98, -1, -1, -1, 0, 0));
                return true;
            }
            String str2 = com.donglh.narutoninjasaga.e.n.n().aH.l;
            if (com.donglh.narutoninjasaga.e.n.n().aH.a()) {
                vector.addElement("c#white" + com.donglh.narutoninjasaga.c.a.iJ + "c#task" + com.donglh.narutoninjasaga.e.f.c().L[98].g + "c#white" + com.donglh.narutoninjasaga.c.a.iP);
                vector2.addElement(new dp(98, -1, -1, -1, 0, 0));
                return true;
            }
            vector.addElement("c#blue- c#task" + str2);
            vector2.addElement(new dp(com.donglh.narutoninjasaga.e.n.n().aH.d, com.donglh.narutoninjasaga.e.n.n().aH.c, com.donglh.narutoninjasaga.e.n.n().aH.e, com.donglh.narutoninjasaga.e.n.n().aH.f, com.donglh.narutoninjasaga.e.n.n().aH.g, com.donglh.narutoninjasaga.e.n.n().aH.h));
            return true;
        }
        return false;
    }

    public static boolean c(Vector vector, Vector vector2, String str) {
        if (d.a().i() >= 20 && d.a().H > 0) {
            if (com.donglh.narutoninjasaga.e.n.n().aL == null) {
                vector.addElement("c#yellow" + str + "c#white" + com.donglh.narutoninjasaga.c.a.lP);
                vector.addElement("c#white" + com.donglh.narutoninjasaga.c.a.iJ + "c#task" + com.donglh.narutoninjasaga.e.f.c().L[100].g + "c#white" + com.donglh.narutoninjasaga.c.a.iL);
                vector2.addElement(new dp(100, -1, -1, -1, 0, 0));
                return true;
            } else if (!com.donglh.narutoninjasaga.e.n.n().aL.b) {
                vector.addElement("c#yellow" + str + "c#white" + com.donglh.narutoninjasaga.c.a.lP);
                String str2 = com.donglh.narutoninjasaga.e.n.n().aL.a().b;
                if (com.donglh.narutoninjasaga.e.n.n().aL.a().c > 1) {
                    str2 = str2 + " " + com.donglh.narutoninjasaga.e.n.n().aL.a + "/" + com.donglh.narutoninjasaga.e.n.n().aL.a().c;
                }
                if (com.donglh.narutoninjasaga.e.n.n().aL.b()) {
                    vector.addElement("c#white" + com.donglh.narutoninjasaga.c.a.iJ + "c#task" + com.donglh.narutoninjasaga.e.f.c().L[100].g + "c#white" + com.donglh.narutoninjasaga.c.a.iP);
                    vector2.addElement(new dp(100, -1, -1, -1, 0, 0));
                    return true;
                }
                vector.addElement("c#green - c#task" + str2);
                vector2.addElement(new dp(-1, -1, -1, -2, 0, 0));
                return true;
            } else {
                return true;
            }
        }
        return false;
    }

    public static boolean d(Vector vector, Vector vector2, String str) {
        if (com.donglh.narutoninjasaga.e.n.n().aI != null) {
            vector.addElement("c#yellow" + str + "c#white" + com.donglh.narutoninjasaga.c.a.oE);
            if (com.donglh.narutoninjasaga.e.n.n().aI.a()) {
                vector.addElement("c#white" + com.donglh.narutoninjasaga.c.a.iJ + "c#task" + com.donglh.narutoninjasaga.e.f.c().L[76].g + "c#white" + com.donglh.narutoninjasaga.c.a.iP);
                vector2.addElement(new dp(76, -1, -1, com.donglh.narutoninjasaga.e.n.n().aI.f, 0, 0));
                return true;
            }
            vector.addElement("c#green- c#task" + com.donglh.narutoninjasaga.c.a.oF + " " + com.donglh.narutoninjasaga.e.f.c().L[com.donglh.narutoninjasaga.e.n.n().aI.d].g);
            vector2.addElement(new dp(com.donglh.narutoninjasaga.e.n.n().aI.d, -1, -1, com.donglh.narutoninjasaga.e.n.n().aI.f, 0, 0));
            return true;
        }
        return false;
    }

    public static boolean e(Vector vector, Vector vector2, String str) {
        if (com.donglh.narutoninjasaga.e.n.n().aJ != null) {
            vector.addElement("c#yellow" + str + "c#white" + com.donglh.narutoninjasaga.c.a.oL);
            if (com.donglh.narutoninjasaga.e.n.n().aJ.a()) {
                vector.addElement("c#white" + com.donglh.narutoninjasaga.c.a.iJ + "c#task" + com.donglh.narutoninjasaga.e.f.c().L[76].g + "c#white" + com.donglh.narutoninjasaga.c.a.iP);
                vector2.addElement(new dp(76, com.donglh.narutoninjasaga.e.n.n().aJ.f, 4, (byte) 0));
                return true;
            }
            vector.addElement("c#green- c#task" + com.donglh.narutoninjasaga.e.aw.b(com.donglh.narutoninjasaga.c.a.oM, com.donglh.narutoninjasaga.e.f.c().L[com.donglh.narutoninjasaga.e.n.n().aJ.d].g));
            vector2.addElement(new dp(-1, com.donglh.narutoninjasaga.e.n.n().aJ.f, 4, (byte) 0));
            return true;
        }
        return false;
    }

    public static boolean f(Vector vector, Vector vector2, String str) {
        if (com.donglh.narutoninjasaga.e.n.n().aK != null) {
            vector.addElement("c#yellow" + str + "c#white" + com.donglh.narutoninjasaga.c.a.pN);
            if (com.donglh.narutoninjasaga.e.n.n().aK.a()) {
                vector.addElement("c#white" + com.donglh.narutoninjasaga.c.a.iJ + "c#task" + com.donglh.narutoninjasaga.e.f.c().L[73].g + "c#white" + com.donglh.narutoninjasaga.c.a.iL);
                vector2.addElement(new dp(73, 86, 5, (byte) 0));
                return true;
            }
            vector.addElement("c#green- c#task" + com.donglh.narutoninjasaga.e.aw.b(com.donglh.narutoninjasaga.c.a.pO, com.donglh.narutoninjasaga.e.f.c().M[com.donglh.narutoninjasaga.e.n.n().aK.e].h));
            vector2.addElement(new dp(-1, -1, 4, (byte) 0));
            return true;
        }
        return false;
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        try {
            if (!this.g) {
                this.g = true;
                com.donglh.narutoninjasaga.e.c.b(f);
                com.donglh.narutoninjasaga.e.az a2 = com.donglh.narutoninjasaga.e.az.a(this.aM * com.donglh.narutoninjasaga.e.f.c().u, this.aN * com.donglh.narutoninjasaga.e.f.c().u);
                f = a2;
                Pixmap pixmap = a2.b;
                com.donglh.narutoninjasaga.e.n.n();
                com.donglh.narutoninjasaga.e.n.a(pixmap, this);
                com.donglh.narutoninjasaga.e.c.a(f);
            }
            lVar.a(f, 0, 0);
            for (int i = 0; i < this.c.length; i++) {
                if (this.b.b(i)) {
                    kk.d(kk.a, lVar, this.c[i].b, 6, (i * this.b.f) + (this.b.f / 2) + 2, 0, -1, -10275328);
                }
            }
        } catch (Exception unused) {
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:227:0x0790, code lost:
        if (r5.e == false) goto L225;
     */
    @Override // com.donglh.narutoninjasaga.d.co
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void b() {
        /*
            Method dump skipped, instructions count: 2364
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: com.donglh.narutoninjasaga.d.iq.b():void");
    }

    private void a(int i) {
        this.e = false;
        com.donglh.narutoninjasaga.e.f.c().aE.a(this.aY + i, this.aZ + 15);
        com.donglh.narutoninjasaga.e.f.c().aE.a = true;
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        Vector vector = new Vector();
        if (!com.donglh.narutoninjasaga.e.n.n().V.c()) {
            return vector;
        }
        if (this.d.size() != 0) {
            vector.addElement(new gu(4000, 0, 0, this.aM, this.aN, this.b, this));
        } else if (this.i >= 0) {
            vector.addElement(new gu(4002, 0, 0, this.aM, this.aN, this.b, this));
        }
        return vector;
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        switch (guVar.b) {
            case 4000:
                kc.G().a(false);
                if (com.donglh.narutoninjasaga.e.n.n().aC == 22 && !com.donglh.narutoninjasaga.e.ay.a().a(13)) {
                    if (com.donglh.narutoninjasaga.e.n.n().aD == 0) {
                        if (com.donglh.narutoninjasaga.e.n.n().aH != null) {
                            a = 1;
                            a();
                            Vector vector = new Vector();
                            b(new Vector(), vector, "");
                            if (vector.size() > 0) {
                                com.donglh.narutoninjasaga.e.n.n().aF = (dp) vector.get(0);
                                return;
                            }
                            return;
                        }
                        h();
                        return;
                    } else if (com.donglh.narutoninjasaga.e.n.n().aD == 1) {
                        if (com.donglh.narutoninjasaga.e.n.n().aL != null) {
                            a = 0;
                            a();
                            Vector vector2 = new Vector();
                            a(new Vector(), vector2, "");
                            if (vector2.size() > 0) {
                                com.donglh.narutoninjasaga.e.n.n().aF = (dp) vector2.get(0);
                                return;
                            }
                            return;
                        }
                        h();
                        return;
                    } else {
                        h();
                        return;
                    }
                }
                h();
                return;
            case 4001:
                a();
                return;
            case 4002:
                com.donglh.narutoninjasaga.e.f.c().am.a("Hiện tại bạn chưa đủ cấp để làm tiếp nhiệm vụ chính tuyến, bạn có muốn chuyển sang giao diện phụ tuyến để tiếp tục làm nhiệm vụ khác không?", 2100, com.donglh.narutoninjasaga.e.n.n());
                return;
            default:
                return;
        }
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void k() {
        super.k();
        this.r.b = (short) -1;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v0 */
    /* JADX WARN: Type inference failed for: r0v1, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v10, types: [com.donglh.narutoninjasaga.e.n] */
    private dp h() {
        ?? r0 = 0;
        dp dpVar = null;
        try {
            dp dpVar2 = (dp) this.d.elementAt(0);
            if (dpVar2.a >= 0) {
                if (com.donglh.narutoninjasaga.e.n.n().i(dpVar2.a) != null) {
                    dpVar2.d = com.donglh.narutoninjasaga.e.n.n().A;
                }
                if (dpVar2.d < 0) {
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.c(dpVar2);
                }
            }
            dpVar = dpVar2.a();
            r0 = com.donglh.narutoninjasaga.e.n.n();
            r0.b(dpVar);
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
        }
        return dpVar;
    }
}
