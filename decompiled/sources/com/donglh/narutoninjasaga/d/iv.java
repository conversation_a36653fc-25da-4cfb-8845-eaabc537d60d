package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.graphics.Pixmap;
import java.util.Vector;
/* compiled from: LangLa_mm.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/iv.class */
public final class iv extends cl {
    private static com.donglh.narutoninjasaga.e.az a;
    private boolean b;
    private int c;

    public iv(int i, int i2, int i3, cn cnVar) {
        g(i, i2);
        a_(com.donglh.narutoninjasaga.e.f.c().u == 1 ? 100 : i3, 32);
        this.l = cnVar;
        this.b = false;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v35, types: [com.badlogic.gdx.graphics.Pixmap] */
    /* JADX WARN: Type inference failed for: r0v36, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v78 */
    @Override // com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        try {
            if (!this.b) {
                this.b = true;
                com.donglh.narutoninjasaga.e.c.b(a);
                com.donglh.narutoninjasaga.e.az a2 = com.donglh.narutoninjasaga.e.az.a(this.aM * com.donglh.narutoninjasaga.e.f.c().u, this.aN * com.donglh.narutoninjasaga.e.f.c().u);
                a = a2;
                Pixmap pixmap = a2.b;
                com.donglh.narutoninjasaga.e.n.n();
                ?? r0 = pixmap;
                com.donglh.narutoninjasaga.e.n.a((Pixmap) r0, this);
                try {
                    if (com.donglh.narutoninjasaga.e.n.n().ay != null && com.donglh.narutoninjasaga.e.n.n().ay.a.size() != 0) {
                        int i = 0;
                        while (true) {
                            r0 = i;
                            if (r0 >= com.donglh.narutoninjasaga.e.n.n().ay.a.size() || i >= 6) {
                                break;
                            }
                            com.donglh.narutoninjasaga.e.az a3 = com.donglh.narutoninjasaga.e.r.a(((dw) com.donglh.narutoninjasaga.e.n.n().ay.a.get(i)).a());
                            a.b(pixmap, a3.b, a3.c, a3.d, 5 * com.donglh.narutoninjasaga.e.f.c().u, 3 + (i * 15 * com.donglh.narutoninjasaga.e.f.c().u), 14 * com.donglh.narutoninjasaga.e.f.c().u, 14 * com.donglh.narutoninjasaga.e.f.c().u, Pixmap.Blending.SourceOver);
                            i++;
                        }
                    }
                } catch (Exception e) {
                    com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                }
                cg cgVar = com.donglh.narutoninjasaga.e.n.n().V;
                short s = (short) (this.aN + 14);
                short s2 = this.aM;
                cgVar.aM = s2;
                cgVar.aN = s;
                cgVar.d.a(s2);
                cgVar.e.a(s);
                cgVar.f = s2;
                cgVar.g = s;
                if (cgVar.h == 2) {
                    cgVar.a(false);
                } else if (cgVar.h == 1) {
                    cgVar.a(true);
                } else if (cgVar.c) {
                    cgVar.d.a(s2);
                    cgVar.e.a(s);
                } else {
                    cgVar.d.a(0);
                    cgVar.e.a(0);
                }
                com.donglh.narutoninjasaga.e.f.c().aC = false;
            }
            lVar.a(a, 0, 0);
            if (com.donglh.narutoninjasaga.e.n.n().V.c()) {
                try {
                    if (com.donglh.narutoninjasaga.e.n.n().ay != null && com.donglh.narutoninjasaga.e.n.n().ay.a.size() != 0) {
                        for (int i2 = 0; i2 < com.donglh.narutoninjasaga.e.n.n().ay.a.size() && i2 < 6; i2++) {
                            dw dwVar = (dw) com.donglh.narutoninjasaga.e.n.n().ay.a.get(i2);
                            if (i2 == 0) {
                                kk.d(kk.a, lVar, "c#yellow" + dwVar.a + " (" + ((int) dwVar.d) + ")", 22, 9 + (i2 * 15), 0, -1, -10275328);
                            } else {
                                kk.d(kk.a, lVar, dwVar.a + " (" + ((int) dwVar.d) + ")", 22, 9 + (i2 * 15), 0, -1, -10275328);
                            }
                        }
                        return;
                    }
                    kk.d(kk.b, lVar, com.donglh.narutoninjasaga.c.a.qe + " (0)", this.aY + 4, 8, 0, -7812062, 0);
                } catch (Exception unused) {
                }
            }
        } catch (Exception unused2) {
        }
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void b() {
        int i = 0;
        if (com.donglh.narutoninjasaga.e.n.n().ay != null) {
            i = com.donglh.narutoninjasaga.e.n.n().ay.a.size();
        }
        if (i > 6) {
            i = 6;
        }
        if (i != this.c) {
            this.c = i;
            int i2 = this.c << 4;
            int i3 = i2;
            if (i2 < 32) {
                i3 = 32;
            }
            a_(this.aM, i3);
            this.b = false;
        }
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        Vector vector = new Vector();
        if (!com.donglh.narutoninjasaga.e.n.n().V.c()) {
            return vector;
        }
        vector.addElement(new gu(4000, 0, 0, this.aM, this.aN, null, this));
        return vector;
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        switch (guVar.b) {
            case 4000:
                com.donglh.narutoninjasaga.e.n.n().a((cn) new bx(com.donglh.narutoninjasaga.e.n.n()));
                return;
            default:
                return;
        }
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void k() {
        super.k();
    }
}
