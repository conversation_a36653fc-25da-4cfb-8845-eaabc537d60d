package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.Input;
import java.util.Vector;
/* compiled from: LangLa_a.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/t.class */
public final class t extends he {
    public q[] a;
    private fx[] d;
    private dg e;
    private int f;
    private int g;
    public boolean b;
    public gt c;

    public t(com.donglh.narutoninjasaga.e.ai aiVar) {
        super(aiVar, new String[]{com.donglh.narutoninjasaga.c.a.aU, com.donglh.narutoninjasaga.c.a.F});
        this.a = new q[3];
        this.d = new fx[3];
        this.d[0] = new fx((byte) 1, 94, a_() + 20, 30, 30, 30, 1, 1);
        this.d[1] = new fx((byte) 1, 174, a_() + 20, 30, 30, 30, 1, 1);
        this.d[2] = new fx((byte) 1, 134, a_() + 65, 30, 30, 30, 1, 1);
        this.e = a(this.aM - 70, this.aN - 33, com.donglh.narutoninjasaga.c.a.aU, this, 0, -8);
        a(this.e, 0);
    }

    @Override // com.donglh.narutoninjasaga.d.et
    public final void a() {
        this.A = new q[d.a().W.length];
        int i = 0;
        for (int i2 = 0; i2 < d.a().W.length; i2++) {
            if (d.a().W[i2] != null && d.a().W[i2].A()) {
                this.A[i] = d.a().W[i2];
                i++;
            }
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void b() {
        try {
            super.b();
            if (this.i.b == 0) {
                for (int i = 0; i < this.d.length; i++) {
                    this.d[i].a();
                }
            } else {
                a();
            }
            if (this.c != null) {
                this.c.b();
                if (this.c.h()) {
                    this.c = null;
                }
            }
            if (this.g > 0) {
                this.g--;
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) this);
        }
    }

    /* JADX WARN: Type inference failed for: r0v28, types: [com.donglh.narutoninjasaga.d.gt, java.lang.Exception] */
    @Override // com.donglh.narutoninjasaga.d.he, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        ?? r0;
        try {
            if (q() <= 0) {
                a(lVar, (int) this.aY, (int) this.aZ, (int) this.aM, (int) this.aN, com.donglh.narutoninjasaga.c.a.o, (byte) 2, false);
            } else {
                super.a(lVar);
            }
            if (this.i.b == 0) {
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.aV, 4 + ((this.aM - 8) / 2), a_() + 9, 2, -10831436, -16777216);
                a(lVar, this.d[0].aY, this.d[0].aZ, this.a[0], this.d[0].i >= 0, com.donglh.narutoninjasaga.c.a.F);
                a(lVar, this.d[1].aY, this.d[1].aZ, this.a[1], this.d[1].i >= 0, com.donglh.narutoninjasaga.c.a.F);
                a(lVar, this.d[2].aY, this.d[2].aZ, this.a[2], this.d[2].i >= 0, com.donglh.narutoninjasaga.c.a.bk);
                com.donglh.narutoninjasaga.e.r.a(lVar, 95, 0, 142 - ((com.donglh.narutoninjasaga.e.f.c().i / 3) % 3), a_() + 25, 20);
                if (this.a[0] != null) {
                    kk.b(kk.c, lVar, "(+" + ((int) this.a[0].p) + ")", 101, a_() + 60, 33, -1, -16777216);
                }
                if (this.a[1] != null) {
                    kk.b(kk.c, lVar, "(+" + ((int) this.a[1].p) + ")", 181, a_() + 60, 33, -1, -16777216);
                }
                if (this.b) {
                    kk.c(kk.c, lVar, com.donglh.narutoninjasaga.c.a.jO, 24, a_() + Input.Keys.BUTTON_START, 0, -2560, -16777216);
                } else {
                    kk.c(kk.c, lVar, com.donglh.narutoninjasaga.c.a.ei, 24, a_() + Input.Keys.BUTTON_START, 0, -1, -16777216);
                    kk.c(kk.c, lVar, com.donglh.narutoninjasaga.c.a.en, 24, a_() + 124, 0, -1, -16777216);
                }
                if (this.c != null) {
                    r0 = this.c;
                    r0.b(lVar, this.d[1].aY + (this.d[1].f / 2), this.d[1].aZ + (this.d[1].f / 2));
                }
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
        }
    }

    @Override // com.donglh.narutoninjasaga.d.he, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        Vector c = super.c();
        if (this.i.b == 0) {
            for (int i = 0; i < this.d.length; i++) {
                c.addElement(this.d[i].a(i + 1001, this));
            }
        }
        return c;
    }

    @Override // com.donglh.narutoninjasaga.d.he, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        if (this.i.b == q() && guVar.b == 0) {
            d();
        }
        super.a(guVar, i, i2);
        if (this.i.b == 0) {
            switch (guVar.b) {
                case 0:
                    a(true);
                    return;
                case 1001:
                    this.f = 1;
                    a(guVar.j, guVar.j.i);
                    if (this.a[0] == null) {
                        this.n = b(com.donglh.narutoninjasaga.c.a.er, this, guVar.j.aY + 32, guVar.j.aZ, 100);
                        return;
                    } else {
                        this.n = a(guVar, this, this.a[0]);
                        return;
                    }
                case 1002:
                    this.f = 2;
                    a(guVar.j, guVar.j.i);
                    if (this.a[1] == null) {
                        this.n = b(com.donglh.narutoninjasaga.c.a.eu, this, guVar.j.aY + 32, guVar.j.aZ, 100);
                        return;
                    } else {
                        this.n = a(guVar, this, this.a[1]);
                        return;
                    }
                case 1003:
                    this.f = 3;
                    a(guVar.j, guVar.j.i);
                    if (this.a[2] == null) {
                        this.n = b(com.donglh.narutoninjasaga.c.a.ex, this, guVar.j.aY + 32, guVar.j.aZ, 100);
                        return;
                    } else {
                        this.n = a(guVar, this, this.a[2]);
                        return;
                    }
                case 2001:
                    a(false);
                    return;
                default:
                    return;
            }
        }
    }

    private void a(fw fwVar, int i) {
        for (int i2 = 0; i2 < this.d.length; i2++) {
            this.d[i2].i = -1;
        }
        fwVar.i = i;
    }

    @Override // com.donglh.narutoninjasaga.d.el
    public final void a(int i) {
        super.a(i);
        if (this.b) {
            this.b = false;
            for (int i2 = 0; i2 < this.a.length; i2++) {
                this.a[i2] = null;
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.ej
    public final void d() {
        super.d();
        for (int i = 0; i < this.a.length; i++) {
            if (this.a[i] != null) {
                d.a().g(this.a[i].r)[this.a[i].e] = this.a[i];
                this.a[i] = null;
            }
        }
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x0171: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:36:0x0170 */
    private void a(boolean z) {
        Exception a;
        try {
            if (this.b) {
                com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.eA, -2560);
            } else if (this.a[0] != null && this.a[1] != null && this.a[2] != null) {
                if (this.a[0].h().f != this.a[1].h().f) {
                    com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.eE, -65536);
                } else if (this.a[0].h().i > this.a[1].h().i) {
                    com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.eF, -65536);
                } else if (this.a[0].p > 14 && this.a[2].c != 158) {
                    com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.eG, -65536);
                } else if (this.a[0].p > 10 && this.a[2].c != 158 && this.a[2].c != 157) {
                    com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.eH, -65536);
                } else if (z) {
                    com.donglh.narutoninjasaga.e.f.c().am.a(com.donglh.narutoninjasaga.c.a.eI, 2001, this);
                } else {
                    com.donglh.narutoninjasaga.e.ak akVar = new com.donglh.narutoninjasaga.e.ak((byte) 104);
                    akVar.a(this.a[0].r);
                    akVar.b(this.a[0].e);
                    akVar.a(this.a[1].r);
                    akVar.b(this.a[1].e);
                    akVar.b(this.a[2].e);
                    akVar.l();
                }
            } else {
                com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.eB, -65536);
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a);
        }
    }
}
