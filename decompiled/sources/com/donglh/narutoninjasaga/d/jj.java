package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.Input;
import com.badlogic.gdx.net.HttpStatus;
import java.util.Collections;
import java.util.Hashtable;
import java.util.Vector;
/* compiled from: LangLa_u.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/jj.class */
public final class jj extends el {
    private String[] e;
    private static com.donglh.narutoninjasaga.e.az f;
    private static com.donglh.narutoninjasaga.e.az g;
    private ga[] h;
    public ig[] a;
    private fx x;
    public int b;
    public int c;
    private int y;
    private int z;
    private int A;
    private cd B;
    private gt C;
    private int D;
    private fw[] E;
    private fw[] F;
    private Vector G;
    private boolean H;
    private com.donglh.narutoninjasaga.e.y I;
    private int J;
    private Vector K;
    private int[] L;
    private String[][] M;
    private dg N;
    public static jj d;

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v42, types: [boolean] */
    /* JADX WARN: Type inference failed for: r0v43, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v50, types: [long] */
    /* JADX WARN: Type inference failed for: r1v55, types: [java.lang.String[], java.lang.String[][]] */
    public jj(com.donglh.narutoninjasaga.e.ai aiVar) {
        super(aiVar);
        String A;
        this.z = -1;
        this.D = -1;
        this.K = new Vector();
        this.L = new int[]{99, 100, 45, 21, 47, 97, 102, 32, 59, 98, 28, 93};
        d(320, 235);
        this.u = false;
        d = this;
        if (com.donglh.narutoninjasaga.e.f.c().N[com.donglh.narutoninjasaga.e.n.n().A].b.length() <= 13) {
            this.e = new String[]{com.donglh.narutoninjasaga.e.f.c().N[com.donglh.narutoninjasaga.e.n.n().A].b};
        } else {
            this.e = kk.a(com.donglh.narutoninjasaga.e.f.c().N[com.donglh.narutoninjasaga.e.n.n().A].b);
        }
        this.h = new ga[3];
        gz gzVar = new gz(-1000);
        gzVar.c = com.donglh.narutoninjasaga.c.a.qK;
        a(gzVar, 72, 4);
        this.a = new ig[9];
        for (int i = 0; i < this.a.length; i++) {
            this.a[i] = new ig();
        }
        e();
        Vector vector = new Vector();
        for (int i2 = 0; i2 < com.donglh.narutoninjasaga.e.n.n().H.size(); i2++) {
            jt jtVar = (jt) com.donglh.narutoninjasaga.e.n.n().H.get(i2);
            if (jtVar.a().h.length() > 0) {
                A = jtVar.A() + " (" + jtVar.a().h + ")";
            } else {
                A = jtVar.A();
            }
            if (!A.isEmpty()) {
                vector.add(new hz(jtVar.a, A));
            }
        }
        Collections.sort(vector, hz.d);
        vector.insertElementAt(new hz(-1, com.donglh.narutoninjasaga.c.a.ol), 0);
        short[] sArr = new short[vector.size()];
        String[] strArr = new String[vector.size()];
        StringBuilder sb = new StringBuilder();
        for (int i3 = 0; i3 < vector.size(); i3++) {
            sArr[i3] = (short) ((hz) vector.get(i3)).a;
            sb.append((int) sArr[i3]).append(", ");
            strArr[i3] = ((hz) vector.get(i3)).b;
        }
        gz gzVar2 = new gz(-2000, sArr, strArr);
        this.B = a(6, this.aN - 47, 84, 7, gzVar2, this, 1);
        gzVar2.b = 0;
        a(this.B, 0);
        gzVar.b = -1;
        a(0);
        db a = db.a();
        ?? r0 = a.a;
        if (r0 == 0) {
            try {
                r0 = a.b[6] != null ? a.b[6].play() : r0;
            } catch (Exception e) {
                com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
            }
        }
        this.G = new Vector();
        for (int i4 = 0; i4 < this.L.length; i4++) {
            this.G.addElement(new hz(this.L[i4], String.format("%s (%s)", com.donglh.narutoninjasaga.e.f.c().L[this.L[i4]].g, com.donglh.narutoninjasaga.e.f.c().L[this.L[i4]].h)));
        }
        this.M = new String[this.L.length];
        this.M[0] = com.donglh.narutoninjasaga.c.a.te;
        this.M[1] = com.donglh.narutoninjasaga.c.a.tf;
        this.M[2] = com.donglh.narutoninjasaga.c.a.tg;
        this.M[3] = com.donglh.narutoninjasaga.c.a.th;
        this.M[4] = com.donglh.narutoninjasaga.c.a.ti;
        this.M[5] = com.donglh.narutoninjasaga.c.a.tj;
        this.M[6] = com.donglh.narutoninjasaga.c.a.tk;
        this.M[7] = com.donglh.narutoninjasaga.c.a.tl;
        this.M[8] = com.donglh.narutoninjasaga.c.a.tm;
        this.M[9] = com.donglh.narutoninjasaga.c.a.tn;
        this.M[10] = com.donglh.narutoninjasaga.c.a.to;
        this.M[11] = com.donglh.narutoninjasaga.c.a.tp;
        this.F = new fw[3];
        this.F[0] = new fw((byte) 1, 13, a_() + 9, (this.aM - 8) - 18, 189, 27, this.G.size());
        this.F[1] = new fw((byte) 1, 13, a_() + 9, (this.aM - 8) - 18, 27, 27, 1);
        this.N = a(this.aM - 75, this.aN - 37, com.donglh.narutoninjasaga.c.a.lW, this, 1011, -8);
        a(this.N, 3);
        u();
        new com.donglh.narutoninjasaga.e.ak((byte) -6).l();
    }

    public final void e() {
        this.x = new fx((byte) 1, 9, a_() + 60, 80, 80, 26, com.donglh.narutoninjasaga.e.aw.c(this.a.length, 3), 3);
        this.x.a(this.b / 3);
    }

    public final void t() {
        this.D = 0;
        this.h[0] = null;
    }

    @Override // com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void b() {
        super.b();
        this.x.a();
        if (this.B != null) {
            this.B.b(com.donglh.narutoninjasaga.e.n.n().M());
        }
        this.N.b(!this.H);
        if (this.F != null) {
            for (int i = 0; i < this.F.length; i++) {
                if (this.F[i] != null) {
                    this.F[i].a();
                }
            }
        }
        if (this.h[this.i.b] != null) {
            if (this.i.b == 0 && d.a().J() && (this.r == null || this.r.b < 0)) {
                this.h[0].h = (d.a().aY / com.donglh.narutoninjasaga.e.n.n().E) - (this.h[0].aM / 2);
                this.h[0].o = (d.a().aZ / com.donglh.narutoninjasaga.e.n.n().E) - (this.h[0].aN / 2);
            }
            this.h[this.i.b].a();
        }
        if (this.y > 0) {
            this.y--;
            if (this.z >= 0 && this.y < 5) {
                com.donglh.narutoninjasaga.e.n.n().aF = new dp(com.donglh.narutoninjasaga.e.f.c().D[this.z].a, 0, 0);
                this.z = -1;
            }
            if (this.y == 0) {
                p();
                kc.G().a(false);
            }
        }
        for (int i2 = 0; i2 < this.E.length; i2++) {
            if (this.E[i2] != null) {
                this.E[i2].a();
            }
        }
        if (this.C != null) {
            this.C.b();
            if (this.C.h()) {
                this.C = null;
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        super.a(lVar);
        int i = 0;
        int i2 = 0;
        int i3 = 0;
        int i4 = 0;
        if (this.D == 0) {
            this.D = -1;
            com.donglh.narutoninjasaga.e.c.b(f);
            int i5 = 1;
            int i6 = 1;
            if (!com.donglh.narutoninjasaga.e.ay.a().a(18)) {
                String str = "mapsmall_" + ((int) com.donglh.narutoninjasaga.e.n.n().A);
                com.donglh.narutoninjasaga.e.az i7 = com.donglh.narutoninjasaga.e.c.i(str);
                f = i7;
                if (i7 == null) {
                    ea eaVar = new ea();
                    eaVar.g = new Hashtable();
                    eaVar.a = com.donglh.narutoninjasaga.e.n.n().A;
                    eaVar.d = "map/" + ((int) com.donglh.narutoninjasaga.e.n.n().A) + ".png";
                    eaVar.e = str;
                    cp.aS.add(eaVar);
                    cp.R();
                } else {
                    com.donglh.narutoninjasaga.e.c.a(f);
                    i5 = com.donglh.narutoninjasaga.e.r.a(f);
                    i6 = com.donglh.narutoninjasaga.e.r.b(f);
                }
            }
            if (this.h[0] == null) {
                int i8 = (this.aM - 8) - 97;
                int i9 = (this.aN - 33) - 36;
                int i10 = 95;
                int a_ = a_() + 19;
                if (i8 > i5) {
                    i10 = 95 + ((i8 - i5) / 2);
                    i8 = i5;
                }
                if (i9 > i6) {
                    a_ += (i9 - i6) / 2;
                    i9 = i6;
                }
                this.h[0] = new ga((byte) 2, i10, a_, i8, i9, i5, i6);
                this.h[0].h = (d.a().aY / com.donglh.narutoninjasaga.e.n.n().E) - (this.h[0].aM / 2);
                this.h[0].o = (d.a().aZ / com.donglh.narutoninjasaga.e.n.n().E) - (this.h[0].aN / 2);
            }
        } else if (this.D == 1) {
            this.D = -1;
            com.donglh.narutoninjasaga.e.c.b(f);
            v();
        }
        if (this.i.b < 2 && f != null) {
            a(lVar, this.aY + this.h[this.i.b].aY, this.aZ + this.h[this.i.b].aZ);
            i = this.h[this.i.b].d;
            i2 = this.h[this.i.b].n;
            if (i < 0) {
                i = 0;
            }
            if (i2 < 0) {
                i2 = 0;
            }
            i3 = i + this.h[this.i.b].aM;
            i4 = i2 + this.h[this.i.b].aN;
            if (i3 > com.donglh.narutoninjasaga.e.r.a(f)) {
                int a = com.donglh.narutoninjasaga.e.r.a(f);
                i3 = a;
                i = a - this.h[this.i.b].aM;
            }
            if (i4 > com.donglh.narutoninjasaga.e.r.b(f)) {
                int b = com.donglh.narutoninjasaga.e.r.b(f);
                i4 = b;
                i2 = b - this.h[this.i.b].aN;
            }
            int i11 = (i3 - i) * com.donglh.narutoninjasaga.e.f.c().u;
            int i12 = (i4 - i2) * com.donglh.narutoninjasaga.e.f.c().u;
            if (this.h[this.i.b].aM * com.donglh.narutoninjasaga.e.f.c().u > f.c) {
                i = 0;
                i11 = f.c;
            }
            if (this.h[this.i.b].aN * com.donglh.narutoninjasaga.e.f.c().u > f.d) {
                i2 = 0;
                i12 = f.d;
            }
            lVar.a(f, i, i2, i11, i12, 0, 0, 0, 0);
            a(lVar, this.aY, this.aZ);
            a(lVar, this.h[this.i.b].aY - 3, this.h[this.i.b].aZ - 3, this.h[this.i.b].aM + 6, this.h[this.i.b].aN + 6, 0, 55, 56);
        }
        switch (this.i.b) {
            case 0:
                d(lVar);
                return;
            case 1:
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.nw, this.aM / 2, this.aN - 11, 2, -6488, -10275328);
                a(lVar, this.h[this.i.b]);
                a(lVar, i, i2, i3, i4);
                b(lVar);
                return;
            case 2:
                e(lVar);
                return;
            case 3:
                f(lVar);
                return;
            default:
                return;
        }
    }

    private void d(l lVar) {
        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.e.f.c().N[com.donglh.narutoninjasaga.e.n.n().A].b, this.h[this.i.b].aY + (this.h[this.i.b].aM / 2), this.h[this.i.b].aZ - 12, 2, -1118703, -10275328);
        lVar.a(3, a_() + 30);
        if (!com.donglh.narutoninjasaga.e.n.n().M() || com.donglh.narutoninjasaga.e.n.n().A == 42 || com.donglh.narutoninjasaga.e.n.n().A == 43 || com.donglh.narutoninjasaga.e.n.n().A == 41 || com.donglh.narutoninjasaga.e.n.n().A == 49 || com.donglh.narutoninjasaga.e.n.n().A == 101) {
            kk.b(kk.c, lVar, "XY: " + ((int) d.a().aY) + ", " + ((int) d.a().aZ), 8, -3, 0, -6488, -10275328);
            kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.hE + this.b + "/" + (this.a.length - 1), 8, 8, 0, -6488, -10275328);
            kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.gc + this.c, 8, 19, 0, -6488, -10275328);
            a(lVar, this.x);
            lVar.e(15190937);
            lVar.c(0, 0, this.x.aM - 1, (this.x.g * this.x.f) + 1);
            int i = 0;
            for (int i2 = 0; i2 < this.x.g; i2++) {
                for (int i3 = 0; i3 < this.x.o; i3++) {
                    if (this.x.b(i2)) {
                        int i4 = 1 + (i3 * this.x.f);
                        int i5 = 1 + (i2 * this.x.f);
                        boolean z = i == this.x.i;
                        if (i == this.b) {
                            a.a(lVar, -7, 0, i4, i5, this.x.f - 2, this.x.f - 2);
                        }
                        lVar.e(8798745);
                        lVar.b(i4, i5, this.x.f - 2, this.x.f - 2);
                        if (z) {
                            lVar.e(-1);
                            lVar.b(i4, i5, this.x.f - 2, this.x.f - 2);
                        }
                        ig igVar = this.a[i];
                        kk.b(kk.d, lVar, new StringBuilder().append(i).toString(), i4 + (this.x.f / 2), i5 + (this.x.f / 2), 2, -7812062, -16777216);
                        if (igVar.a() == 0) {
                            kk.b(kk.a, lVar, new StringBuilder().append(igVar.a).toString(), (i4 + this.x.f) - 3, (i5 + this.x.f) - 5, 1, -1, -16777216);
                        } else if (igVar.a() == 1) {
                            kk.b(kk.a, lVar, new StringBuilder().append(igVar.a).toString(), (i4 + this.x.f) - 3, (i5 + this.x.f) - 5, 1, -2560, -16777216);
                        } else if (igVar.a() == 2) {
                            kk.b(kk.d, lVar, new StringBuilder().append(i).toString(), i4 + (this.x.f / 2), i5 + (this.x.f / 2), 2, -65536, -16777216);
                        }
                    }
                    i++;
                }
            }
            b(lVar);
        }
        a(lVar, f(), g());
        a(lVar, this.h[this.i.b]);
        this.A = -1;
        for (int i6 = 0; i6 < com.donglh.narutoninjasaga.e.n.n().H.size(); i6++) {
            jt jtVar = (jt) com.donglh.narutoninjasaga.e.n.n().H.elementAt(i6);
            int i7 = jtVar.aY / com.donglh.narutoninjasaga.e.n.n().E;
            int i8 = jtVar.aZ / com.donglh.narutoninjasaga.e.n.n().E;
            lVar.e(-2560);
            lVar.c(i7 - 2, i8 - 2, 4, 4);
        }
        Vector vector = new Vector();
        for (int i9 = 0; i9 < com.donglh.narutoninjasaga.e.n.n().H.size(); i9++) {
            jt jtVar2 = (jt) com.donglh.narutoninjasaga.e.n.n().H.elementAt(i9);
            if (jtVar2.aG == 0 || jtVar2.a == 95) {
                fe feVar = new fe();
                feVar.c = jtVar2.aY / com.donglh.narutoninjasaga.e.n.n().E;
                feVar.d = jtVar2.aZ / com.donglh.narutoninjasaga.e.n.n().E;
                feVar.b = jtVar2.a().g;
                feVar.a = jtVar2.a;
                feVar.e = kk.b(kk.a, feVar.b);
                feVar.f = kk.a(kk.a) - 1;
                vector.add(feVar);
            }
        }
        Collections.sort(vector, fe.g);
        for (int i10 = 0; i10 < vector.size(); i10++) {
            fe feVar2 = (fe) vector.elementAt(i10);
            for (int i11 = i10 + 1; i11 < vector.size() - 1; i11++) {
                fe feVar3 = (fe) vector.elementAt(i11);
                if (feVar2.c - (feVar2.e / 2) < feVar3.c + (feVar3.e / 2) && feVar2.c + (feVar2.e / 2) > feVar3.c - (feVar3.e / 2) && feVar2.d < feVar3.d + feVar3.f && feVar2.f + feVar2.d > feVar3.d) {
                    feVar3.d = feVar2.d - 6;
                }
            }
        }
        for (int i12 = 0; i12 < vector.size(); i12++) {
            fe feVar4 = (fe) vector.elementAt(i12);
            int b = kk.b(kk.a, feVar4.b);
            if (feVar4.c - (b / 2) < 3) {
                feVar4.c = 3 + (b / 2);
            }
            if (feVar4.c + (b / 2) + 3 > com.donglh.narutoninjasaga.e.n.n().B / com.donglh.narutoninjasaga.e.n.n().E) {
                feVar4.c = ((com.donglh.narutoninjasaga.e.n.n().B / com.donglh.narutoninjasaga.e.n.n().E) - 3) - (b / 2);
            }
            if (!com.donglh.narutoninjasaga.e.f.an && com.donglh.narutoninjasaga.e.n.n().s().d != 9) {
                kk.a(kk.a, lVar, feVar4.b, feVar4.c, feVar4.d - 7, 2, -2560, -16777216);
            }
        }
        lVar.e(-16647317);
        for (int i13 = 0; i13 < com.donglh.narutoninjasaga.e.n.n().G.size(); i13++) {
            jq jqVar = (jq) com.donglh.narutoninjasaga.e.n.n().G.elementAt(i13);
            if (jqVar.q) {
                int i14 = jqVar.e / com.donglh.narutoninjasaga.e.n.n().E;
                int i15 = jqVar.f / com.donglh.narutoninjasaga.e.n.n().E;
                lVar.c(i14 - 2, i15 - 2, 4, 4);
                kk.a(kk.a, lVar, jqVar.h().h, i14, i15 - 7, 2, -16647317, -13553359);
            }
        }
        lVar.c(-65536);
        for (int i16 = 0; i16 < com.donglh.narutoninjasaga.e.n.n().M.size(); i16++) {
            kf kfVar = (kf) com.donglh.narutoninjasaga.e.n.n().M.elementAt(i16);
            short s = kfVar.aY;
            short s2 = kfVar.aZ;
            ki b2 = d.b(s, s2);
            if (b2 != null) {
                s = b2.aY;
                s2 = b2.aZ;
            }
            int i17 = (s2 - 24) / com.donglh.narutoninjasaga.e.n.n().E;
            if (i17 < 10) {
                a.a(lVar, 95, -270, (s - 20) / com.donglh.narutoninjasaga.e.n.n().E, 10, 40);
            } else if (i17 > (com.donglh.narutoninjasaga.e.n.n().C - 20) / com.donglh.narutoninjasaga.e.n.n().E) {
                com.donglh.narutoninjasaga.e.n.n();
                com.donglh.narutoninjasaga.e.n.n();
            } else if (s > com.donglh.narutoninjasaga.e.n.n().B / 2) {
                a.a(lVar, 95, 0, (s - 40) / com.donglh.narutoninjasaga.e.n.n().E, i17, 40);
            } else {
                a.a(lVar, 95, -360, (s - 20) / com.donglh.narutoninjasaga.e.n.n().E, i17, 40);
            }
            int i18 = s / com.donglh.narutoninjasaga.e.n.n().E;
            int i19 = (s2 - 48) / com.donglh.narutoninjasaga.e.n.n().E;
            int i20 = i19;
            if (i19 > (com.donglh.narutoninjasaga.e.n.n().C / com.donglh.narutoninjasaga.e.n.n().E) - 5) {
                i20 = (com.donglh.narutoninjasaga.e.n.n().C / com.donglh.narutoninjasaga.e.n.n().E) - 5;
            }
            String str = com.donglh.narutoninjasaga.e.f.c().N[kfVar.b].b;
            if (i18 - (kk.b(kk.a, str) / 2) < 5) {
                i18 = 5 + (kk.b(kk.a, str) / 2);
            }
            if (i18 > ((com.donglh.narutoninjasaga.e.n.n().B / com.donglh.narutoninjasaga.e.n.n().E) - (kk.b(kk.a, str) / 2)) - 5) {
                i18 = ((com.donglh.narutoninjasaga.e.n.n().B / com.donglh.narutoninjasaga.e.n.n().E) - (kk.b(kk.a, str) / 2)) - 5;
            }
            if (i20 < 12) {
                i20 += 12;
            }
            kk.a(kk.a, lVar, str, i18, i20, 2, -48128, -16777216);
        }
        lVar.c();
        lVar.e(-16711681);
        if (com.donglh.narutoninjasaga.e.n.n().ay != null) {
            for (int i21 = 0; i21 < com.donglh.narutoninjasaga.e.n.n().ay.a.size(); i21++) {
                d b3 = com.donglh.narutoninjasaga.e.n.n().b(((dw) com.donglh.narutoninjasaga.e.n.n().ay.a.elementAt(i21)).a);
                if (b3 != null && !b3.N.equals(d.a().N)) {
                    int i22 = b3.aY / com.donglh.narutoninjasaga.e.n.n().E;
                    int i23 = b3.aZ / com.donglh.narutoninjasaga.e.n.n().E;
                    lVar.c(i22 - 2, i23 - 2, 4, 4);
                    kk.b(kk.a, lVar, b3.N, i22, i23 - 7, 2, -16711681, -13553359);
                }
            }
        }
        lVar.e(-3407617);
        for (int i24 = 0; i24 < com.donglh.narutoninjasaga.e.n.n().F.size(); i24++) {
            d dVar = (d) com.donglh.narutoninjasaga.e.n.n().F.get(i24);
            if (dVar != null && com.donglh.narutoninjasaga.e.n.n().a(dVar)) {
                int i25 = dVar.aY / com.donglh.narutoninjasaga.e.n.n().E;
                int i26 = dVar.aZ / com.donglh.narutoninjasaga.e.n.n().E;
                lVar.c(i25 - 2, i26 - 2, 4, 4);
                kk.b(kk.a, lVar, dVar.N, i25, i26 - 7, 2, -3407617, -13553359);
            }
        }
        int i27 = d.a().aY / com.donglh.narutoninjasaga.e.n.n().E;
        int i28 = d.a().aZ / com.donglh.narutoninjasaga.e.n.n().E;
        if (com.donglh.narutoninjasaga.e.f.c().i % 30 < 10) {
            lVar.e(-1);
        } else {
            lVar.e(-14443265);
        }
        lVar.c(i27 - 2, i28 - 2, 4, 4);
        if (this.C != null) {
            this.C.a(lVar);
        }
        b(lVar);
    }

    private void a(l lVar, int i, int i2, int i3, int i4) {
        for (int i5 = 0; i5 < com.donglh.narutoninjasaga.e.f.c().D.length; i5++) {
            if (com.donglh.narutoninjasaga.e.f.c().D[i5].a >= 0 && com.donglh.narutoninjasaga.e.f.c().D[i5].a < com.donglh.narutoninjasaga.e.f.c().N.length) {
                lVar.a(g, com.donglh.narutoninjasaga.e.f.c().D[i5].b - 2, com.donglh.narutoninjasaga.e.f.c().D[i5].c - 2);
            }
            if (com.donglh.narutoninjasaga.e.f.c().D[i5].a >= 0 && com.donglh.narutoninjasaga.e.f.c().D[i5].a < com.donglh.narutoninjasaga.e.f.c().N.length && i - 50 <= com.donglh.narutoninjasaga.e.f.c().D[i5].b && com.donglh.narutoninjasaga.e.f.c().D[i5].b <= i3 + 50 && i2 - 14 <= com.donglh.narutoninjasaga.e.f.c().D[i5].c && com.donglh.narutoninjasaga.e.f.c().D[i5].c <= i4 + 14) {
                short s = com.donglh.narutoninjasaga.e.f.c().D[i5].b;
                String str = com.donglh.narutoninjasaga.e.f.c().N[com.donglh.narutoninjasaga.e.f.c().D[i5].a].b;
                int b = b(com.donglh.narutoninjasaga.e.f.c().D[i5].a);
                if (b > 0) {
                    str = str + " (" + com.donglh.narutoninjasaga.c.a.Y + b + ")";
                }
                if (s < 2 + (kk.b(kk.c, str) / 2)) {
                    s = 2 + (kk.b(kk.c, str) / 2);
                }
                if (s > (this.h[this.i.b].aM - (kk.b(kk.c, str) / 2)) - 2) {
                    s = (this.h[this.i.b].aM - (kk.b(kk.c, str) / 2)) - 2;
                }
                if (i5 == this.z) {
                    kk.b(kk.c, lVar, str, s, com.donglh.narutoninjasaga.e.f.c().D[i5].c - 1, 2, -2560, 0);
                } else if (com.donglh.narutoninjasaga.e.f.c().D[i5].a == com.donglh.narutoninjasaga.e.n.n().A && com.donglh.narutoninjasaga.e.f.c().i % 20 < 8) {
                    kk.b(kk.c, lVar, str, s, com.donglh.narutoninjasaga.e.f.c().D[i5].c - 1, 2, -16711681, 0);
                }
            }
        }
    }

    private void e(l lVar) {
        Vector vector = new Vector();
        vector.add(71);
        vector.add(72);
        vector.add(70);
        vector.add(87);
        vector.add(80);
        vector.add(78);
        vector.add(81);
        vector.add(79);
        vector.add(74);
        vector.add(88);
        vector.add(76);
        vector.add(73);
        a(lVar, this.E[0]);
        for (int i = 0; i < com.donglh.narutoninjasaga.e.n.n().a.size(); i++) {
            ex exVar = (ex) com.donglh.narutoninjasaga.e.n.n().a.get(i);
            kk.c(kk.a, lVar, com.donglh.narutoninjasaga.e.f.c().N[exVar.b].b + com.donglh.narutoninjasaga.e.aw.b(com.donglh.narutoninjasaga.c.a.pJ, new StringBuilder().append(b(exVar.b)).toString()) + com.donglh.narutoninjasaga.e.aw.b(com.donglh.narutoninjasaga.c.a.pH, "c#cyan" + exVar.a + "c#yellow"), 9, 7 + (i * 13), 0, -2560, -10275328);
            vector.remove(Integer.valueOf(exVar.b));
        }
        for (int i2 = 0; i2 < vector.size(); i2++) {
            kk.c(kk.a, lVar, com.donglh.narutoninjasaga.e.f.c().N[((Integer) vector.get(i2)).intValue()].b + com.donglh.narutoninjasaga.e.aw.b(com.donglh.narutoninjasaga.c.a.pJ, new StringBuilder().append(b(((Integer) vector.get(i2)).intValue())).toString()) + com.donglh.narutoninjasaga.c.a.pI, 9, 7 + (com.donglh.narutoninjasaga.e.n.n().a.size() * 13) + (i2 * 13), 0, -1, -10275328);
        }
        b(lVar);
        this.E[0].d(lVar, -5, -10);
        a(lVar, ((4 + (this.aM - 8)) - 90) - 3, a_() + 11, 92, (this.aN - 33) - 27, -11, 22, 23, 1, 1);
        kk.c(kk.a, lVar, com.donglh.narutoninjasaga.c.a.pL, this.E[1].aY + (this.E[1].aM / 2), a_() + 18, 2, -1, -10275328);
        if (com.donglh.narutoninjasaga.e.n.n().b.size() > 0) {
            a(lVar, this.E[1]);
            for (int i3 = 0; i3 < com.donglh.narutoninjasaga.e.n.n().b.size(); i3++) {
                ex exVar2 = (ex) com.donglh.narutoninjasaga.e.n.n().b.get(i3);
                kk.c(kk.a, lVar, exVar2.a + com.donglh.narutoninjasaga.e.aw.b(com.donglh.narutoninjasaga.c.a.pK, new StringBuilder().append(exVar2.b).toString()), 3, 6 + (i3 * 13), 0, -2560, -10275328);
            }
            b(lVar);
            this.E[1].d(lVar, -12, -10);
        }
        kk.a(kk.a, lVar, com.donglh.narutoninjasaga.c.a.cU, (4 + (this.aM - 8)) - 40, (a_() + (this.aN - 33)) - 12, 0, -1, -10275328);
        lVar.e(-1);
        lVar.a((4 + (this.aM - 8)) - 40, ((a_() + (this.aN - 33)) - 12) + 4, ((4 + (this.aM - 8)) - 40) + kk.b(kk.a, com.donglh.narutoninjasaga.c.a.cU), ((a_() + (this.aN - 33)) - 12) + 4);
    }

    private void f(l lVar) {
        if (!this.H) {
            a(lVar, this.F[0]);
            for (int i = 0; i < this.F[0].g; i++) {
                if (this.F[0].b(i)) {
                    a(lVar, 0, i * this.F[0].f, this.F[0].aM, 26, -17, 84, 5, 1, 1);
                    com.donglh.narutoninjasaga.e.r.a(lVar, 395, 0, 15, (i * this.F[0].f) + 13, 3);
                    if (this.F[0].j == i) {
                        kk.a(lVar, ((hz) this.G.get(i)).b, 26, 13 + (i * this.F[0].f), 0, -1);
                    } else {
                        kk.a(lVar, ((hz) this.G.get(i)).b, 26, 12 + (i * this.F[0].f), 0, -1);
                    }
                }
            }
            b(lVar);
            this.F[0].d(lVar, -5, -10);
            return;
        }
        a(lVar, this.F[1]);
        a(lVar, 0, 0 * this.F[1].f, this.F[1].aM, 26, -17, 84, 5, 1, 1);
        if (this.F[1].j == 0) {
            kk.a(lVar, ((hz) this.G.get(this.J)).b, 26, 13, 0, -1);
            lVar.d();
            com.donglh.narutoninjasaga.e.r.a(lVar, 398, 0, this.F[1].aM - 21, 5, 20);
            lVar.e();
        } else {
            kk.a(lVar, ((hz) this.G.get(this.J)).b, 26, 12, 0, -1);
            com.donglh.narutoninjasaga.e.r.a(lVar, 398, 0, this.F[1].aM - 22, 4, 20);
        }
        com.donglh.narutoninjasaga.e.r.a(lVar, 396, 0, 15, 13, 3);
        b(lVar);
        a(lVar, 12, a_() + 40, (this.aM - 8) - 16, (this.aN - 33) - 78, 4, 55, 56);
        lVar.i = true;
        for (int i2 = 0; i2 < this.I.a.length; i2++) {
            com.donglh.narutoninjasaga.e.r.b(lVar, this.I.a[i2].a, com.donglh.narutoninjasaga.e.aw.a(this.I.a[i2].d), (this.aM / 2) + this.I.a[i2].b + 90, a_() + Input.Keys.CONTROL_RIGHT + this.I.a[i2].c, 33);
        }
        lVar.i = false;
        a(lVar, this.F[2]);
        for (int i3 = 0; i3 < this.F[2].g; i3++) {
            kk.a(lVar, "-  " + ((String) this.K.get(i3)), 7, 7 + (i3 * this.F[2].f), 0, -1);
        }
        b(lVar);
        this.F[2].d(lVar, -12, -10);
    }

    @Override // com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        Vector c = super.c();
        if (this.i.b < 2) {
            if (this.i.b == 0) {
                if (!com.donglh.narutoninjasaga.e.n.n().M() || com.donglh.narutoninjasaga.e.n.n().A == 49 || com.donglh.narutoninjasaga.e.n.n().A == 101) {
                    c.addElement(this.x.a(5001, this));
                }
            } else if (this.i.b == 1) {
                for (int i = 0; i < com.donglh.narutoninjasaga.e.f.c().D.length; i++) {
                    if (com.donglh.narutoninjasaga.e.f.c().D[i].a >= 0 && com.donglh.narutoninjasaga.e.f.c().D[i].a < com.donglh.narutoninjasaga.e.f.c().N.length) {
                        int i2 = (com.donglh.narutoninjasaga.e.f.c().D[i].b + this.h[this.i.b].aY) - 8;
                        int i3 = (com.donglh.narutoninjasaga.e.f.c().D[i].c + this.h[this.i.b].aZ) - 8;
                        int i4 = i2 + 16;
                        int i5 = i3 + 16;
                        if (com.donglh.narutoninjasaga.e.aw.a(i2, i3, i4, i5, 4, a_(), 4 + (this.aM - 8), a_() + (this.aN - 33))) {
                            if (i2 < 4) {
                                i2 = 4;
                            }
                            if (i3 < a_()) {
                                i3 = a_();
                            }
                            if (i4 > 4 + (this.aM - 8)) {
                                i4 = 4 + (this.aM - 8);
                            }
                            if (i5 > a_() + (this.aN - 33)) {
                                i5 = a_() + (this.aN - 33);
                            }
                            c.addElement(new gu(i + 4000, i2, i3, i4, i5, this.h[this.i.b], this));
                        }
                    }
                }
            }
            if (this.h[this.i.b] != null) {
                c.addElement(new gu(-1001, this.h[this.i.b].aY, this.h[this.i.b].aZ, this.h[this.i.b].aY + this.h[this.i.b].aM, this.h[this.i.b].aZ + this.h[this.i.b].aN, this.h[this.i.b], this));
            }
        } else if (this.i.b == 2) {
            c.addElement(new gu(-2001, (4 + (this.aM - 8)) - 42, (a_() + (this.aN - 33)) - 17, (4 + (this.aM - 8)) - 8, (a_() + (this.aN - 33)) - 7, null, this));
            for (int i6 = 0; i6 < this.E.length; i6++) {
                c.addElement(this.E[i6].a(8880, this));
            }
        } else if (this.i.b == 3) {
            if (this.H) {
                c.addElement(this.F[1].a(9981, this));
                if (this.F[2] != null) {
                    c.addElement(this.F[2].a(9982, this));
                }
            } else {
                c.addElement(this.F[0].a(9980, this));
            }
        }
        return c;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v34, types: [com.donglh.narutoninjasaga.d.jj] */
    /* JADX WARN: Type inference failed for: r0v35, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v39, types: [com.donglh.narutoninjasaga.e.ak] */
    @Override // com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        try {
            super.a(guVar, i, i2);
            if (com.donglh.narutoninjasaga.e.f.c().D != null && guVar.b >= 4000 && guVar.b < 4000 + com.donglh.narutoninjasaga.e.f.c().D.length) {
                int i3 = guVar.b - 4000;
                if (i3 == this.z) {
                    this.y = 12;
                }
                this.z = i3;
                d.a().y();
                com.donglh.narutoninjasaga.e.n.n().aF = null;
                return;
            }
            switch (guVar.b) {
                case -2001:
                    db.a().h();
                    this.n = a(com.donglh.narutoninjasaga.c.a.pM, this, 296, this.aN - 33, (int) HttpStatus.SC_RESET_CONTENT);
                    return;
                case -1001:
                    if (this.i.b == 0) {
                        this.y = 12;
                        com.donglh.narutoninjasaga.e.n.n().aF = null;
                        d.a().y();
                        ga gaVar = (ga) guVar.j;
                        int i4 = (i - guVar.j.aY) + gaVar.d;
                        int i5 = (i2 - guVar.j.aZ) + gaVar.n;
                        this.C = new gt(Input.Keys.NUMPAD_2, i4, i5, 1);
                        d.a().a(i4 * com.donglh.narutoninjasaga.e.n.n().E, i5 * com.donglh.narutoninjasaga.e.n.n().E);
                        return;
                    }
                    return;
                case 1011:
                    if (d.a().I != 0) {
                        com.donglh.narutoninjasaga.e.n.n();
                        com.donglh.narutoninjasaga.e.n.b(this.L[this.J]);
                        p();
                        return;
                    }
                    gm.a().a(com.donglh.narutoninjasaga.c.a.pG, kk.c, -2560);
                    return;
                case 5001:
                    if (guVar.j.i >= 0) {
                        int i6 = guVar.j.i;
                        ?? r0 = this;
                        try {
                            if (i6 != r0.b) {
                                com.donglh.narutoninjasaga.e.n.ai = true;
                                com.donglh.narutoninjasaga.e.ak akVar = new com.donglh.narutoninjasaga.e.ak((byte) -7);
                                akVar.a(i6);
                                r0 = akVar;
                                r0.l();
                                return;
                            }
                            return;
                        } catch (Exception e) {
                            com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                            return;
                        }
                    }
                    return;
                case 9980:
                    if (guVar.j.i >= 0) {
                        this.J = guVar.j.i;
                        String[] strArr = this.M[this.J];
                        this.K.clear();
                        if (strArr == null) {
                            this.F[2] = null;
                        } else {
                            for (String str : strArr) {
                                this.K.addAll(kk.a(kk.c, str, (this.aM - 8) - 160));
                            }
                            this.F[2] = new fw((byte) 1, 18, a_() + 48, (this.aM - 8) - Input.Keys.END, Input.Keys.BUTTON_R2, 15, this.K.size());
                        }
                        this.I = new jt((byte) 0, this.L[this.J], 0, 0).a().b[0];
                        this.H = true;
                        return;
                    }
                    return;
                case 9981:
                    this.H = false;
                    return;
                default:
                    return;
            }
        } catch (Exception unused) {
        }
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a(int i, Object obj, co coVar) {
        if (i == -2000 && this.B.b.b > 0) {
            com.donglh.narutoninjasaga.e.n.n();
            if (com.donglh.narutoninjasaga.e.n.e(this.B.b.d[this.B.b.b])) {
                p();
            } else {
                com.donglh.narutoninjasaga.e.n.n().b(com.donglh.narutoninjasaga.e.aw.b(com.donglh.narutoninjasaga.c.a.oO, this.B.b.c[this.B.b.b]), -1);
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a_(int i) {
    }

    @Override // com.donglh.narutoninjasaga.d.el
    public final void a(int i) {
        if (this.i.b == i) {
            super.a(i);
            return;
        }
        super.a(i);
        switch (i) {
            case 0:
                this.D = 0;
                return;
            case 1:
                this.D = 1;
                return;
            default:
                return;
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v34, types: [int] */
    private void v() {
        short s = this.aM;
        int i = this.aN - (this.aN - 33);
        try {
            f = com.donglh.narutoninjasaga.e.c.i("mapmini_0");
            g = com.donglh.narutoninjasaga.e.c.i("mapmini_1");
            com.donglh.narutoninjasaga.e.c.a(f);
            s = com.donglh.narutoninjasaga.e.r.a(f);
            i = com.donglh.narutoninjasaga.e.r.b(f);
        } catch (Exception unused) {
        }
        if (this.h[1] == null) {
            this.h[1] = new ga((byte) 2, 10, a_() + 4, (this.aM - 8) - 12, (this.aN - 33) - 22, s, i);
            this.h[1].h = s / 2;
            this.h[1].o = i / 2;
            for (int i2 = 0; i2 < com.donglh.narutoninjasaga.e.f.c().D.length; i2++) {
                if (com.donglh.narutoninjasaga.e.f.c().D[i2].a == com.donglh.narutoninjasaga.e.n.n().A) {
                    this.h[1].h = com.donglh.narutoninjasaga.e.f.c().D[i2].b - (this.h[1].aM / 2);
                    this.h[1].o = com.donglh.narutoninjasaga.e.f.c().D[i2].c - (this.h[1].aN / 2);
                    return;
                }
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.ej
    public final void d() {
        if (f != null) {
            f.a();
            f = null;
        }
        if (g != null) {
            g.a();
            g = null;
        }
    }

    private static int b(int i) {
        int i2 = 0;
        switch (i) {
            case Input.Keys.PERIOD /* 56 */:
                i2 = 46;
                break;
            case Input.Keys.ALT_LEFT /* 57 */:
                i2 = 20;
                break;
            case Input.Keys.TAB /* 61 */:
                i2 = 16;
                break;
            case Input.Keys.SPACE /* 62 */:
                i2 = 10;
                break;
            case Input.Keys.SYM /* 63 */:
                i2 = 28;
                break;
            case 64:
                i2 = 22;
                break;
            case Input.Keys.ENVELOPE /* 65 */:
                i2 = 30;
                break;
            case Input.Keys.ENTER /* 66 */:
                i2 = 14;
                break;
            case Input.Keys.EQUALS /* 70 */:
                i2 = 36;
                break;
            case Input.Keys.LEFT_BRACKET /* 71 */:
                i2 = 32;
                break;
            case Input.Keys.RIGHT_BRACKET /* 72 */:
                i2 = 34;
                break;
            case Input.Keys.BACKSLASH /* 73 */:
                i2 = 60;
                break;
            case Input.Keys.SEMICOLON /* 74 */:
                i2 = 52;
                break;
            case Input.Keys.APOSTROPHE /* 75 */:
                i2 = 1;
                break;
            case Input.Keys.SLASH /* 76 */:
                i2 = 58;
                break;
            case Input.Keys.AT /* 77 */:
                i2 = 24;
                break;
            case Input.Keys.NUM /* 78 */:
                i2 = 44;
                break;
            case Input.Keys.HEADSETHOOK /* 79 */:
                i2 = 50;
                break;
            case Input.Keys.FOCUS /* 80 */:
                i2 = 42;
                break;
            case Input.Keys.PLUS /* 81 */:
                i2 = 48;
                break;
            case Input.Keys.MENU /* 82 */:
                i2 = 5;
                break;
            case Input.Keys.NOTIFICATION /* 83 */:
                i2 = 9;
                break;
            case Input.Keys.MEDIA_NEXT /* 87 */:
                i2 = 40;
                break;
            case Input.Keys.MEDIA_PREVIOUS /* 88 */:
                i2 = 56;
                break;
            case Input.Keys.BUTTON_R1 /* 103 */:
                i2 = 62;
                break;
            case Input.Keys.BUTTON_L2 /* 104 */:
                i2 = 66;
                break;
            case Input.Keys.BUTTON_R2 /* 105 */:
                i2 = 68;
                break;
            case Input.Keys.BUTTON_THUMBL /* 106 */:
                i2 = 70;
                break;
        }
        return i2;
    }

    public final void u() {
        this.E = new fw[2];
        this.E[0] = new fw((byte) 1, 6, a_() + 9, (this.aM - 8) - Input.Keys.BUTTON_START, 182, 13, 12);
        this.E[1] = new fw((byte) 1, (4 + (this.aM - 8)) - 90, a_() + 27, 85, 156, 13, com.donglh.narutoninjasaga.e.n.n().b.size());
    }
}
