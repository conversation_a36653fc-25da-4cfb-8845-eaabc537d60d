package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.Input;
import java.util.Vector;
/* compiled from: LangLa_ay.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/at.class */
public final class at extends em {
    private Vector a = new Vector();
    private hw b;
    private fw c;
    private int d;
    private long e;
    private String f;
    private int g;

    public at(com.donglh.narutoninjasaga.e.ai aiVar, com.donglh.narutoninjasaga.e.ak akVar) {
        try {
            this.s = aiVar;
            d(316, 213);
            this.u = false;
            this.e = com.donglh.narutoninjasaga.e.aw.a();
            this.d = akVar.b.a.readInt();
            this.f = akVar.j();
            int readByte = akVar.b.a.readByte();
            for (int i = 0; i < readByte; i++) {
                fa faVar = new fa();
                faVar.a = akVar.j();
                faVar.b = akVar.j();
                faVar.c = akVar.b.a.readByte();
                this.a.add(faVar);
            }
            this.b = new hw(new gz(1001, new String[]{this.f}), 4, a_(), this.aM - 8, 76, this);
            this.c = new fw((byte) 1, 4, this.b.d() + 28, this.aM - 8, Input.Keys.BUTTON_START, 18, this.a.size());
            this.b.a(0);
        } catch (Exception unused) {
        }
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void b() {
        this.b.b();
        this.c.a();
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v3, types: [int] */
    /* JADX WARN: Type inference failed for: r0v4, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v7, types: [com.donglh.narutoninjasaga.e.ak] */
    /* JADX WARN: Type inference failed for: r0v8, types: [com.donglh.narutoninjasaga.e.ak] */
    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a(int i, Object obj, co coVar) {
        switch (i) {
            case 1002:
                return;
            case 2002:
                try {
                    fa faVar = (fa) this.a.get(((gz) obj).a);
                    String str = "";
                    if (this.g >= 70 && this.g <= 150) {
                        str = faVar.a;
                    } else if (this.g >= 180 && this.g <= 260) {
                        str = faVar.b;
                    }
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.h(str);
                    return;
                } catch (Exception unused) {
                    return;
                }
            case 2003:
                try {
                    fa faVar2 = (fa) this.a.get(((gz) obj).a);
                    String str2 = "";
                    if (this.g >= 70 && this.g <= 150) {
                        str2 = faVar2.a;
                    } else if (this.g >= 180 && this.g <= 260) {
                        str2 = faVar2.b;
                    }
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.e(str2);
                    return;
                } catch (Exception unused2) {
                    return;
                }
            case 2004:
                try {
                    fa faVar3 = (fa) this.a.get(((gz) obj).a);
                    String str3 = "";
                    if (this.g >= 70 && this.g <= 150) {
                        str3 = faVar3.a;
                    } else if (this.g >= 180 && this.g <= 260) {
                        str3 = faVar3.b;
                    }
                    ho.c = str3;
                    com.donglh.narutoninjasaga.e.n.n().a(str3, new hk(com.donglh.narutoninjasaga.e.n.n(), 6));
                    return;
                } catch (Exception unused3) {
                    return;
                }
            case 2005:
                ?? r0 = ((gz) obj).a;
                try {
                    if (r0 == com.donglh.narutoninjasaga.e.n.n().D) {
                        return;
                    }
                    com.donglh.narutoninjasaga.e.n.ai = true;
                    ?? akVar = new com.donglh.narutoninjasaga.e.ak((byte) -7);
                    akVar.a(r0);
                    r0 = akVar;
                    r0.l();
                    return;
                } catch (Exception e) {
                    com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                    return;
                }
            default:
                return;
        }
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a_(int i) {
    }

    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        Vector c = super.c();
        c.addElement(this.b.c());
        c.addElement(this.c.a(1003, this));
        return c;
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        a(lVar, (int) this.aY, (int) this.aZ, (int) this.aM, (int) this.aN, com.donglh.narutoninjasaga.c.a.bT, (byte) 2, false);
        this.b.a(lVar);
        b(lVar, this.c);
        a(lVar, 0, -28, this.c.aM, 28, 4, 55, 56);
        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.kf, 40, -15, 2, -10831436, -16777216);
        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.ju, Input.Keys.BUTTON_MODE, -15, 2, -10831436, -16777216);
        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.ju, 210, -15, 2, -10831436, -16777216);
        a(lVar, this.c);
        for (int i = 0; i < this.c.g; i++) {
            if (this.c.b(i)) {
                fa faVar = (fa) this.a.get(i);
                if (i == this.c.i) {
                    lVar.e(13136426);
                    lVar.c(0, i * this.c.f, this.c.aM, this.c.f);
                }
                kk.b(kk.c, lVar, new StringBuilder().append(i + 1).toString(), 40, 8 + (i * this.c.f), 2, -3604601, -16777216);
                kk.b(kk.c, lVar, faVar.a, Input.Keys.BUTTON_MODE, 8 + (i * this.c.f), 2, -3604601, -16777216);
                kk.b(kk.c, lVar, faVar.b, 210, 8 + (i * this.c.f), 2, -3604601, -16777216);
                if (faVar.c == 1) {
                    kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.kg, 160, 8 + (i * this.c.f), 2, -3604601, -16777216);
                } else if (faVar.c == 2) {
                    kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.kh, 160, 8 + (i * this.c.f), 2, -3604601, -16777216);
                } else {
                    kk.b(kk.c, lVar, "VS", 160, 8 + (i * this.c.f), 2, -3604601, -16777216);
                }
            }
        }
        b(lVar);
        kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.ki + this.f + com.donglh.narutoninjasaga.c.a.kj + com.donglh.narutoninjasaga.e.aw.g(this.d - ((int) ((com.donglh.narutoninjasaga.e.aw.a() - this.e) / 1000))), this.aM / 2, this.aN - 18, 2, -1, -16777216);
    }

    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        switch (guVar.b) {
            case 1001:
                return;
            case 1003:
                if (guVar.j.i >= 0) {
                    int i3 = guVar.j.i;
                    this.g = i;
                    if ((i >= 70 && i <= 150) || (i >= 180 && i <= 260)) {
                        Vector vector = new Vector();
                        vector.addElement(new hz(2002, com.donglh.narutoninjasaga.c.a.ca));
                        vector.addElement(new hz(2003, com.donglh.narutoninjasaga.c.a.cb));
                        vector.addElement(new hz(2004, com.donglh.narutoninjasaga.c.a.cc));
                        vector.addElement(new hz(2005, com.donglh.narutoninjasaga.c.a.ob));
                        if (vector.size() > 0) {
                            String[] strArr = new String[vector.size()];
                            short[] sArr = new short[vector.size()];
                            for (int i4 = 0; i4 < vector.size(); i4++) {
                                hz hzVar = (hz) vector.elementAt(i4);
                                strArr[i4] = hzVar.b;
                                sArr[i4] = (short) hzVar.a;
                            }
                            this.n = a(this, i + 25, i2, new gz(i3, sArr, strArr));
                            return;
                        }
                        return;
                    }
                    return;
                }
                return;
            default:
                return;
        }
    }
}
