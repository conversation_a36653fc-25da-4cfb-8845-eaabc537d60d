package com.donglh.narutoninjasaga.d;
/* compiled from: ItemMap.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/r.class */
public final class r extends j {
    public q a;
    public short b;
    public short c;
    private short g;
    private short h;
    public boolean d;
    private byte i = 70;
    private int[] j = {41, 42, 43};
    private int k = 0;
    public int e = 0;
    public int f;

    public r() {
        a_(25, 25);
        this.aN = (short) 17;
    }

    public final void a(int i, int i2, int i3, int i4) {
        this.b = (short) i;
        this.c = (short) i2;
        g(i3, i4);
        int b = com.donglh.narutoninjasaga.e.aw.b(i - i3);
        int b2 = com.donglh.narutoninjasaga.e.aw.b(i2 - i4);
        this.g = (short) (b / 6);
        this.h = (short) (b2 / 6);
        if (this.g <= 0) {
            this.g = (short) 1;
        }
        if (this.h <= 0) {
            this.h = (short) 1;
        }
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final int d() {
        return this.aY - (this.aM / 2);
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final int e() {
        return this.aY + (this.aM / 2);
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final int f() {
        return this.aZ - this.aN;
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final int g() {
        return this.aZ;
    }

    public final void a() {
        if (this.e > 0) {
            this.e--;
            if (this.e == 1) {
                com.donglh.narutoninjasaga.e.n.n().I.remove(this);
            }
        }
        if (this.aY != this.b) {
            if (com.donglh.narutoninjasaga.e.aw.b(this.aY - this.b) < this.g) {
                this.aY = this.b;
            } else if (this.aY > this.b) {
                this.aY = (short) (this.aY - this.g);
            } else {
                this.aY = (short) (this.aY + this.g);
            }
        }
        if (this.aZ != this.c) {
            if (com.donglh.narutoninjasaga.e.aw.b(this.aZ - this.c) < this.h) {
                this.aZ = this.c;
            } else if (this.aZ > this.c) {
                this.aZ = (short) (this.aZ - this.h);
            } else {
                this.aZ = (short) (this.aZ + this.h);
            }
        }
        if (this.d) {
            if (this.aY == this.b && this.aZ == this.c) {
                com.donglh.narutoninjasaga.e.n.n().I.remove(this);
            } else {
                this.i = (byte) (this.i + 5);
                if (this.i > 100) {
                    this.i = (byte) 100;
                }
            }
        }
        if (this.a != null && this.a.h().a == 224 && com.donglh.narutoninjasaga.e.f.c().i % 3 == 0) {
            this.k++;
            if (this.k >= this.j.length) {
                this.k = 0;
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final void a(l lVar) {
        a.a(lVar, (int) this.a.h().h, 0, (int) this.aY, (int) this.aZ, (int) this.i, (byte) 33);
        if (this.a.h().a == 224) {
            com.donglh.narutoninjasaga.e.r.a(lVar, this.j[this.k], 0, this.aY, this.aZ, 33);
        }
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final String A() {
        return this.a.h().b;
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final String B() {
        return null;
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final String C() {
        return null;
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final void b(l lVar) {
        if (com.donglh.narutoninjasaga.e.n.n().as != null && com.donglh.narutoninjasaga.e.n.n().as.equals(this)) {
            kk.b(kk.c, lVar, this.a.h().b, this.aY, (this.aZ - this.aN) - 8, 2, -16646423, -16777216);
            com.donglh.narutoninjasaga.e.n.n().ax.g(this.aY, (this.aZ - this.aN) - 20);
            com.donglh.narutoninjasaga.e.n.n().ax.b(lVar, 0, 0);
        }
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final int N() {
        return 3;
    }
}
