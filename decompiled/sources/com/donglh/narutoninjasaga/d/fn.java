package com.donglh.narutoninjasaga.d;

import java.util.Vector;
/* JADX INFO: Access modifiers changed from: package-private */
/* compiled from: LangLa_hw.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/fn.class */
public final class fn implements Runnable {
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v2, types: [java.lang.Object] */
    /* JADX WARN: Type inference failed for: r0v3, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v7, types: [java.lang.Object] */
    @Override // java.lang.Runnable
    public final void run() {
        ki a;
        ki a2;
        boolean z = false;
        while (true) {
            if (z) {
                z = false;
            } else {
                ?? f = jr.f();
                synchronized (f) {
                    try {
                        jr.a(1);
                        f = jr.f();
                        f.wait(1000000000L);
                    } catch (Exception e) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) f);
                    }
                }
            }
            try {
                if (jr.g()) {
                    jr.a(0);
                    ki f2 = ki.f(jr.h(), jr.i());
                    ki f3 = ki.f(jr.j(), jr.k());
                    if (!d.a().c(jr.h(), jr.i()) && (a2 = d.a().a(f2, 10, true)) != null) {
                        jr.b(a2.aY);
                        jr.c(a2.aZ);
                        f2 = a2;
                    }
                    if (!d.a().c(jr.j(), jr.k()) && (a = d.a().a(f3, 10, true)) != null) {
                        jr.d(a.aY);
                        jr.e(a.aZ);
                        f3 = a;
                    }
                    Vector a3 = jr.a(f2, f3, false);
                    Vector vector = a3;
                    if (a3 == null) {
                        int i = 0;
                        int i2 = 0;
                        int i3 = 0;
                        while (true) {
                            if (i3 >= com.donglh.narutoninjasaga.e.n.n().e.size() || jr.l() == 2) {
                                break;
                            }
                            ki kiVar = null;
                            ki kiVar2 = null;
                            for (int i4 = 0; i4 < ((b) com.donglh.narutoninjasaga.e.n.n().e.get(i3)).a.length - 1 && jr.l() != 2; i4++) {
                                ki kiVar3 = ((b) com.donglh.narutoninjasaga.e.n.n().e.get(i3)).a[i4];
                                ki kiVar4 = ((b) com.donglh.narutoninjasaga.e.n.n().e.get(i3)).a[i4 + 1];
                                ki a4 = com.donglh.narutoninjasaga.e.aw.a(f2, kiVar3, kiVar4);
                                if (jr.l() == 2) {
                                    break;
                                }
                                if (a4 != null && !com.donglh.narutoninjasaga.e.n.n().a(f2, a4)) {
                                    if (kiVar2 == null) {
                                        kiVar2 = a4;
                                        i = i4;
                                    } else if (com.donglh.narutoninjasaga.e.aw.a(f2.aY, f2.aZ, a4.aY, a4.aZ) < com.donglh.narutoninjasaga.e.aw.a(f2.aY, f2.aZ, kiVar2.aY, kiVar2.aZ)) {
                                        kiVar2 = a4;
                                        i = i4;
                                    }
                                }
                                if (jr.l() == 2) {
                                    break;
                                }
                                if (kiVar3 != null && !com.donglh.narutoninjasaga.e.n.n().a(f2, kiVar3)) {
                                    if (kiVar2 == null) {
                                        kiVar2 = kiVar3;
                                        i = i4;
                                    } else if (com.donglh.narutoninjasaga.e.aw.a(f2.aY, f2.aZ, kiVar3.aY, kiVar3.aZ) < com.donglh.narutoninjasaga.e.aw.a(f2.aY, f2.aZ, kiVar2.aY, kiVar2.aZ)) {
                                        kiVar2 = kiVar3;
                                        i = i4;
                                    }
                                }
                                if (jr.l() == 2) {
                                    break;
                                }
                                if (kiVar4 != null && !com.donglh.narutoninjasaga.e.n.n().a(f2, kiVar4)) {
                                    if (kiVar2 == null) {
                                        kiVar2 = kiVar4;
                                        i = i4;
                                    } else if (com.donglh.narutoninjasaga.e.aw.a(f2.aY, f2.aZ, kiVar4.aY, kiVar4.aZ) < com.donglh.narutoninjasaga.e.aw.a(f2.aY, f2.aZ, kiVar2.aY, kiVar2.aZ)) {
                                        kiVar2 = kiVar4;
                                        i = i4;
                                    }
                                }
                                if (jr.l() == 2) {
                                    break;
                                }
                                ki a5 = com.donglh.narutoninjasaga.e.aw.a(f3, kiVar3, kiVar4);
                                if (a5 != null && !com.donglh.narutoninjasaga.e.n.n().a(f3, a5)) {
                                    if (kiVar == null) {
                                        kiVar = a5;
                                        i2 = i4;
                                    } else if (com.donglh.narutoninjasaga.e.aw.a(f3.aY, f3.aZ, a5.aY, a5.aZ) < com.donglh.narutoninjasaga.e.aw.a(f3.aY, f3.aZ, kiVar.aY, kiVar.aZ)) {
                                        kiVar = a5;
                                        i2 = i4;
                                    }
                                }
                                if (jr.l() == 2) {
                                    break;
                                }
                                if (kiVar3 != null && !com.donglh.narutoninjasaga.e.n.n().a(f3, kiVar3)) {
                                    if (kiVar == null) {
                                        kiVar = kiVar3;
                                        i2 = i4;
                                    } else if (com.donglh.narutoninjasaga.e.aw.a(f3.aY, f3.aZ, kiVar3.aY, kiVar3.aZ) < com.donglh.narutoninjasaga.e.aw.a(f3.aY, f3.aZ, kiVar.aY, kiVar.aZ)) {
                                        kiVar = kiVar3;
                                        i2 = i4;
                                    }
                                }
                                if (jr.l() == 2) {
                                    break;
                                }
                                if (kiVar4 != null && !com.donglh.narutoninjasaga.e.n.n().a(f3, kiVar4)) {
                                    if (kiVar == null) {
                                        kiVar = kiVar4;
                                        i2 = i4;
                                    } else if (com.donglh.narutoninjasaga.e.aw.a(f3.aY, f3.aZ, kiVar4.aY, kiVar4.aZ) < com.donglh.narutoninjasaga.e.aw.a(f3.aY, f3.aZ, kiVar.aY, kiVar.aZ)) {
                                        kiVar = kiVar4;
                                        i2 = i4;
                                    }
                                }
                            }
                            if (jr.l() == 2) {
                                z = true;
                            } else if (kiVar2 != null && kiVar != null) {
                                Vector vector2 = new Vector();
                                vector = vector2;
                                vector2.addElement(f2);
                                vector.addElement(kiVar2);
                                if (i < i2) {
                                    for (int i5 = i + 1; i5 <= i2 && jr.l() != 2; i5++) {
                                        vector.addElement(((b) com.donglh.narutoninjasaga.e.n.n().e.get(i3)).a[i5]);
                                    }
                                } else {
                                    for (int i6 = i; i6 > i2 && jr.l() != 2; i6--) {
                                        vector.addElement(((b) com.donglh.narutoninjasaga.e.n.n().e.get(i3)).a[i6]);
                                    }
                                }
                                vector.addElement(kiVar);
                                vector.addElement(f3);
                            }
                            i3++;
                        }
                    }
                    if (vector != null) {
                        jr.m().addAll(vector);
                    }
                    if (!jr.g()) {
                        jr.m().clear();
                    }
                }
            } catch (Exception unused) {
            }
        }
    }
}
