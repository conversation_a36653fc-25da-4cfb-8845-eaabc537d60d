package com.donglh.narutoninjasaga.d;

import java.util.Vector;
/* compiled from: LangLa_av.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/aq.class */
public final class aq extends em {
    private kn a;
    private q b;

    public aq(com.donglh.narutoninjasaga.e.ai aiVar, q qVar) {
        this.j = (byte) 2;
        this.s = aiVar;
        this.b = qVar;
        this.u = false;
        d(180, 120);
        this.a = a(54, 43, 60, "", this, 2);
        this.a.j = 1;
        this.a.a("1");
        a(this.a);
        a(119, 46, "", this, 1001, 58);
        a(140, 46, "", this, 1002, 57);
        dg a = a(com.donglh.narutoninjasaga.c.a.e, 1003);
        a.a(-8);
        a.g(a.aY - 27, a.aZ);
        dg a2 = a(com.donglh.narutoninjasaga.c.a.t, -7);
        a2.a(-8);
        a2.g(a2.aY + 40, a2.aZ);
    }

    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.cn
    public final void a(int i, Object obj) {
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a_(int i) {
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a(int i, Object obj, co coVar) {
    }

    @Override // com.donglh.narutoninjasaga.d.ej
    public final int a_() {
        return 5;
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        a(lVar, 0, 0, this.aM, this.aN, 80, 55, 56);
        kk.b(kk.e, lVar, com.donglh.narutoninjasaga.c.a.mb, 9, 20, 0, -1, -16777216);
        kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.al, 9, 55, 0, -10831436, -16777216);
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void b() {
    }

    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        super.c();
        Vector vector = new Vector();
        vector.addElement(new gu(0, 0, 0, this.aM, this.aN, this.t, this));
        return vector;
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        switch (guVar.b) {
            case 1001:
                int j = this.a.j() - 1;
                int i3 = j;
                if (j <= 0) {
                    i3 = 1;
                }
                this.a.a(i3);
                return;
            case 1002:
                this.a.a(this.a.j() + 1);
                return;
            case 1003:
                try {
                    if (this.a.j() >= 0 && this.a.j() <= this.b.O()) {
                        p();
                        for (int i4 = 0; i4 < this.a.j(); i4++) {
                            com.donglh.narutoninjasaga.e.n.n();
                            com.donglh.narutoninjasaga.e.n.a(this.b);
                        }
                        return;
                    }
                    com.donglh.narutoninjasaga.e.n.n().b(com.donglh.narutoninjasaga.c.a.md, -65536);
                    return;
                } catch (Exception e) {
                    com.donglh.narutoninjasaga.e.aw.a((Exception) this);
                    return;
                }
            default:
                return;
        }
    }
}
