package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.Input;
/* compiled from: LangLa_kg.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/hb.class */
public final class hb extends em {
    private kn c;
    private kn d;
    private kn e;
    private int f;
    private int g;
    public String a;
    public String b;
    private static hb h;

    public hb(com.donglh.narutoninjasaga.e.ai aiVar) {
        this.s = aiVar;
        h = this;
        d(210, Input.Keys.NUMPAD_3);
        this.f = 68;
        this.g = 35;
        this.c = a(74, 34, 124, "", this, 0);
        this.c.a = 16;
        this.d = a(74, 60, 124, "", this, 1);
        this.e = a(74, 86, 124, "", this, 1);
        a(com.donglh.narutoninjasaga.c.a.e, 1002);
    }

    public static hb e() {
        return h;
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a_(int i) {
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a(int i, Object obj, co coVar) {
    }

    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        switch (guVar.b) {
            case 1002:
                this.s.U();
                this.s.U();
                this.a = this.c.n();
                this.b = this.e.n();
                jp.r();
                com.donglh.narutoninjasaga.e.ak akVar = null;
                try {
                    try {
                        com.donglh.narutoninjasaga.e.ak q = q();
                        akVar = q;
                        q.l();
                        if (akVar != null) {
                            akVar.c();
                            return;
                        }
                    } catch (Exception e) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) null);
                        if (akVar != null) {
                            akVar.c();
                            return;
                        }
                    }
                    return;
                } catch (Throwable th) {
                    if (akVar != null) {
                        akVar.c();
                    }
                    throw th;
                }
            default:
                return;
        }
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        a(lVar, (int) this.aY, (int) this.aZ, (int) this.aM, (int) this.aN, com.donglh.narutoninjasaga.c.a.ab, (byte) 2, true);
        kk.a(kk.c, lVar, com.donglh.narutoninjasaga.c.a.qL[0], 15, this.g + 10, 0, -1);
        kk.a(kk.c, lVar, com.donglh.narutoninjasaga.c.a.ac, 15, this.g + 25 + 10, 0, -1);
        kk.a(kk.c, lVar, com.donglh.narutoninjasaga.c.a.ad, 15, this.g + 50 + 10, 0, -1);
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void b() {
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v0 */
    /* JADX WARN: Type inference failed for: r0v1, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v8, types: [com.donglh.narutoninjasaga.e.ak] */
    private com.donglh.narutoninjasaga.e.ak q() {
        ?? r0 = 0;
        com.donglh.narutoninjasaga.e.ak akVar = null;
        try {
            com.donglh.narutoninjasaga.e.ak b = com.donglh.narutoninjasaga.e.ak.b((byte) -124);
            akVar = b;
            b.a(this.a);
            akVar.a(this.d.n());
            akVar.a(this.b);
            akVar.c(com.donglh.narutoninjasaga.e.f.c().w);
            r0 = akVar;
            r0.c(com.donglh.narutoninjasaga.e.f.c().x);
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
        }
        return akVar;
    }
}
