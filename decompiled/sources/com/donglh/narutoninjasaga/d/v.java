package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.Input;
import com.badlogic.gdx.net.HttpStatus;
import java.util.Vector;
/* compiled from: LangLa_a2.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/v.class */
public final class v extends he {
    public q[] a;
    private fx[] f;
    private dg g;
    public int b;
    public int c;
    private int h;
    public boolean d;
    public gt e;

    public v(com.donglh.narutoninjasaga.e.ai aiVar) {
        super(aiVar, new String[]{com.donglh.narutoninjasaga.c.a.ba, com.donglh.narutoninjasaga.c.a.F});
        this.a = new q[3];
        this.f = new fx[3];
        this.c = 0;
        this.f[0] = new fx((byte) 1, 94, a_() + 20, 30, 30, 30, 1, 1);
        this.f[1] = new fx((byte) 1, 174, a_() + 20, 30, 30, 30, 1, 1);
        this.f[2] = new fx((byte) 1, 134, a_() + 65, 30, 30, 30, 1, 1);
        this.g = a(this.aM - 70, this.aN - 33, com.donglh.narutoninjasaga.c.a.ba, this, 0, -8);
        a(this.g, 0);
    }

    @Override // com.donglh.narutoninjasaga.d.et
    public final void a() {
        this.A = new q[d.a().W.length];
        int i = 0;
        for (int i2 = 0; i2 < d.a().W.length; i2++) {
            if (d.a().W[i2] != null && d.a().W[i2].C()) {
                this.A[i] = d.a().W[i2];
                i++;
            }
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void b() {
        try {
            super.b();
            if (this.i.b == 0) {
                if (this.a[2] != null && this.a[0] != null && this.a[0].c < 925) {
                    if (this.a[2].c != this.a[0].c + 14 || this.a[0].p != 19) {
                        if (this.a[2].c == this.a[0].c + 13) {
                            this.a[1] = this.a[0].a();
                            this.a[1].q = true;
                            int i = this.a[1].p + 1;
                            int i2 = i;
                            if (i > 19) {
                                i2 = 19;
                            }
                            this.a[1].a(i2);
                        }
                    } else {
                        this.a[1] = this.a[0].a();
                        this.a[1].c++;
                        this.a[1].q = true;
                        this.a[1].a(0);
                    }
                } else if (!this.d) {
                    this.a[1] = null;
                }
                for (int i3 = 0; i3 < this.f.length; i3++) {
                    this.f[i3].a();
                }
            } else {
                a();
            }
            if (this.e != null) {
                this.e.b();
                if (this.e.h()) {
                    this.e = null;
                }
            }
            if (this.h > 0) {
                this.h--;
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) this);
        }
    }

    /* JADX WARN: Type inference failed for: r0v52, types: [com.donglh.narutoninjasaga.d.gt, java.lang.Exception] */
    @Override // com.donglh.narutoninjasaga.d.he, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        ?? r0;
        try {
            if (q() <= 0) {
                a(lVar, (int) this.aY, (int) this.aZ, (int) this.aM, (int) this.aN, com.donglh.narutoninjasaga.c.a.o, (byte) 2, false);
            } else {
                super.a(lVar);
            }
            if (this.i.b == 0) {
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.aX, 4 + ((this.aM - 8) / 2), a_() + 9, 2, -10831436, -16777216);
                a(lVar, this.f[0].aY, this.f[0].aZ, this.a[0], this.f[0].i >= 0, com.donglh.narutoninjasaga.c.a.tx[3]);
                a(lVar, this.f[1].aY, this.f[1].aZ, this.a[1], this.f[1].i >= 0, "Xem Thử");
                a(lVar, this.f[2].aY, this.f[2].aZ, this.a[2], this.f[2].i >= 0, "Đá");
                com.donglh.narutoninjasaga.e.r.a(lVar, 95, 0, 142 - ((com.donglh.narutoninjasaga.e.f.c().i / 3) % 3), a_() + 25, 20);
                if (this.a[0] != null) {
                    kk.b(kk.c, lVar, "(+" + ((int) this.a[0].p) + ")", 101, a_() + 60, 33, -1, -16777216);
                }
                if (this.a[1] != null) {
                    kk.b(kk.c, lVar, "(+" + ((int) this.a[1].p) + ")", 181, a_() + 60, 33, -1, -16777216);
                }
                if (this.d) {
                    if (this.c == 0) {
                        kk.c(kk.c, lVar, com.donglh.narutoninjasaga.c.a.jO, 24, a_() + Input.Keys.BUTTON_START, 0, -2560, -16777216);
                    } else {
                        kk.c(kk.c, lVar, com.donglh.narutoninjasaga.c.a.jH, 24, a_() + Input.Keys.BUTTON_START, 0, -2560, -16777216);
                    }
                } else {
                    if (this.a[1] != null && this.a[1].c == this.a[0].c) {
                        kk.c(kk.c, lVar, com.donglh.narutoninjasaga.c.a.ek + (100 - ((this.a[1].p << 1) + ((this.a[1].c - 916) << 1))) + "%", 24, a_() + Input.Keys.BUTTON_START, 0, -1, -16777216);
                        kk.c(kk.c, lVar, "- Cần " + (100 + (20 * this.a[1].p) + (10 * (this.a[1].c - 916))) + com.donglh.narutoninjasaga.c.a.el, 24, a_() + 124, 0, -1, -16777216);
                    } else if (this.a[1] != null && this.a[1].c == this.a[0].c + 1) {
                        kk.c(kk.c, lVar, com.donglh.narutoninjasaga.c.a.ek + (100 - ((this.a[1].c - 917) * 10)) + "%", 24, a_() + Input.Keys.BUTTON_START, 0, -1, -16777216);
                        kk.c(kk.c, lVar, "- Cần " + (HttpStatus.SC_INTERNAL_SERVER_ERROR * (this.a[1].c - 916)) + com.donglh.narutoninjasaga.c.a.el, 24, a_() + 124, 0, -1, -16777216);
                    }
                    kk.c(kk.c, lVar, com.donglh.narutoninjasaga.c.a.em, 24, a_() + 140, 0, -1, -16777216);
                    kk.c(kk.c, lVar, com.donglh.narutoninjasaga.c.a.ep, 24, a_() + 156, 0, -1, -16777216);
                    kk.c(kk.c, lVar, com.donglh.narutoninjasaga.c.a.eq, 24, a_() + 172, 0, -1, -16777216);
                }
                if (this.e != null) {
                    r0 = this.e;
                    r0.b(lVar, this.f[1].aY + (this.f[1].f / 2), this.f[1].aZ + (this.f[1].f / 2));
                }
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
        }
    }

    @Override // com.donglh.narutoninjasaga.d.he, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        Vector c = super.c();
        if (this.i.b == 0) {
            for (int i = 0; i < this.f.length; i++) {
                c.addElement(this.f[i].a(i + 1001, this));
            }
        }
        return c;
    }

    @Override // com.donglh.narutoninjasaga.d.he, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        if (this.i.b == q() && guVar.b == 0) {
            d();
        }
        super.a(guVar, i, i2);
        if (this.i.b == 0) {
            switch (guVar.b) {
                case 0:
                    e();
                    return;
                case 1001:
                    this.b = 1;
                    a(guVar.j, guVar.j.i);
                    if (this.a[0] == null) {
                        this.n = b(com.donglh.narutoninjasaga.c.a.et, this, guVar.j.aY + 32, guVar.j.aZ, 100);
                        return;
                    } else {
                        this.n = a(guVar, this, this.a[0]);
                        return;
                    }
                case 1002:
                    this.b = 2;
                    a(guVar.j, guVar.j.i);
                    if (this.a[1] == null) {
                        this.n = b(com.donglh.narutoninjasaga.c.a.ew, this, guVar.j.aY + 32, guVar.j.aZ, 100);
                        return;
                    } else {
                        this.n = a(guVar, this, this.a[1]);
                        return;
                    }
                case 1003:
                    this.b = 3;
                    a(guVar.j, guVar.j.i);
                    if (this.a[2] == null) {
                        this.n = b(com.donglh.narutoninjasaga.c.a.ez, this, guVar.j.aY + 32, guVar.j.aZ, 100);
                        return;
                    } else {
                        this.n = a(guVar, this, this.a[2]);
                        return;
                    }
                case 2001:
                    e();
                    return;
                default:
                    return;
            }
        }
    }

    private void a(fw fwVar, int i) {
        for (int i2 = 0; i2 < this.f.length; i2++) {
            this.f[i2].i = -1;
        }
        fwVar.i = i;
    }

    @Override // com.donglh.narutoninjasaga.d.el
    public final void a(int i) {
        super.a(i);
        if (this.d) {
            this.d = false;
            for (int i2 = 0; i2 < this.a.length; i2++) {
                this.a[i2] = null;
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.ej
    public final void d() {
        super.d();
        for (int i = 0; i < this.a.length; i++) {
            if (i != 1) {
                if (this.a[i] != null) {
                    d.a().g(this.a[i].r)[this.a[i].e] = this.a[i];
                }
            }
            this.a[i] = null;
        }
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x0082: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:15:0x0081 */
    private void e() {
        Exception a;
        try {
            if (this.d) {
                com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.eA, -2560);
            } else if (this.a[0] != null && this.a[1] != null && this.a[2] != null) {
                com.donglh.narutoninjasaga.e.ak akVar = new com.donglh.narutoninjasaga.e.ak((byte) -104);
                akVar.a(1);
                akVar.a(this.a[0].r);
                akVar.b(this.a[0].e);
                akVar.b(this.a[2].e);
                akVar.l();
            } else {
                com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.eD, -65536);
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a);
        }
    }
}
