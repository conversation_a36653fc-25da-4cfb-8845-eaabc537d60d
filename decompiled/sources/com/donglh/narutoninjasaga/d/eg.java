package com.donglh.narutoninjasaga.d;
/* JADX INFO: Access modifiers changed from: package-private */
/* compiled from: LangLa_fh.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/eg.class */
public final class eg implements Runnable {
    @Override // java.lang.Runnable
    public final void run() {
        for (int i = 0; i < com.donglh.narutoninjasaga.e.f.c().ag.size(); i++) {
            try {
                String str = (String) com.donglh.narutoninjasaga.e.f.c().ag.get(i);
                StringBuilder append = new StringBuilder().append(com.donglh.narutoninjasaga.e.f.c().aK).append(com.donglh.narutoninjasaga.e.f.c().aO.replaceAll("img", "imgios"));
                com.donglh.narutoninjasaga.e.f.c();
                byte[] c = com.donglh.narutoninjasaga.e.c.c(append.append(com.donglh.narutoninjasaga.e.f.a(str) + 1).append("/").append(str).toString());
                if (c != null) {
                    com.donglh.narutoninjasaga.e.c.a(c, true);
                }
            } catch (Exception unused) {
                com.donglh.narutoninjasaga.e.f.c().a(new com.donglh.narutoninjasaga.e.e());
                return;
            }
        }
        cp.aV = false;
        com.donglh.narutoninjasaga.e.c.a("arr_full", true);
        com.donglh.narutoninjasaga.e.f.c().a(false);
        com.donglh.narutoninjasaga.e.f.aS = 1;
        com.donglh.narutoninjasaga.c.a.b();
        com.donglh.narutoninjasaga.e.n n = com.donglh.narutoninjasaga.e.n.n();
        try {
            n.ag.a(n.A);
        } catch (Exception unused2) {
        }
        com.donglh.narutoninjasaga.e.f.c().aC = false;
    }
}
