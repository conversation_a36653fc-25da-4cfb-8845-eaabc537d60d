package com.donglh.narutoninjasaga.d;

import java.util.Vector;
/* compiled from: Npc.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/jt.class */
public final class jt extends j {
    public short a;
    private byte d;
    public Vector b = new Vector();
    public fv c;

    public jt(byte b, int i, int i2, int i3) {
        this.aG = b;
        this.a = (short) i;
        g(i2, i3);
        a_(a().c, a().d);
        if (com.donglh.narutoninjasaga.e.aw.a(2) == 0) {
            this.aF = (byte) 3;
        } else {
            this.aF = (byte) 2;
        }
        if (i == 28) {
            this.b.addElement(new gt(182, 0, 0, -1));
        }
    }

    public final com.donglh.narutoninjasaga.e.am a() {
        return com.donglh.narutoninjasaga.e.f.c().L[this.a];
    }

    public final void b() {
        ki b;
        if (this.aG != 6 && (b = d.b(this.aY, this.aZ)) != null) {
            this.aZ = b.aZ;
        }
        g(this.aY, this.aZ);
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final int d() {
        return this.aY - (this.aM / 2);
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final int e() {
        return this.aY + (this.aM / 2);
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final int f() {
        return this.aZ - this.aN;
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final int g() {
        return this.aZ;
    }

    public final void c() {
        try {
            if (this.a == 72) {
                if (com.donglh.narutoninjasaga.e.n.n().bQ.length() > 0) {
                    this.aG = (byte) 0;
                } else {
                    this.aG = (byte) 6;
                }
            }
            if (a().i <= 0) {
                a().i = 100;
            }
            if (a().j <= 0) {
                a().j = 100;
            }
            if (this.aG != 6) {
                if (this.c != null && this.c.b(P(), (this.aZ - this.aN) + 2)) {
                    this.c = null;
                }
                this.aH = (byte) (this.aH + 1);
                if (this.aH >= a().a[this.aG].length) {
                    this.aH = (byte) 0;
                }
                this.d = a().a[this.aG][this.aH];
                for (int size = this.b.size() - 1; size >= 0; size--) {
                    gt gtVar = (gt) this.b.elementAt(size);
                    gtVar.b(this);
                    if (gtVar.h()) {
                        this.b.removeElement(gtVar);
                    }
                }
            }
        } catch (Exception unused) {
        }
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final void a(l lVar) {
        try {
            if (this.aG != 6 && !com.donglh.narutoninjasaga.e.aw.f()) {
                com.donglh.narutoninjasaga.e.y yVar = a().b[this.d];
                if (this.a == 72) {
                    yVar = com.donglh.narutoninjasaga.e.f.c().L[com.donglh.narutoninjasaga.e.n.n().bR].b[this.d];
                }
                for (int i = 0; i < yVar.a.length; i++) {
                    if (this.a == 72) {
                        com.donglh.narutoninjasaga.e.r.b(lVar, yVar.a[i].a, com.donglh.narutoninjasaga.e.aw.a(yVar.a[i].d), this.aY + yVar.a[i].b, this.aZ + yVar.a[i].c, 33);
                    } else if (!j() && a().e != 235 && this.a != 72) {
                        com.donglh.narutoninjasaga.e.r.b(lVar, yVar.a[i].a, com.donglh.narutoninjasaga.e.aw.a(yVar.a[i].d), this.aY + yVar.a[i].b, this.aZ + yVar.a[i].c, 33);
                    } else {
                        com.donglh.narutoninjasaga.e.r.b(lVar, yVar.a[i].a, com.donglh.narutoninjasaga.e.aw.b(yVar.a[i].d), this.aY - yVar.a[i].b, this.aZ + yVar.a[i].c, 33);
                    }
                }
                if (this.a == 47) {
                    com.donglh.narutoninjasaga.e.r.b(lVar, 4139, 0, this.aY + 30, this.aZ, 33);
                }
            }
        } catch (Exception unused) {
        }
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final String A() {
        return this.a == 72 ? com.donglh.narutoninjasaga.e.n.n().bQ : a().g;
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final String B() {
        return a().i + "/" + a().i;
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final String C() {
        return a().j + "/" + a().j;
    }

    public final int h() {
        return (this.a % 5) + 1;
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final void b(l lVar) {
        if (a().e != 235) {
            com.donglh.narutoninjasaga.e.n.n();
            if (this.aG != 4 && this.aG != 6) {
                int i = (this.aZ - this.aN) + 2;
                if (this.a >= 2) {
                    if (this.a == 72) {
                        int i2 = i - 10;
                        kk.a(lVar, com.donglh.narutoninjasaga.e.n.n().bQ, this.aY, i2, 2, -1);
                        i = i2 - 10;
                        kk.a(kk.f, lVar, a().g, this.aY, i, 2, -16648198, 3, 0);
                    } else {
                        i -= 10;
                        kk.a(lVar, a().g, this.aY, i, 2, -1);
                        if (a().h.length() > 0) {
                            i -= 10;
                            kk.a(lVar, a().h, this.aY, i, 2, -7812062);
                        }
                    }
                }
                if (com.donglh.narutoninjasaga.e.n.n().as != null && com.donglh.narutoninjasaga.e.n.n().as.equals(this)) {
                    com.donglh.narutoninjasaga.e.n.n().ax.g(this.aY, i - 10);
                    com.donglh.narutoninjasaga.e.n.n().ax.b(lVar, 0, 0);
                }
                if (this.c != null) {
                    this.c.a(lVar);
                }
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final int N() {
        return this.a == 47 ? 4 : 3;
    }
}
