package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.Gdx;
import java.util.Vector;
/* compiled from: LangLa_x.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/jm.class */
public final class jm extends em {
    private boolean a;
    private fw b;
    private gz c;
    private int d;
    private String e;
    private com.donglh.narutoninjasaga.e.az f;

    public jm(com.donglh.narutoninjasaga.e.ai aiVar, gz gzVar, int i) {
        this.s = aiVar;
        this.c = gzVar;
        this.d = i;
        g(0, 0);
        a_(220, com.donglh.narutoninjasaga.e.f.c().p);
        a(this.aM, (byte) 1);
        a(this.aM - 28, 8, "", this, 1102, 6).g(this.aM - 28, 8);
        int length = gzVar.c.length;
        int i2 = length > 10 ? 10 : length;
        int i3 = (this.aN - 50) / i2;
        this.b = new fw((byte) 1, 2, 50, this.aM - 4, i2 * i3, i3, i2);
        this.j = (byte) 2;
        this.e = com.donglh.narutoninjasaga.e.aw.e(com.donglh.narutoninjasaga.e.f.c().x);
        if (i == 0) {
            try {
                this.f = com.donglh.narutoninjasaga.e.c.a(a.b("/4.png").c());
                this.f = a.b(this.f, (this.f.c * com.donglh.narutoninjasaga.e.f.c().u) / 4, (this.f.d * com.donglh.narutoninjasaga.e.f.c().u) / 4);
            } catch (Exception unused) {
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a(int i, Object obj, co coVar) {
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a_(int i) {
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v91 */
    /* JADX WARN: Type inference failed for: r0v92, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v98, types: [int] */
    @Override // com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        com.donglh.narutoninjasaga.e.r.a(lVar, 643, 0, 0, 3, (this.aM - 3) * com.donglh.narutoninjasaga.e.f.c().u, (this.aN - 3) * com.donglh.narutoninjasaga.e.f.c().u, 30, 30, this.aM - 3, 170, 20);
        short s = this.aN;
        short s2 = this.aM;
        int i = lVar.a;
        int i2 = lVar.b;
        lVar.a(0, 0);
        a.a(lVar, 646, 0, 0, 0, s2 - 20, 20);
        a.a(lVar, 645, -360, s2 - 20, 20, 20, s - 20);
        com.donglh.narutoninjasaga.e.r.a(lVar, 644, -360, s2, 0, 24);
        a(lVar, i, i2);
        lVar.c();
        if (this.d == 1) {
            com.donglh.narutoninjasaga.e.r.a(lVar, 92, 0, 37, 27, 3);
            com.donglh.narutoninjasaga.e.r.a(lVar, com.donglh.narutoninjasaga.e.f.c().S[d.a().b].a, 0, 37, 27, 3);
            kk.a(kk.d, lVar, com.donglh.narutoninjasaga.c.a.kP + d.a().a, 63, 10, 0, -2560, -10275328);
            kk.a(kk.d, lVar, com.donglh.narutoninjasaga.c.a.kQ + d.a().N, 63, 25, 0, -2560, -10275328);
            long j = d.a().M;
            int i3 = 0;
            while (i3 < com.donglh.narutoninjasaga.e.f.c().aA.length && j >= com.donglh.narutoninjasaga.e.f.c().aA[i3]) {
                j -= com.donglh.narutoninjasaga.e.f.c().aA[i3];
                i3++;
            }
            ?? r0 = 0;
            int i4 = 0;
            try {
                com.donglh.narutoninjasaga.e.f.c();
                r0 = (int) ((j * 100) / com.donglh.narutoninjasaga.e.f.c().aA[i3]);
                i4 = r0;
            } catch (Exception e) {
                com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
            }
            kk.a(kk.d, lVar, com.donglh.narutoninjasaga.c.a.dW + i3 + " + " + i4 + "%", 63, 40, 0, -2560, -10275328);
        } else {
            lVar.a(this.f, 15, 10);
            kk.a(kk.d, lVar, com.donglh.narutoninjasaga.c.a.a(), 52, 17, 0, -2560, -10275328);
            kk.a(kk.d, lVar, com.donglh.narutoninjasaga.c.a.kR + this.e, 52, 32, 0, -2560, -10275328);
        }
        a(lVar, this.b);
        int i5 = lVar.b;
        for (int i6 = 0; i6 < this.c.c.length; i6++) {
            if (i6 == this.b.j) {
                kk.a(kk.e, lVar, this.c.c[i6], (this.aM / 2) + 1, (this.b.f / 2) + (i6 * this.b.f) + 1, 2, -1, -10275328);
            } else {
                kk.a(kk.e, lVar, this.c.c[i6], this.aM / 2, (this.b.f / 2) + (i6 * this.b.f), 2, -1, -10275328);
            }
            a.a(lVar, 5, 0, 13, 1 + (i6 * this.b.f), this.b.aM - 30, 20);
            if (i6 == 1 && this.d == 1 && com.donglh.narutoninjasaga.e.n.n().an) {
                com.donglh.narutoninjasaga.e.r.a(lVar, 713, 0, (this.aM / 2) + 50, ((this.b.f / 2) + (i6 * this.b.f)) - 2, 20);
            }
            if (this.c.d[i6] == 12 && com.donglh.narutoninjasaga.e.n.n().aC == 0 && com.donglh.narutoninjasaga.e.n.n().aD == 1 && !i()) {
                com.donglh.narutoninjasaga.e.f.c().aE.a((this.aM / 2) + 20, i5 + (this.b.f / 2) + (i6 * this.b.f) + 1);
                com.donglh.narutoninjasaga.e.f.c().aE.a = true;
            } else if (this.c.d[i6] == 10 && com.donglh.narutoninjasaga.e.n.n().aC == 14 && com.donglh.narutoninjasaga.e.n.n().aD == 0 && !i()) {
                com.donglh.narutoninjasaga.e.f.c().aE.a((this.aM / 2) + 20, i5 + (this.b.f / 2) + (i6 * this.b.f) + 1);
                com.donglh.narutoninjasaga.e.f.c().aE.a = true;
            }
        }
        b(lVar);
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void b() {
        if (this.a && this.o == null) {
            p();
        }
        this.b.a();
    }

    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        Vector vector = new Vector();
        vector.add(this.b.a(1100, this));
        vector.add(new gu(1103, 15, 5, 58, 48, null, this));
        vector.add(new gu(1101, 0, 0, this.aM, this.aN, null, this));
        vector.add(new gu(1102, 0, 0, com.donglh.narutoninjasaga.e.f.c().o, com.donglh.narutoninjasaga.e.f.c().p, null, this));
        return vector;
    }

    @Override // com.donglh.narutoninjasaga.d.ej
    public final void d() {
        if (this.f != null) {
            this.f.a();
        }
        super.d();
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v24, types: [short] */
    /* JADX WARN: Type inference failed for: r0v25, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v28, types: [boolean] */
    /* JADX WARN: Type inference failed for: r0v29, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v32, types: [boolean] */
    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        boolean z;
        if (this.o == null) {
            switch (guVar.b) {
                case 1100:
                    short s = this.c.d[this.b.i];
                    switch (s) {
                        case 0:
                            this.s.a(new az(this.s));
                            z = true;
                            break;
                        case 1:
                            this.s.a(com.donglh.narutoninjasaga.c.a.bG, 2001, this);
                            z = true;
                            break;
                        case 2:
                            this.s.a(new jl(this.s));
                            z = true;
                            break;
                        case 3:
                            try {
                                s = Gdx.net.openURI(n.c());
                            } catch (Exception e) {
                                com.donglh.narutoninjasaga.e.aw.a((Exception) s);
                            }
                            z = true;
                            break;
                        case 4:
                            try {
                                s = Gdx.net.openURI(n.d());
                            } catch (Exception e2) {
                                com.donglh.narutoninjasaga.e.aw.a((Exception) s);
                            }
                            z = true;
                            break;
                        case 5:
                            this.s.a(com.donglh.narutoninjasaga.c.a.lY, 2003, this);
                            z = true;
                            break;
                        case 6:
                            this.s.a(com.donglh.narutoninjasaga.c.a.iw, 2002, this);
                            z = true;
                            break;
                        case 7:
                            com.donglh.narutoninjasaga.e.n.n();
                            com.donglh.narutoninjasaga.e.n.l(72);
                            z = true;
                            break;
                        case 8:
                        default:
                            z = false;
                            break;
                        case 9:
                            this.s.a(new bx(com.donglh.narutoninjasaga.e.n.n()));
                            z = true;
                            break;
                        case 10:
                            com.donglh.narutoninjasaga.e.n.n();
                            com.donglh.narutoninjasaga.e.n.l(54);
                            z = true;
                            break;
                        case 11:
                            this.s.a(new fq(this.s));
                            z = true;
                            break;
                        case 12:
                            kc G = kc.G();
                            G.F();
                            G.e();
                            G.aO = 0;
                            G.w = 0;
                            G.d(G.aM, G.aN);
                            this.s.a(G);
                            com.donglh.narutoninjasaga.e.n.n().Q.clear();
                            z = true;
                            break;
                        case 13:
                            this.s.k(com.donglh.narutoninjasaga.c.a.iv).k.clear();
                            com.donglh.narutoninjasaga.e.e.a = true;
                            com.donglh.narutoninjasaga.e.a.f().h = 50;
                            z = true;
                            break;
                        case 14:
                            this.s.a(new ba(this.s));
                            z = true;
                            break;
                        case 15:
                            com.donglh.narutoninjasaga.e.a.f();
                            z = true;
                            break;
                    }
                    if (z) {
                        p();
                        return;
                    }
                    return;
                case 1102:
                    this.a = true;
                    g(-this.aM, 0);
                    a((-this.aM) / 2, (byte) 0);
                    db.a().n();
                    return;
                case 1103:
                    this.a = true;
                    g(-this.aM, 0);
                    a((-this.aM) / 2, (byte) 0);
                    new com.donglh.narutoninjasaga.e.ak((byte) -95).l();
                    com.donglh.narutoninjasaga.e.n.n().a((cn) new hg(com.donglh.narutoninjasaga.e.n.n(), d.a(), com.donglh.narutoninjasaga.e.n.n().t));
                    db.a().i();
                    return;
                case 2001:
                    com.donglh.narutoninjasaga.e.c.c();
                    return;
                case 2002:
                    com.donglh.narutoninjasaga.e.a.f();
                    com.donglh.narutoninjasaga.e.a.i();
                    return;
                case 2003:
                    com.donglh.narutoninjasaga.e.a.f();
                    com.donglh.narutoninjasaga.e.a.j();
                    com.donglh.narutoninjasaga.e.ao n = jp.n();
                    n.h();
                    com.donglh.narutoninjasaga.e.f.c().a(n);
                    return;
                default:
                    return;
            }
        }
    }
}
