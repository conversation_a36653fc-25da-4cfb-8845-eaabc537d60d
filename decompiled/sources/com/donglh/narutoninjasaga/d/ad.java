package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.Input;
import com.badlogic.gdx.net.HttpStatus;
import java.util.Vector;
/* compiled from: LangLa_ai.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/ad.class */
public final class ad extends em {
    public ib[] a = new ib[0];
    private hw b;
    private fw c;
    private cd d;

    public ad(com.donglh.narutoninjasaga.e.ai aiVar) {
        this.s = aiVar;
        d(316, 213);
        this.u = false;
        if (com.donglh.narutoninjasaga.e.n.n().s().d == 16) {
            this.b = new hw(new gz(1001, com.donglh.narutoninjasaga.c.a.sT), 3, a_(), (this.aM - 8) + 2, 60, this);
        } else {
            this.b = new hw(new gz(1001, com.donglh.narutoninjasaga.c.a.sf), 3, a_(), (this.aM - 8) + 2, 60, this);
        }
        this.d = a(173, this.b.d() + 4, 80, 6, new gz(1002, com.donglh.narutoninjasaga.c.a.qQ), this, 0);
        a();
        b(0);
    }

    public final void a() {
        this.c = new fw((byte) 1, 4, this.b.d() + 28, this.aM - 8, 126, 18, this.a.length);
    }

    private void b(int i) {
        this.a = new ib[0];
        a();
        this.b.a(i);
        switch (i) {
            case 0:
                this.d.o = false;
                break;
            default:
                this.d.o = true;
                break;
        }
        this.d.o = true;
        e();
        if (i > 3) {
            this.b.a.a(this.b.b.c.length);
        } else {
            this.b.a.a(0);
        }
    }

    /* JADX WARN: Type inference failed for: r0v1, types: [com.donglh.narutoninjasaga.e.ak, java.lang.Exception] */
    private void e() {
        ?? akVar;
        try {
            akVar = new com.donglh.narutoninjasaga.e.ak((byte) -22);
            akVar.a(this.b.b.b);
            akVar.a(this.d.b.b);
            akVar.l();
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) akVar);
        }
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void b() {
        this.b.b();
        this.c.a();
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a(int i, Object obj, co coVar) {
        switch (i) {
            case 1002:
                e();
                return;
            case 2002:
                gz gzVar = (gz) obj;
                try {
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.h(this.a[gzVar.a].b);
                    return;
                } catch (Exception unused) {
                    return;
                }
            case 2003:
                gz gzVar2 = (gz) obj;
                try {
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.e(this.a[gzVar2.a].b);
                    return;
                } catch (Exception unused2) {
                    return;
                }
            case 2004:
                gz gzVar3 = (gz) obj;
                try {
                    ho.c = this.a[gzVar3.a].b;
                    com.donglh.narutoninjasaga.e.n.n().a(this.a[gzVar3.a].b, new hk(com.donglh.narutoninjasaga.e.n.n(), 6));
                    return;
                } catch (Exception unused3) {
                    return;
                }
            default:
                return;
        }
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a_(int i) {
    }

    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        Vector c = super.c();
        c.addElement(this.b.c());
        c.addElement(this.c.a(1003, this));
        return c;
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        a(lVar, (int) this.aY, (int) this.aZ, (int) this.aM, (int) this.aN, com.donglh.narutoninjasaga.c.a.bT, (byte) 2, false);
        this.b.a(lVar);
        b(lVar, this.c);
        a(lVar, 0, -28, this.c.aM, 28, -11, 55, 56);
        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.bU, 20, -15, 2, -6488, -10275328);
        if (this.b.b.b == 5) {
            kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.bV, 70, -15, 2, -6488, -10275328);
            kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.jt, Input.Keys.CONTROL_RIGHT, -15, 2, -6488, -10275328);
            kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.di, HttpStatus.SC_NO_CONTENT, -15, 2, -6488, -10275328);
            kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.dj, 274, -15, 2, -6488, -10275328);
            a(lVar, this.c);
            for (int i = 0; i < this.c.g; i++) {
                if (this.c.b(i)) {
                    if (i == this.c.i) {
                        lVar.e(13136426);
                        lVar.c(0, i * this.c.f, this.c.aM, this.c.f);
                    }
                    ib ibVar = this.a[i];
                    kk.b(kk.c, lVar, new StringBuilder().append(i + 1).toString(), 20, 8 + (i * this.c.f), 2, -3604601, -16777216);
                    kk.b(kk.c, lVar, ibVar.f, 70, 8 + (i * this.c.f), 2, -3604601, -16777216);
                    kk.b(kk.c, lVar, new StringBuilder().append(ibVar.c).toString(), Input.Keys.CONTROL_RIGHT, 8 + (i * this.c.f), 2, -3604601, -16777216);
                    kk.b(kk.c, lVar, ibVar.d + "/" + ibVar.e, 208, 8 + (i * this.c.f), 2, -3604601, -16777216);
                    kk.b(kk.c, lVar, ibVar.b, 274, 8 + (i * this.c.f), 2, -3604601, -16777216);
                }
            }
            b(lVar);
            return;
        }
        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.ju, 70, -15, 2, -6488, -10275328);
        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.jv, Input.Keys.CONTROL_RIGHT, -15, 2, -6488, -10275328);
        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.jz, HttpStatus.SC_NO_CONTENT, -15, 2, -6488, -10275328);
        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.jA, 274, -15, 2, -6488, -10275328);
        a(lVar, this.c);
        for (int i2 = 0; i2 < this.c.g; i2++) {
            if (this.c.b(i2)) {
                if (i2 == this.c.i) {
                    lVar.e(13136426);
                    lVar.c(0, i2 * this.c.f, this.c.aM, this.c.f);
                }
                ib ibVar2 = this.a[i2];
                kk.b(kk.c, lVar, new StringBuilder().append(i2 + 1).toString(), 20, 8 + (i2 * this.c.f), 2, -3604601, -16777216);
                kk.b(kk.c, lVar, ibVar2.b, 70, 8 + (i2 * this.c.f), 2, -3604601, -16777216);
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.c(ibVar2.d), Input.Keys.CONTROL_RIGHT, 8 + (i2 * this.c.f), 2, -3604601, -16777216);
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.e.f.c().R[ibVar2.e].b, 208, 8 + (i2 * this.c.f), 2, -3604601, -16777216);
                kk.b(kk.c, lVar, ibVar2.f, 274, 8 + (i2 * this.c.f), 2, -3604601, -16777216);
            }
        }
        b(lVar);
    }

    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        switch (guVar.b) {
            case 1001:
                if (guVar.j.i >= 0) {
                    b(guVar.j.i);
                    return;
                }
                return;
            case 1003:
                if (guVar.j.i >= 0) {
                    int i3 = guVar.j.i;
                    Vector vector = new Vector();
                    vector.addElement(new hz(2002, com.donglh.narutoninjasaga.c.a.ca));
                    vector.addElement(new hz(2003, com.donglh.narutoninjasaga.c.a.cb));
                    vector.addElement(new hz(2004, com.donglh.narutoninjasaga.c.a.cc));
                    if (vector.size() > 0) {
                        String[] strArr = new String[vector.size()];
                        short[] sArr = new short[vector.size()];
                        for (int i4 = 0; i4 < vector.size(); i4++) {
                            hz hzVar = (hz) vector.elementAt(i4);
                            strArr[i4] = hzVar.b;
                            sArr[i4] = (short) hzVar.a;
                        }
                        this.n = a(this, i + 25, i2, new gz(i3, sArr, strArr));
                        return;
                    }
                    return;
                }
                return;
            default:
                return;
        }
    }
}
