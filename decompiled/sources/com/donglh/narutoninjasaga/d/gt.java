package com.donglh.narutoninjasaga.d;

import java.util.Vector;
/* compiled from: LangLa_jw.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/gt.class */
public final class gt extends j {
    public short a;
    public byte b = -1;
    public byte c = -1;
    private Vector e = new Vector();
    private Vector f = new Vector();
    private int g = 0;
    public boolean d;
    private int h;

    private com.donglh.narutoninjasaga.e.ah i() {
        return com.donglh.narutoninjasaga.e.f.c().K[this.a];
    }

    public gt(int i, int i2, int i3) {
        a(i, i2, i3, 1);
    }

    public gt(int i, int i2, int i3, int i4) {
        a(i, i2, i3, i4);
    }

    private void a(int i, int i2, int i3, int i4) {
        this.b = (byte) i4;
        this.a = (short) i;
        g(i2, i3);
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final int d() {
        return this.aY - (this.aM / 2);
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final int e() {
        return this.aY + (this.aM / 2);
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final int f() {
        return this.aZ - (this.aN / 2);
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final int g() {
        return this.aZ + (this.aN / 2);
    }

    public final void a() {
        fk fkVar;
        if (this.a >= 273 && this.a <= 275 && com.donglh.narutoninjasaga.e.f.c().i % 20 == 0 && this.h == 0 && com.donglh.narutoninjasaga.e.aw.c()) {
            this.h = com.donglh.narutoninjasaga.e.aw.a(10, 100);
        }
        if (this.h > 0) {
            this.h--;
            return;
        }
        this.c = (byte) (this.c + 1);
        if (this.g > 0 && this.c >= this.g) {
            this.c = (byte) 0;
            if (this.b > 0) {
                this.b = (byte) (this.b - 1);
            }
        }
        for (int i = 0; i < i().b.length; i++) {
            if (this.d) {
                fkVar = new fk(i().b[i].a, this.aY - i().b[i].d, this.aZ + i().b[i].e);
            } else {
                fkVar = new fk(i().b[i].a, this.aY + i().b[i].d, this.aZ + i().b[i].e);
            }
            fkVar.c = this.d;
            if (this.c == i().b[i].b) {
                this.e.addElement(fkVar);
            }
            if (i + fkVar.a().b.length > this.g) {
                this.g = i + fkVar.a().b.length;
            }
            if (i().a > 2) {
                fkVar.b = (byte) (i().a - 2);
            }
        }
        if (this.b != 0) {
            com.donglh.narutoninjasaga.e.n.n().K.addAll(this.e);
        }
        this.e.clear();
    }

    public final void b() {
        fk fkVar;
        this.c = (byte) (this.c + 1);
        if (this.g > 0 && this.b == -1 && this.c >= this.g) {
            this.c = (byte) 0;
        }
        for (int i = 0; i < i().b.length; i++) {
            if (this.d) {
                fkVar = new fk(i().b[i].a, this.aY - i().b[i].d, this.aZ + i().b[i].e);
            } else {
                fkVar = new fk(i().b[i].a, this.aY + i().b[i].d, this.aZ + i().b[i].e);
            }
            fkVar.c = this.d;
            if (this.c == i().b[i].b) {
                this.e.addElement(fkVar);
            }
            if (i + fkVar.a().b.length > this.g) {
                this.g = i + fkVar.a().b.length;
            }
        }
        for (int size = this.e.size() - 1; size >= 0; size--) {
            fk fkVar2 = (fk) this.e.elementAt(size);
            fkVar2.c();
            if (fkVar2.b()) {
                this.e.removeElement(fkVar2);
            }
        }
        if (this.c >= this.g) {
            this.c = (byte) -1;
            if (this.b > 0) {
                this.b = (byte) (this.b - 1);
            }
        }
    }

    public final void c() {
        this.c = (byte) -1;
        this.e.clear();
        this.f.clear();
    }

    public final void b(j jVar) {
        fk fkVar;
        short s = jVar.aY;
        short s2 = jVar.aZ;
        if (this.a == 112 || this.a == 113) {
            s2 = (short) (s2 - (jVar.aN + 14));
            if ((jVar instanceof jt) && ((jt) jVar).a().h.length() > 0) {
                s2 = (short) (s2 - 10);
            }
        }
        if (this.a == 109) {
            s2 = (short) (s2 - jVar.aN);
        }
        if (this.a == 170) {
            if (jVar instanceof d) {
                s2 = (short) (s2 - 17);
            } else {
                s2 = (short) (s2 - (jVar.aN / 2));
            }
        }
        if (this.a == 98 && jVar.aG == 2) {
            switch (jVar.aF) {
                case 0:
                    s2 = (short) (s2 + 4);
                    break;
                case 1:
                    s2 = (short) (s2 - 12);
                    break;
                case 2:
                    s = (short) (s + 12);
                    break;
                case 3:
                    s = (short) (s - 12);
                    break;
            }
        }
        int i = s - this.aY;
        int i2 = s2 - this.aZ;
        if (!(this.a == 72 || this.a == 112 || this.a == 113 || this.a == 182 || this.a == 133 || this.a == 134)) {
            this.d = jVar.j();
        }
        this.aY = s;
        this.aZ = s2;
        this.c = (byte) (this.c + 1);
        if (this.g > 0 && this.b == -1 && this.c >= this.g) {
            this.c = (byte) 0;
        }
        for (int size = this.e.size() - 1; size >= 0; size--) {
            fk fkVar2 = (fk) this.e.elementAt(size);
            fkVar2.aY = (short) (fkVar2.aY + i);
            fkVar2.aZ = (short) (fkVar2.aZ + i2);
            fkVar2.c();
            if (fkVar2.b()) {
                this.e.removeElement(fkVar2);
            }
        }
        for (int i3 = 0; i3 < i().b.length; i3++) {
            if (this.d) {
                fkVar = new fk(i().b[i3].a, this.aY - i().b[i3].d, this.aZ + i().b[i3].e);
            } else {
                fkVar = new fk(i().b[i3].a, this.aY + i().b[i3].d, this.aZ + i().b[i3].e);
            }
            fkVar.c = this.d;
            if (i().a > 2) {
                fkVar.b = (byte) (i().a - 2);
            }
            if (this.c == i().b[i3].b) {
                this.e.addElement(fkVar);
            }
            if (i3 + fkVar.a().b.length > this.g) {
                this.g = i3 + fkVar.a().b.length;
            }
        }
        for (int size2 = this.f.size() - 1; size2 >= 0; size2--) {
            fk fkVar3 = (fk) this.f.elementAt(size2);
            if (i().a > 2) {
                fkVar3.b = (byte) (i().a - 2);
            }
            fkVar3.c = this.d;
            if (fkVar3.b()) {
                this.f.remove(fkVar3);
            }
        }
        for (int size3 = this.e.size() - 1; size3 >= 0; size3--) {
            fk fkVar4 = (fk) this.e.elementAt(size3);
            if (i().a > 2) {
                fkVar4.b = (byte) (i().a - 2);
            }
            if (!jVar.O()) {
                com.donglh.narutoninjasaga.e.n.n().a((j) this.e.elementAt(size3));
            }
        }
        if (this.c >= this.g) {
            this.c = (byte) -1;
            if (this.b > 0) {
                this.b = (byte) (this.b - 1);
            }
        }
    }

    public final boolean h() {
        return this.b == 0;
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final void a(l lVar) {
        if (!h() && this.h <= 0) {
            for (int i = 0; i < this.e.size(); i++) {
                ((fk) this.e.elementAt(i)).b(lVar, 0, 0);
            }
        }
    }

    public final void b(l lVar, int i, int i2) {
        if (!h()) {
            for (int i3 = 0; i3 < this.e.size(); i3++) {
                ((fk) this.e.elementAt(i3)).b(lVar, i, i2);
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final String A() {
        return null;
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final String B() {
        return null;
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final String C() {
        return null;
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final void b(l lVar) {
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final int N() {
        return 2;
    }
}
