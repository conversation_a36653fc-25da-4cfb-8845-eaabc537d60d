package com.donglh.narutoninjasaga.d;
/* compiled from: LangLa_ki.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/hd.class */
public final class hd extends cd {
    public hd(int i, int i2, int i3, gz gzVar, cn cnVar) {
        super(i, i2, 20, i3, gzVar, cnVar, 1, true);
        this.i = new String[gzVar.c.length];
        g(i, i2);
        this.c = 100;
        this.d = i3 * this.h;
        for (int i4 = 0; i4 < this.i.length; i4++) {
            this.i[i4] = "";
            kk.b(kk.c, gzVar.c[i4]);
        }
    }

    @Override // com.donglh.narutoninjasaga.d.cd, com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        if (this.b.b < 0 || this.b.b >= this.b.d.length) {
            this.b.b = 0;
        }
        short s = this.b.d[this.b.b];
        if (m()) {
            lVar.d();
        }
        com.donglh.narutoninjasaga.e.r.a(lVar, s, 0, d() - 1, 2, 24);
        lVar.e();
        if (this.j) {
            a.a(lVar, -16, 0, this.a.aY, this.a.aZ, this.a.aM, this.a.aN);
            a(lVar, this.a);
            for (int i = 0; i < this.b.c.length; i++) {
                try {
                    if (this.a.b(i)) {
                        if (i == this.a.i) {
                            a.a(lVar, -7, 0, 0, i * this.a.f, this.a.aM, this.a.f);
                        }
                        if (i == this.a.j) {
                            com.donglh.narutoninjasaga.e.r.a(lVar, this.b.d[i], 0, 11, 1 + (i * this.a.f) + (this.a.f / 2), 3);
                            kk.b(kk.c, lVar, this.b.c[i], 26, 0 + (i * this.a.f) + (this.a.f / 2), 0, -1, -11184811);
                        } else {
                            com.donglh.narutoninjasaga.e.r.a(lVar, this.b.d[i], 0, 10, (i * this.a.f) + (this.a.f / 2), 3);
                            kk.b(kk.c, lVar, this.b.c[i], 25, (-1) + (i * this.a.f) + (this.a.f / 2), 0, -1, -11184811);
                        }
                        if (i == 8) {
                            com.donglh.narutoninjasaga.e.r.a(lVar, 752 + ((com.donglh.narutoninjasaga.e.f.c().i / 10) % 4), 0, this.a.aM - 3, ((2 + (i * this.a.f)) + (this.a.f / 2)) - 3, 10);
                        }
                    }
                } catch (Exception e) {
                    com.donglh.narutoninjasaga.e.aw.a("loiiii: " + this.b.d.length + ", " + this.b.c.length + ", iii: 0");
                    com.donglh.narutoninjasaga.e.aw.a(e);
                }
            }
            b(lVar);
        }
    }

    @Override // com.donglh.narutoninjasaga.d.cd, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        if (this.e <= 0) {
            try {
                switch (guVar.b) {
                    case -1002:
                        if (guVar.j.i >= 0) {
                            this.e = 4;
                            if (this.a.i == 7) {
                                com.donglh.narutoninjasaga.e.n.n().b(com.donglh.narutoninjasaga.c.a.oD, -3407617);
                                return;
                            } else if (this.a.i == 8) {
                                com.donglh.narutoninjasaga.e.n.n().b(com.donglh.narutoninjasaga.c.a.nz, -7812062);
                                return;
                            } else {
                                return;
                            }
                        }
                        return;
                    case -1001:
                        a();
                        return;
                    case -1000:
                        this.j = true;
                        a_(this.c, this.d);
                        if (this.a == null) {
                            if (this.f == 0) {
                                this.a = new fw((byte) 1, 0, 21, this.aM, this.aN - this.h, this.h, this.b.c.length);
                            } else if (this.f == 1) {
                                this.a = new fw((byte) 1, 0, 21 - this.aN, this.aM, this.aN - this.h, this.h, this.b.c.length);
                            }
                        }
                        if (this.a.h < 0 || this.a.h >= this.b.c.length || this.a.h > this.b.b + (this.g / 2) + 1 || this.a.h < this.b.b - ((this.g / 2) + 1)) {
                            this.a.a(this.b.b);
                        }
                        this.a.i = this.b.b;
                        return;
                    default:
                        return;
                }
            } catch (Exception unused) {
            }
        }
    }

    public final void a(byte b) {
        this.b.b = b;
    }

    public final byte e() {
        if (this.b.b >= 8) {
            return (byte) 9;
        }
        if (this.b.b >= 7) {
            return (byte) 8;
        }
        return (byte) this.b.b;
    }
}
