package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.Input;
/* compiled from: LangLa_aw.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/ar.class */
public final class ar extends em {
    private String[] a;

    public ar(String str, com.donglh.narutoninjasaga.e.ai aiVar) {
        this.s = aiVar;
        d(240, Input.Keys.NUMPAD_6);
        this.u = false;
        a(com.donglh.narutoninjasaga.c.a.e, 100);
        n();
        this.a = kk.c(kk.c, str, this.aM - 40);
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a(int i, Object obj, co coVar) {
    }

    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        if (guVar.b == 100) {
            p();
        }
    }

    @Override // com.donglh.narutoninjasaga.d.ej
    public final void d() {
        new com.donglh.narutoninjasaga.e.ak((byte) -89).l();
        com.donglh.narutoninjasaga.e.n.n().by = "";
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a_(int i) {
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        a(lVar, (int) this.aY, (int) this.aZ, (int) this.aM, (int) this.aN, com.donglh.narutoninjasaga.c.a.dr, (byte) 2, false);
        a(lVar, 6, 33, this.aM - 12, this.aN - 48, -11, 55, 56);
        for (int i = 0; i < this.a.length; i++) {
            kk.c(kk.c, lVar, this.a[i], 10, ((((this.aN - 33) / 2) + 12) + (i * 12)) - (this.a.length * 6), 0, -1, -16777216);
        }
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void b() {
    }
}
