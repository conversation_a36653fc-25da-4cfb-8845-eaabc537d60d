package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.Input;
import com.badlogic.gdx.net.HttpStatus;
import java.util.Vector;
/* compiled from: LangLa_am.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/ah.class */
public final class ah extends he {
    public q[] a;
    public q[] b;
    private fx[] g;
    private dg h;
    public int c;
    public gt d;
    public q e;
    public boolean f;

    public ah(com.donglh.narutoninjasaga.e.ai aiVar) {
        super(aiVar, new String[]{com.donglh.narutoninjasaga.c.a.rp[7], com.donglh.narutoninjasaga.c.a.F});
        this.a = new q[1];
        this.b = new q[20];
        this.g = new fx[3];
        this.g[0] = new fx((byte) 1, HttpStatus.SC_ACCEPTED, a_() + 20, 30, 30, 30, 1, 1);
        this.g[1] = new fx((byte) 1, 242, a_() + 20, 30, 30, 30, 1, 1);
        this.g[2] = new fx((byte) 0, 14, a_() + 20, 128, 160, 32, 5, 4);
        this.h = a(this.aM - 70, this.aN - 33, com.donglh.narutoninjasaga.c.a.aZ, this, 0, -8);
        a(this.h, 0);
    }

    @Override // com.donglh.narutoninjasaga.d.et
    public final void a() {
        this.A = new q[d.a().W.length];
        int i = 0;
        for (int i2 = 0; i2 < d.a().W.length; i2++) {
            if (d.a().W[i2] != null && d.a().W[i2].r()) {
                this.A[i] = d.a().W[i2];
                i++;
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.el
    public final void a(int i) {
        super.a(i);
        this.f = false;
    }

    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void b() {
        super.b();
        if (this.i.b == 0) {
            if (!this.f) {
                if (this.a[0] == null) {
                    int i = 0;
                    while (true) {
                        if (i < this.b.length) {
                            if (this.b[i] == null) {
                                i++;
                            } else {
                                a(this.b[i]);
                                break;
                            }
                        } else {
                            break;
                        }
                    }
                } else {
                    boolean z = false;
                    int i2 = 0;
                    while (true) {
                        if (i2 >= this.b.length) {
                            break;
                        } else if (this.b[i2] == null || !this.b[i2].equals(this.a[0])) {
                            i2++;
                        } else {
                            z = true;
                            break;
                        }
                    }
                    if (!z) {
                        a((q) null);
                    } else {
                        boolean z2 = false;
                        for (int i3 = 0; i3 < this.b.length; i3++) {
                            if (this.b[i3] != null) {
                                z2 = true;
                            }
                        }
                        if (!z2) {
                            a((q) null);
                        }
                    }
                }
            }
            for (int i4 = 0; i4 < this.g.length; i4++) {
                this.g[i4].a();
            }
        } else {
            a();
        }
        if (this.d != null) {
            this.d.b();
            if (this.d.h()) {
                this.d = null;
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.he, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        if (q() <= 0) {
            a(lVar, (int) this.aY, (int) this.aZ, (int) this.aM, (int) this.aN, com.donglh.narutoninjasaga.c.a.o, (byte) 2, false);
        } else {
            super.a(lVar);
        }
        if (this.i.b == 0) {
            kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.nA, 14, a_() + 9, 0, -10831436, -16777216);
            if (this.f) {
                if (com.donglh.narutoninjasaga.e.f.c().i % 20 > 12) {
                    kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.nE, Input.Keys.NUMPAD_5, a_() + Input.Keys.BUTTON_START, 0, -2560, -16777216);
                }
            } else if (this.e != null) {
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.b(com.donglh.narutoninjasaga.c.a.nC, new StringBuilder().append(this.e.p * 6).toString()), Input.Keys.NUMPAD_5, a_() + Input.Keys.BUTTON_START, 0, -30976, -16777216);
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.nF, Input.Keys.NUMPAD_5, a_() + 124, 0, -10831436, -16777216);
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.nG, Input.Keys.NUMPAD_5, a_() + 140, 0, -10831436, -16777216);
            }
            a(lVar, this.g[0].aY, this.g[0].aZ, this.e, this.g[0].i >= 0, com.donglh.narutoninjasaga.c.a.G);
            a(lVar, this.g[2]);
            for (int i = 0; i < this.g[2].g; i++) {
                for (int i2 = 0; i2 < this.g[2].o; i2++) {
                    b(lVar, i2 * this.g[2].f, i * this.g[2].f, this.b[(i * this.g[2].o) + i2], (i * this.g[2].g) + i2 == this.g[2].i, "");
                }
            }
            b(lVar);
            if (this.d != null) {
                this.d.b(lVar, this.g[0].aY + (this.g[0].f / 2), this.g[0].aZ + (this.g[0].f / 2));
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.he, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        Vector c = super.c();
        if (this.i.b == 0) {
            for (int i = 0; i < this.g.length; i++) {
                if (i != 1) {
                    c.addElement(this.g[i].a(i + 1001, this));
                }
            }
        }
        return c;
    }

    @Override // com.donglh.narutoninjasaga.d.he, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        if (this.i.b == q() && guVar.b == 0) {
            d();
        }
        super.a(guVar, i, i2);
        if (this.i.b == 0) {
            switch (guVar.b) {
                case 0:
                    a(true);
                    return;
                case 1001:
                    if (this.a[0] == null) {
                        a(2);
                        com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.nB, -1);
                        return;
                    }
                    this.c = 1;
                    a(guVar.j, guVar.j.i);
                    this.n = a(guVar, this, this.e);
                    return;
                case 1002:
                    this.c = 0;
                    a(guVar.j, guVar.j.i);
                    this.n = a(guVar, this, this.a[1]);
                    return;
                case 1003:
                    this.c = 0;
                    a(guVar.j, guVar.j.i);
                    this.n = b(guVar, this, this.b[((guVar.j.i / 5) << 2) + (guVar.j.i % 5)]);
                    return;
                case 2001:
                    a(false);
                    return;
                default:
                    return;
            }
        }
    }

    private void a(fw fwVar, int i) {
        for (int i2 = 0; i2 < this.g.length; i2++) {
            this.g[i2].i = -1;
        }
        fwVar.i = i;
    }

    @Override // com.donglh.narutoninjasaga.d.ej
    public final void d() {
        super.d();
        for (int i = 0; i < this.b.length; i++) {
            if (this.b[i] != null) {
                d.a().g(this.b[i].r)[this.b[i].e] = this.b[i];
                this.b[i] = null;
            }
        }
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x00ce: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:27:0x00cd */
    private void a(boolean z) {
        Exception a;
        try {
            if (this.e != null && this.e.p > 0) {
                if (z) {
                    com.donglh.narutoninjasaga.e.f.c().am.a(com.donglh.narutoninjasaga.c.a.nD, 2001, this);
                    return;
                }
                com.donglh.narutoninjasaga.e.ak akVar = new com.donglh.narutoninjasaga.e.ak((byte) -50);
                Vector vector = new Vector();
                for (int i = 0; i < this.b.length; i++) {
                    if (this.b[i] != null) {
                        if (this.b[i].c == this.e.c) {
                            vector.insertElementAt(this.b[i], 0);
                        } else {
                            vector.add(this.b[i]);
                        }
                    }
                }
                akVar.a(vector.size());
                for (int i2 = 0; i2 < vector.size(); i2++) {
                    akVar.a(((q) vector.get(i2)).r);
                    akVar.b(((q) vector.get(i2)).e);
                }
                akVar.l();
                return;
            }
            com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.nA, -65536);
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a);
        }
    }

    public final void a(q qVar) {
        s[] N;
        this.a[0] = qVar;
        if (this.a[0] == null) {
            this.e = null;
            return;
        }
        this.e = this.a[0].a();
        this.e.p = (byte) -1;
        for (int i = 0; i < this.b.length; i++) {
            if (this.b[i] != null) {
                q qVar2 = this.e;
                qVar2.p = (byte) (qVar2.p + 1);
            }
        }
        if (this.e.p < 0) {
            this.e.p = (byte) 0;
        }
        Vector vector = new Vector();
        Vector vector2 = new Vector();
        for (int i2 = 0; i2 < this.b.length; i2++) {
            if (this.b[i2] != null && (N = this.b[i2].N()) != null) {
                if (!this.b[i2].equals(this.a[0])) {
                    int i3 = 0;
                    while (true) {
                        if (i3 < com.donglh.narutoninjasaga.e.f.c().V.length) {
                            if (!com.donglh.narutoninjasaga.e.f.c().V[i3].b.trim().toLowerCase().equals(this.b[i2].h().b.trim().toLowerCase())) {
                                i3++;
                            } else {
                                vector2.add(new s(((int) com.donglh.narutoninjasaga.e.f.c().V[i3].a) + ",0,0"));
                                break;
                            }
                        } else {
                            break;
                        }
                    }
                }
                for (int i4 = 0; i4 < N.length; i4++) {
                    boolean z = true;
                    int i5 = 0;
                    while (true) {
                        if (i5 < vector.size()) {
                            if (((s) vector.get(i5)).a[0] != N[i4].a[0]) {
                                i5++;
                            } else {
                                ((s) vector.get(i5)).c(((s) vector.get(i5)).a[1] + N[i4].a[1]);
                                z = false;
                                break;
                            }
                        } else {
                            break;
                        }
                    }
                    if (z) {
                        vector.add(N[i4]);
                    }
                }
            }
        }
        vector.addAll(vector2);
        this.e.h = q.a(vector);
        this.e.p = (byte) vector2.size();
        this.e.q = true;
    }
}
