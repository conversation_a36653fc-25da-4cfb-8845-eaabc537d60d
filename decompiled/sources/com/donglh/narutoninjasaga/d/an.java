package com.donglh.narutoninjasaga.d;

import java.util.Vector;
/* compiled from: LangLa_as.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/an.class */
public final class an extends he {
    public q[] a;
    public q[] b;
    private fx[] f;
    private ci[] g;
    private dg h;
    private dg B;
    public int c;
    private gy C;
    public int d;
    public gt e;
    private cd D;

    public an(com.donglh.narutoninjasaga.e.ai aiVar) {
        super(aiVar, new String[]{com.donglh.narutoninjasaga.c.a.rp[2], com.donglh.narutoninjasaga.c.a.F});
        this.a = new q[2];
        this.b = new q[6];
        this.f = new fx[2];
        this.g = new ci[2];
        this.d = 0;
        this.f[0] = new fx((byte) 1, 124, a_() + 20, 30, 30, 30, 1, 1);
        this.f[1] = new fx((byte) 1, 14, a_() + 20, 64, 96, 32, 3, 2);
        this.h = a(this.aM - 70, this.aN - 33, com.donglh.narutoninjasaga.c.a.ba, this, 0, -8);
        a(this.h, 0);
        this.B = a(139, this.aN - 33, com.donglh.narutoninjasaga.c.a.jY, this, 2002, -8);
        a(this.B, 0);
        gz gzVar = new gz(2000, com.donglh.narutoninjasaga.c.a.ss);
        this.D = a(14, this.aN - 32, 120, gzVar.c.length, gzVar, this, 1);
        this.D.a(0);
        a(this.D, 0);
        this.C = new gy(0);
        this.g[0] = a(89, a_() + 77, "", this, this.C);
        this.g[1] = a(89, a_() + 97, "", this, this.C);
        a(this.g[0], 0);
        a(this.g[1], 0);
    }

    @Override // com.donglh.narutoninjasaga.d.et
    public final void a() {
        this.A = new q[d.a().W.length];
        int i = 0;
        for (int i2 = 0; i2 < d.a().W.length; i2++) {
            if (d.a().W[i2] != null && d.a().W[i2].E()) {
                this.A[i] = d.a().W[i2];
                i++;
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void b() {
        super.b();
        if (this.i.b == 0) {
            int i = 0;
            for (int i2 = 0; i2 < this.b.length; i2++) {
                if (this.b[i2] != null) {
                    i += this.b[i2].O();
                }
            }
            if (this.a[0] != null) {
                s[] N = this.a[0].N();
                for (int i3 = 0; i3 < this.g.length; i3++) {
                    this.g[i3].o = false;
                    if (this.g[i3].a) {
                        this.g[i3].a(N[i3].b(i));
                    } else {
                        this.g[i3].a(N[i3].b(0));
                    }
                }
            } else {
                for (int i4 = 0; i4 < this.g.length; i4++) {
                    this.g[i4].o = true;
                }
            }
            for (int i5 = 0; i5 < this.f.length; i5++) {
                this.f[i5].a();
            }
        } else {
            a();
        }
        if (this.e != null) {
            this.e.b();
            if (this.e.h()) {
                this.e = null;
            }
        }
        if (this.d > 0) {
            this.d--;
        }
    }

    @Override // com.donglh.narutoninjasaga.d.he, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        if (q() <= 0) {
            a(lVar, (int) this.aY, (int) this.aZ, (int) this.aM, (int) this.aN, com.donglh.narutoninjasaga.c.a.o, (byte) 2, false);
        } else {
            super.a(lVar);
        }
        if (this.i.b == 0) {
            kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.aQ, 14, a_() + 9, 0, -10831436, -16777216);
            if (this.a[0] != null) {
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.aR, 89, a_() + 65, 0, -10831436, -16777216);
            }
            a(lVar, this.f[0].aY, this.f[0].aZ, this.a[0], this.f[0].i >= 0, com.donglh.narutoninjasaga.c.a.az);
            com.donglh.narutoninjasaga.e.r.a(lVar, 95, 0, 92 - ((com.donglh.narutoninjasaga.e.f.c().i / 3) % 3), a_() + 25, 20);
            if (this.d > 0 && this.d % 14 > 2) {
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.jO, 89, a_() + 125, 0, -2560, -16777216);
            }
            a(lVar, this.f[1]);
            int i = 0;
            for (int i2 = 0; i2 < this.f[1].g; i2++) {
                for (int i3 = 0; i3 < this.f[1].o; i3++) {
                    if (i3 % 2 == 0) {
                        b(lVar, i3 * this.f[1].f, i2 * this.f[1].f, this.b[(i2 * this.f[1].o) + i3], i == this.f[1].i, com.donglh.narutoninjasaga.c.a.ra[0]);
                    } else {
                        b(lVar, i3 * this.f[1].f, i2 * this.f[1].f, this.b[(i2 * this.f[1].o) + i3], i == this.f[1].i, com.donglh.narutoninjasaga.c.a.ra[1]);
                    }
                    i++;
                }
            }
            b(lVar);
            if (this.e != null) {
                this.e.b(lVar, this.f[0].aY + (this.f[0].f / 2), this.f[0].aZ + (this.f[0].f / 2));
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.he, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        Vector c = super.c();
        if (this.i.b == 0) {
            for (int i = 0; i < this.f.length; i++) {
                c.addElement(this.f[i].a(i + 1001, this));
            }
        }
        return c;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v82, types: [com.donglh.narutoninjasaga.d.q[]] */
    /* JADX WARN: Type inference failed for: r0v83 */
    @Override // com.donglh.narutoninjasaga.d.he, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        if (this.i.b == q() && guVar.b == 0) {
            d();
        }
        super.a(guVar, i, i2);
        if (this.i.b == 0) {
            switch (guVar.b) {
                case 0:
                    int i3 = 0;
                    Exception exc = null;
                    int i4 = 0;
                    while (i4 < this.b.length) {
                        try {
                            ?? r0 = this.b[i4];
                            if (r0 != 0) {
                                i3++;
                            }
                            i4++;
                            exc = r0;
                        } catch (Exception e) {
                            com.donglh.narutoninjasaga.e.aw.a(exc);
                            return;
                        }
                    }
                    if (this.a[0] != null && i3 != 0) {
                        s sVar = this.a[0].N()[this.C.b];
                        if (sVar.a[1] >= sVar.f()) {
                            com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.jQ, -2560);
                            return;
                        }
                        com.donglh.narutoninjasaga.e.ak akVar = new com.donglh.narutoninjasaga.e.ak((byte) 106);
                        akVar.a(this.C.b);
                        akVar.a(this.a[0].r);
                        akVar.b(this.a[0].e);
                        akVar.a(i3);
                        for (int i5 = 0; i5 < this.b.length; i5++) {
                            if (this.b[i5] != null) {
                                akVar.b(this.b[i5].e);
                            }
                        }
                        akVar.l();
                        return;
                    }
                    com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.jP, -65536);
                    return;
                case 1001:
                    this.c = 1;
                    a(guVar.j, guVar.j.i);
                    this.n = a(guVar, this, this.a[0]);
                    if (this.a[0] == null) {
                        a(2);
                        com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.mp, -1);
                        return;
                    }
                    return;
                case 1002:
                    this.c = 2;
                    a(guVar.j, guVar.j.i);
                    this.n = a(guVar, this, this.b[guVar.j.i]);
                    return;
                case 2002:
                    boolean z = this.D.b.b == 0;
                    for (int i6 = 0; i6 < d.a().W.length; i6++) {
                        if (d.a().W[i6] != null && d.a().W[i6].c == 160 && d.a().W[i6].q == z) {
                            int i7 = 0;
                            while (true) {
                                if (i7 >= this.b.length) {
                                    break;
                                } else if (this.b[i7] != null) {
                                    i7++;
                                } else {
                                    this.b[i7] = d.a().W[i6];
                                    d.a().W[i6] = null;
                                }
                            }
                        }
                    }
                    return;
                default:
                    return;
            }
        }
    }

    private void a(fw fwVar, int i) {
        for (int i2 = 0; i2 < this.f.length; i2++) {
            this.f[i2].i = -1;
        }
        fwVar.i = i;
    }

    @Override // com.donglh.narutoninjasaga.d.ej
    public final void d() {
        super.d();
        if (this.a[0] != null) {
            d.a().g(this.a[0].r)[this.a[0].e] = this.a[0];
            this.a[0] = null;
        }
        for (int i = 0; i < this.b.length; i++) {
            if (this.b[i] != null) {
                d.a().W[this.b[i].e] = this.b[i];
                this.b[i] = null;
            }
        }
    }
}
