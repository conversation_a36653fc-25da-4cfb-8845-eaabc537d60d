package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.Application;
import com.badlogic.gdx.Gdx;
import com.badlogic.gdx.Input;
import java.util.Vector;
/* compiled from: LangLa_w.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/jl.class */
public final class jl extends em {
    private cb a;
    private cb b;
    private cb c;
    private gy d;
    private gy e;
    private hw f;
    private int g;

    public jl(com.donglh.narutoninjasaga.e.ai aiVar) {
        this.g = 2;
        this.s = aiVar;
        int i = 0;
        if (Gdx.app.getType() == Application.ApplicationType.Desktop) {
            d(240, 186);
            i = 15;
        } else {
            d(240, 166);
        }
        this.f = new hw(new gz(2000, com.donglh.narutoninjasaga.c.a.sI), 4, a_(), this.aM - 8, 60, this);
        this.a = a(15, i + 45, com.donglh.narutoninjasaga.c.a.bD, 1001);
        this.a.a = com.donglh.narutoninjasaga.e.ay.a().a(1);
        this.b = a(15, i + 65, com.donglh.narutoninjasaga.c.a.bE, 1002);
        this.b.a = com.donglh.narutoninjasaga.e.ay.a().a(0);
        this.c = a(Input.Keys.CONTROL_RIGHT, i + 45, com.donglh.narutoninjasaga.c.a.bR, 1004);
        this.c.a = com.donglh.narutoninjasaga.e.ay.a().a(11);
        this.d = new gy(1000, com.donglh.narutoninjasaga.e.ay.a().b(9));
        this.e = new gy(1005, 1);
        short[] b = com.donglh.narutoninjasaga.e.c.b();
        if (b[0] == 800 && b[1] == 480) {
            this.e.b = 0;
        } else if (b[0] == 960 && b[1] == 640) {
            this.e.b = 1;
        } else if (b[0] == 1024 && b[1] == 600) {
            this.e.b = 2;
        } else if (b[0] == 1200 && b[1] == 720) {
            this.e.b = 3;
        }
        ci a = a(15, i + 90, com.donglh.narutoninjasaga.c.a.bO, this, this.d);
        ci a2 = a(15, i + 115, com.donglh.narutoninjasaga.c.a.bP, this, this.d);
        ci a3 = Gdx.app.getType() == Application.ApplicationType.Desktop ? a(15, i + 140, com.donglh.narutoninjasaga.c.a.nW, this, this.d) : null;
        ci a4 = a(15, i + 60, com.donglh.narutoninjasaga.c.a.sJ[0], this, this.e);
        ci a5 = a(15, i + 82, com.donglh.narutoninjasaga.c.a.sJ[1], this, this.e);
        ci a6 = a(15, i + Input.Keys.BUTTON_L2, com.donglh.narutoninjasaga.c.a.sJ[2], this, this.e);
        ci a7 = a(15, i + 126, com.donglh.narutoninjasaga.c.a.sJ[3], this, this.e);
        this.g = this.e.b;
        if (this.f != null) {
            this.f.a(this.a, 0);
            this.f.a(this.b, 0);
            this.f.a(this.c, 0);
            this.f.a(a, 0);
            this.f.a(a2, 0);
            if (a3 != null) {
                this.f.a(a3, 0);
            }
            this.f.a(a4, 1);
            this.f.a(a5, 1);
            this.f.a(a6, 1);
            this.f.a(a7, 1);
            this.f.a(0);
        }
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a_(int i) {
    }

    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        switch (guVar.b) {
            case 2000:
                if (guVar.j.i >= 0) {
                    this.f.a(guVar.j.i);
                    return;
                }
                return;
            default:
                return;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:32:0x017e, code lost:
        if (r7 == null) goto L24;
     */
    @Override // com.donglh.narutoninjasaga.d.cn
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void a(int r6, java.lang.Object r7, com.donglh.narutoninjasaga.d.co r8) {
        /*
            Method dump skipped, instructions count: 445
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: com.donglh.narutoninjasaga.d.jl.a(int, java.lang.Object, com.donglh.narutoninjasaga.d.co):void");
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        a(lVar, (int) this.aY, (int) this.aZ, (int) this.aM, (int) this.aN, com.donglh.narutoninjasaga.c.a.rN[3], (byte) 2, false);
        if (Gdx.app.getType() == Application.ApplicationType.Desktop) {
            this.f.a(lVar);
            if (this.f.b.b == 1) {
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.sJ[4], 14, 65, 0, -1, -16777216);
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void b() {
        if (this.f != null) {
            this.f.b();
        }
    }

    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        Vector c = super.c();
        if (Gdx.app.getType() == Application.ApplicationType.Desktop) {
            c.addElement(this.f.c());
        }
        return c;
    }
}
