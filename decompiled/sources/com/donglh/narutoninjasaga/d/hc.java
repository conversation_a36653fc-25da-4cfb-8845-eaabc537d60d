package com.donglh.narutoninjasaga.d;

import java.util.Vector;
/* compiled from: LangLa_kh.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/hc.class */
public final class hc extends he {
    private fx c;
    private dg d;
    public q[] a;
    public byte b;
    private cd e;

    public hc(com.donglh.narutoninjasaga.e.ai aiVar, String[] strArr, byte b) {
        super(aiVar, strArr);
        this.a = new q[16];
        this.b = b;
        this.d = a((this.aM / 2) - 35, this.aN - 33, com.donglh.narutoninjasaga.c.a.aE, this, 5002, -8);
        a(this.d, 0);
        this.c = new fx((byte) 1, 7, a_() + 3, 128, 128, 32, com.donglh.narutoninjasaga.e.aw.c(this.a.length, 4), 4);
        if (b == 68 || b == 69 || b == 70 || b == 71) {
            this.e = a(142, 72, 100, 5, new gz(5003, com.donglh.narutoninjasaga.c.a.rg), this, 0);
            a(this.e, 0);
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v10, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v40, types: [com.donglh.narutoninjasaga.e.ak] */
    /* JADX WARN: Type inference failed for: r0v9, types: [com.donglh.narutoninjasaga.d.hc] */
    @Override // com.donglh.narutoninjasaga.d.he, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        if (this.i.b == q() && guVar.b == 0) {
            d();
        }
        super.a(guVar, i, i2);
        if (this.i.b == 0) {
            switch (guVar.b) {
                case 5001:
                    this.n = a(guVar, this, this.a[guVar.j.i]);
                    return;
                case 5002:
                    ?? r0 = this;
                    try {
                        com.donglh.narutoninjasaga.e.ak akVar = null;
                        if (r0.b == 78) {
                            akVar = new com.donglh.narutoninjasaga.e.ak((byte) -20);
                        } else if (r0.b != 61 && r0.b != 60 && r0.b != 62 && r0.b != 63) {
                            if (r0.b == 68 || r0.b == 69 || r0.b == 70 || r0.b == 71) {
                                com.donglh.narutoninjasaga.e.ak akVar2 = new com.donglh.narutoninjasaga.e.ak((byte) -39);
                                akVar = akVar2;
                                akVar2.a((int) r0.b);
                            }
                        } else {
                            com.donglh.narutoninjasaga.e.ak akVar3 = new com.donglh.narutoninjasaga.e.ak((byte) -34);
                            akVar = akVar3;
                            akVar3.a((int) r0.b);
                        }
                        int i3 = 0;
                        for (int i4 = 0; i4 < r0.a.length; i4++) {
                            if (r0.a[i4] != null) {
                                i3++;
                            }
                        }
                        akVar.a(i3);
                        for (int i5 = 0; i5 < r0.a.length; i5++) {
                            if (r0.a[i5] != null) {
                                akVar.a(r0.a[i5].r);
                                akVar.b(r0.a[i5].e);
                            }
                        }
                        if (r0.e != null) {
                            akVar.a(r0.e.b.b);
                        }
                        r0 = akVar;
                        r0.l();
                        return;
                    } catch (Exception e) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                        return;
                    }
                default:
                    return;
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.cn
    public final void a_(int i) {
    }

    @Override // com.donglh.narutoninjasaga.d.he, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        super.a(lVar);
        if (this.i.b == 0) {
            a(lVar, this.c);
            for (int i = 0; i < this.c.g; i++) {
                for (int i2 = 0; i2 < this.c.o; i2++) {
                    if (this.c.b(i)) {
                        b(lVar, i2 * this.c.f, i * this.c.f, this.a[(i * this.c.o) + i2], (i * this.c.o) + i2 == this.c.i);
                    }
                }
            }
            b(lVar);
            a(lVar, this.aY + 4, this.aZ + a_());
            int i3 = 0;
            int i4 = 0;
            int i5 = 0;
            int i6 = 0;
            int i7 = 0;
            for (int i8 = 0; i8 < this.a.length; i8++) {
                if (this.a[i8] != null) {
                    i3 += this.a[i8].V();
                    if (this.a[i8].Y() || this.a[i8].Z()) {
                        int i9 = ((this.a[i8].h().i / 10) * 100) - 100;
                        if (this.a[i8].h().i / 10 == 6) {
                            i9 = 600;
                        }
                        if (this.a[i8].Z()) {
                            i9 <<= 1;
                        }
                        if (this.a[i8].k()) {
                            i4 += i9;
                        }
                        if (this.a[i8].F()) {
                            i5 += i9;
                        }
                        if (this.a[i8].G()) {
                            i6 += i9;
                        }
                        if (this.a[i8].H()) {
                            i7 += i9;
                        }
                    }
                }
            }
            if (this.b == 78) {
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.kz, this.c.aM + 10, 10, 0, -1, -16777216);
            } else if (this.b == 61) {
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.kF, this.c.aM + 10, 10, 0, -1, -16777216);
            } else if (this.b == 60) {
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.kG, this.c.aM + 10, 10, 0, -1, -16777216);
            } else if (this.b == 62) {
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.kH, this.c.aM + 10, 10, 0, -1, -16777216);
            } else if (this.b == 63) {
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.kI, this.c.aM + 10, 10, 0, -1, -16777216);
            } else if (this.b == 68 || this.b == 69 || this.b == 70 || this.b == 71) {
                if (this.b == 69) {
                    kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.lb, this.c.aM + 10, 10, 0, -1, -16777216);
                    kk.b(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.b(com.donglh.narutoninjasaga.c.a.lf, "200"), this.c.aM + 10, 22, 0, -1, -16777216);
                } else if (this.b == 71) {
                    kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.lc, this.c.aM + 10, 10, 0, -1, -16777216);
                    kk.b(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.b(com.donglh.narutoninjasaga.c.a.lf, "200"), this.c.aM + 10, 22, 0, -1, -16777216);
                } else if (this.b == 70) {
                    kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.ld, this.c.aM + 10, 10, 0, -1, -16777216);
                    kk.b(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.b(com.donglh.narutoninjasaga.c.a.lf, "200"), this.c.aM + 10, 22, 0, -1, -16777216);
                } else if (this.b == 68) {
                    kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.le, this.c.aM + 10, 10, 0, -1, -16777216);
                    kk.b(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.b(com.donglh.narutoninjasaga.c.a.lf, "350"), this.c.aM + 10, 22, 0, -1, -16777216);
                }
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.lg, this.c.aM + 10, 34, 0, -1, -16777216);
            }
            if (i3 > 0) {
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.cB, this.c.aM + 10, 25, 0, -1, -16777216);
                int i10 = 25 + 15;
                kk.b(kk.c, lVar, "- " + com.donglh.narutoninjasaga.e.aw.c(i3) + " ", this.c.aM + 10, 40, 0, -1, -16777216);
                a(lVar, this.c.aM + 10, 40, "- " + com.donglh.narutoninjasaga.e.aw.c(i3) + " ", (byte) 5);
                if (i4 > 0) {
                    i10 += 15;
                    kk.b(kk.c, lVar, "- " + com.donglh.narutoninjasaga.e.aw.c(i4) + " " + com.donglh.narutoninjasaga.e.f.c().U[353].b + " " + com.donglh.narutoninjasaga.c.a.og, this.c.aM + 10, 55, 0, -1, -16777216);
                }
                if (i5 > 0) {
                    i10 += 15;
                    kk.b(kk.c, lVar, "- " + com.donglh.narutoninjasaga.e.aw.c(i5) + " " + com.donglh.narutoninjasaga.e.f.c().U[565].b + " " + com.donglh.narutoninjasaga.c.a.og, this.c.aM + 10, i10, 0, -1, -16777216);
                }
                if (i6 > 0) {
                    i10 += 15;
                    kk.b(kk.c, lVar, "- " + com.donglh.narutoninjasaga.e.aw.c(i6) + " " + com.donglh.narutoninjasaga.e.f.c().U[563].b + " " + com.donglh.narutoninjasaga.c.a.og, this.c.aM + 10, i10, 0, -1, -16777216);
                }
                if (i7 > 0) {
                    kk.b(kk.c, lVar, "- " + com.donglh.narutoninjasaga.e.aw.c(i7) + " " + com.donglh.narutoninjasaga.e.f.c().U[567].b + " " + com.donglh.narutoninjasaga.c.a.og, this.c.aM + 10, i10 + 15, 0, -1, -16777216);
                }
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void b() {
        super.b();
        if (this.i.b == 0) {
            this.c.a();
        }
    }

    @Override // com.donglh.narutoninjasaga.d.he, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        Vector c = super.c();
        if (this.i.b == 0) {
            c.addElement(this.c.a(5001, this));
        }
        return c;
    }

    @Override // com.donglh.narutoninjasaga.d.ej
    public final void d() {
        super.d();
        for (int i = 0; i < this.a.length; i++) {
            if (this.a[i] != null) {
                d.a().g(this.a[i].r)[this.a[i].e] = this.a[i];
                this.a[i] = null;
            }
        }
    }
}
