package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.Input;
import com.badlogic.gdx.net.HttpStatus;
import java.util.Vector;
/* compiled from: LangLa_km.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/hh.class */
public final class hh extends em {
    private static byte[] a;
    private static cb[] b;

    public hh(com.donglh.narutoninjasaga.e.ai aiVar) {
        this.j = (byte) 2;
        this.u = false;
        d(Input.Keys.F7, HttpStatus.SC_OK);
        this.s = aiVar;
        dg a2 = a(com.donglh.narutoninjasaga.c.a.l, 2001);
        a2.g(a2.aY, a2.aZ - 5);
        b = new cb[14];
        for (int i = 0; i < b.length / 2; i++) {
            switch (i) {
                case 3:
                    b[i + 4] = a(20, 25 + (i * 18), com.donglh.narutoninjasaga.c.a.rH[i], i + 4);
                    b[i] = a(135, 25 + (i * 18), com.donglh.narutoninjasaga.c.a.rH[i + 4], i);
                    break;
                case 4:
                case 5:
                    b[i << 1] = a(20, 28 + ((i + 1) * 18), com.donglh.narutoninjasaga.c.a.rH[i << 1], i << 1);
                    b[(i << 1) + 1] = a(135, 28 + (i * 18), com.donglh.narutoninjasaga.c.a.rH[(i << 1) + 1], (i << 1) + 1);
                    break;
                case 6:
                    b[i << 1] = a(20, 28 + ((i + 1) * 18), com.donglh.narutoninjasaga.c.a.rH[i << 1], i << 1);
                    b[i << 1].o = true;
                    b[(i << 1) + 1] = a(135, 28 + (i * 18), com.donglh.narutoninjasaga.c.a.rH[(i << 1) + 1], (i << 1) + 1);
                    break;
                default:
                    b[i] = a(20, 25 + (i * 18), com.donglh.narutoninjasaga.c.a.rH[i], i);
                    b[i + 4] = a(135, 25 + (i * 18), com.donglh.narutoninjasaga.c.a.rH[i + 4], i + 4);
                    break;
            }
        }
        b[9].o = true;
        e();
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a_(int i) {
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        a(lVar, 0, 0, this.aM, this.aN, 80, 55, 56);
        kk.a(kk.c, lVar, com.donglh.narutoninjasaga.c.a.k, 15, 15, 0, -1);
        kk.a(kk.c, lVar, com.donglh.narutoninjasaga.c.a.lv, 15, (int) Input.Keys.BUTTON_R2, 0, -1);
    }

    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        switch (guVar.b) {
            case 2001:
                com.donglh.narutoninjasaga.e.ay.a().a(16, false);
                com.donglh.narutoninjasaga.e.ay.a().a(20, true);
                com.donglh.narutoninjasaga.e.ay.a().a(21, false);
                com.donglh.narutoninjasaga.e.ay.a().a(22, false);
                q();
                s();
                return;
            default:
                return;
        }
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a(int i, Object obj, co coVar) {
        switch (i) {
            case 8:
                com.donglh.narutoninjasaga.e.ay.a().a(20, b[i].a);
                break;
            case 9:
                com.donglh.narutoninjasaga.e.ay.a().a(16, b[i].a);
                break;
            case 10:
                com.donglh.narutoninjasaga.e.ay.a().a(21, b[i].a);
                break;
            case 11:
                com.donglh.narutoninjasaga.e.ay.a().a(1, b[i].a);
                kc.G().J();
                db.a().b(b[i].a);
                break;
            case 12:
                com.donglh.narutoninjasaga.e.ay.a().a(22, b[i].a);
                break;
            case 13:
                com.donglh.narutoninjasaga.e.ay.a().a(0, b[i].a);
                kc.G().J();
                db.a().a(b[i].a);
                break;
            default:
                ho.a[i] = b[i].a ? a[i] : (byte) -1;
                break;
        }
        r();
        s();
    }

    /* JADX WARN: Type inference failed for: r0v6, types: [java.lang.Exception] */
    public static void e() {
        ?? r0;
        try {
            q();
            byte[] e = com.donglh.narutoninjasaga.e.c.e("chanels");
            if (e != null && e.length == ho.a.length) {
                int i = 0;
                while (true) {
                    r0 = i;
                    if (r0 >= e.length) {
                        break;
                    }
                    ho.a[i] = e[i];
                    i++;
                }
            }
        } catch (Exception e2) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
        }
        r();
    }

    private static void q() {
        a = new byte[]{0, 1, 2, 3, 4, 5, 6, 7, 8};
        ho.a = new byte[]{0, 1, 2, -1, -1, -1, -1, 7, 8};
        r();
    }

    /* JADX WARN: Type inference failed for: r0v3, types: [java.lang.Exception] */
    private static void r() {
        ?? r0;
        cb cbVar;
        boolean z;
        try {
            if (b != null) {
                int i = 0;
                while (true) {
                    r0 = i;
                    if (r0 < b.length) {
                        if (b[i] != null) {
                            switch (i) {
                                case 8:
                                    cbVar = b[i];
                                    z = com.donglh.narutoninjasaga.e.ay.a().a(20);
                                    break;
                                case 9:
                                    cbVar = b[i];
                                    z = com.donglh.narutoninjasaga.e.ay.a().a(16);
                                    break;
                                case 10:
                                    cbVar = b[i];
                                    z = com.donglh.narutoninjasaga.e.ay.a().a(21);
                                    break;
                                case 11:
                                    cbVar = b[i];
                                    z = com.donglh.narutoninjasaga.e.ay.a().a(1);
                                    break;
                                case 12:
                                    cbVar = b[i];
                                    z = com.donglh.narutoninjasaga.e.ay.a().a(22);
                                    break;
                                case 13:
                                    cbVar = b[i];
                                    z = com.donglh.narutoninjasaga.e.ay.a().a(0);
                                    break;
                                default:
                                    cbVar = b[i];
                                    if (ho.a[i] < 0) {
                                        z = false;
                                        break;
                                    } else {
                                        z = true;
                                        break;
                                    }
                            }
                            cbVar.a = z;
                        }
                        i++;
                    } else {
                        return;
                    }
                }
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
        }
    }

    private static void s() {
        com.donglh.narutoninjasaga.e.c.a("chanels", ho.a);
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void b() {
    }

    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        super.c();
        Vector vector = new Vector();
        vector.addElement(new gu(0, 0, 0, this.aM, this.aN, this.t, this));
        return vector;
    }
}
