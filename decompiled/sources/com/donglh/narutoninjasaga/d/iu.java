package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.Input;
import com.badlogic.gdx.net.HttpStatus;
import java.util.Vector;
/* compiled from: LangLa_mk.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/iu.class */
public final class iu extends ek {
    private int d;
    dg[] a;
    private fw[] e;
    private fw f;
    q b;
    private ew g;
    private boolean i;
    private dg k;
    public iz c;
    private Vector h = new Vector();
    private int j = -1;

    public iu(int i, int i2, cn cnVar, q qVar) {
        a(i, i2, 1044, cnVar, qVar);
    }

    public iu(int i, int i2, cn cnVar, ew ewVar) {
        this.g = ewVar;
        a(i, i2, 1044, cnVar, ewVar.e);
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v0 */
    /* JADX WARN: Type inference failed for: r0v1, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v621, types: [com.donglh.narutoninjasaga.d.iu] */
    private void a(int i, int i2, int i3, cn cnVar, q qVar) {
        String[] c;
        ?? r0 = qVar;
        if (r0 != 0) {
            try {
                this.d = 1044;
                this.l = cnVar;
                this.b = qVar;
                g(i, i2);
                a_(Input.Keys.F7, 142);
                Vector vector = new Vector();
                vector.add(new dg(0, 3, "", cnVar, 100, 52));
                if (cnVar instanceof et) {
                    et etVar = (et) cnVar;
                    if (etVar.r() == etVar.q()) {
                        a(vector, cnVar, etVar);
                    }
                }
                if (cnVar instanceof eb) {
                    vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aG, cnVar, Input.Keys.BUTTON_START, 15));
                }
                if (cnVar instanceof bn) {
                    bn bnVar = (bn) cnVar;
                    if (bnVar.r() == 0) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.ak, cnVar, 101, 15));
                    } else if (bnVar.t() == 0) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aj, cnVar, Input.Keys.BUTTON_R1, 15));
                    }
                } else if (cnVar instanceof bs) {
                    vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.ak, cnVar, 101, 15));
                } else if (cnVar instanceof hg) {
                    hg hgVar = (hg) cnVar;
                    if (hgVar.w() == 0 && hgVar.G.c()) {
                        if (hgVar.x() != 0 || (qVar.c != 788 && (qVar.h == null || !qVar.h.contains(";333,0")))) {
                            if (hgVar.x() == 0 && (qVar.c == 789 || qVar.c == 812 || qVar.c == 855 || qVar.c == 880 || ((qVar.h != null && qVar.h.startsWith("340,789")) || ((qVar.h != null && qVar.h.startsWith("340,812")) || ((qVar.h != null && qVar.h.startsWith("340,855")) || (qVar.h != null && qVar.h.startsWith("340,880"))))))) {
                                vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.qs, cnVar, 170, 15));
                            }
                        } else {
                            vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.qu, cnVar, 170, 15));
                        }
                        if (hgVar.x() == 0 && qVar.i()) {
                            vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aG, cnVar, Input.Keys.BUTTON_R2, 15));
                        } else if (hgVar.x() == 0 && hgVar.G.p > 17) {
                            vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.ql, cnVar, 167, 15));
                            vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.qk, cnVar, 168, 15));
                        } else if (hgVar.x() == 1) {
                            vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aG, cnVar, 128, 15));
                        }
                    }
                } else if (cnVar instanceof hu) {
                    hu huVar = (hu) cnVar;
                    if (huVar.e() == 0) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aH, cnVar, Input.Keys.BUTTON_THUMBR, 15));
                    } else if (huVar.t() == 0) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aG, cnVar, Input.Keys.BUTTON_THUMBL, 15));
                    }
                } else if (cnVar instanceof ae) {
                    ae aeVar = (ae) cnVar;
                    if (aeVar.a == 0) {
                        if (aeVar.e() == 0) {
                            if (aeVar.h == 1) {
                                vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aH, cnVar, 126, 15));
                            }
                        } else if (aeVar.t() == 0 && !qVar.q) {
                            vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.bb, cnVar, 125, 15));
                        }
                    }
                } else if (cnVar instanceof ap) {
                    ap apVar = (ap) cnVar;
                    if (apVar.r() == 0) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aH, cnVar, 111, 15));
                    } else if (apVar.t() == 0 && qVar.q()) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.bb, cnVar, Input.Keys.BUTTON_MODE, 15));
                    }
                } else if (cnVar instanceof am) {
                    am amVar = (am) cnVar;
                    if (amVar.r() == 0) {
                        if (amVar.c == 1 || amVar.c == 3 || amVar.c == 4) {
                            vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aH, cnVar, 113, 15));
                        }
                    } else if (amVar.t() == 0 && qVar.t()) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.bb, cnVar, Input.Keys.FORWARD_DEL, 15));
                    }
                } else if (cnVar instanceof br) {
                    br brVar = (br) cnVar;
                    if (brVar.r() == 0) {
                        if (brVar.c == 1) {
                            vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aH, cnVar, 115, 15));
                        }
                    } else if (brVar.t() == 0 && qVar.x()) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.bb, cnVar, 114, 15));
                    }
                } else if (cnVar instanceof bq) {
                    bq bqVar = (bq) cnVar;
                    if (bqVar.r() == 0) {
                        if (bqVar.c == 1) {
                            vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aH, cnVar, Input.Keys.NUMPAD_4, 15));
                        }
                    } else if (bqVar.t() == 0 && qVar.y()) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.bb, cnVar, Input.Keys.NUMPAD_3, 15));
                    }
                } else if (cnVar instanceof t) {
                    t tVar = (t) cnVar;
                    if (tVar.r() == 0) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aH, cnVar, 117, 15));
                    } else if (tVar.t() == 0 && qVar.A()) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.bb, cnVar, 116, 15));
                    }
                } else if (cnVar instanceof u) {
                    u uVar = (u) cnVar;
                    if (uVar.r() == 0) {
                        if (uVar.b == 1 || uVar.b == 3 || uVar.b == 4) {
                            vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aH, cnVar, -117, 15));
                        }
                    } else if (uVar.t() == 0 && qVar.B()) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.bb, cnVar, -116, 15));
                    }
                } else if (cnVar instanceof v) {
                    v vVar = (v) cnVar;
                    if (vVar.r() == 0) {
                        if (vVar.b == 1 || vVar.b == 3 || vVar.b == 4) {
                            vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aH, cnVar, -118, 15));
                        }
                    } else if (vVar.t() == 0 && qVar.C()) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.bb, cnVar, -119, 15));
                    }
                } else if (cnVar instanceof ab) {
                    ab abVar = (ab) cnVar;
                    if (abVar.r() == 0) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aH, cnVar, 166, 15));
                    } else if (abVar.t() == 0 && qVar.D()) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.bb, cnVar, 165, 15));
                    }
                } else if (cnVar instanceof an) {
                    an anVar = (an) cnVar;
                    if (anVar.r() == 0) {
                        if (anVar.c == 1 || anVar.c == 2) {
                            vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aH, cnVar, 119, 15));
                        }
                    } else if (anVar.t() == 0 && qVar.E()) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.bb, cnVar, 118, 15));
                    }
                } else if (cnVar instanceof jn) {
                    jn jnVar = (jn) cnVar;
                    if (jnVar.r() == 0) {
                        if (jnVar.c == 1 || jnVar.c == 3) {
                            vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aH, cnVar, Input.Keys.NUMPAD_2, 15));
                        }
                    } else if (jnVar.t() == 0 && qVar.w()) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.bb, cnVar, Input.Keys.NUMPAD_1, 15));
                    }
                } else if (cnVar instanceof ag) {
                    ag agVar = (ag) cnVar;
                    if (agVar.r() == 0) {
                        if (agVar.c == 1 || agVar.c == 2) {
                            vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aH, cnVar, 172, 15));
                        }
                    } else if (agVar.t() == 0 && qVar.I()) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.bb, cnVar, 171, 15));
                    }
                } else if (cnVar instanceof ai) {
                    ai aiVar = (ai) cnVar;
                    if (aiVar.r() == 0) {
                        if (aiVar.c == 1 || aiVar.c == 2) {
                            vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aH, cnVar, Input.Keys.NUMPAD_0, 15));
                        }
                    } else if (aiVar.t() == 0 && qVar.J()) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.bb, cnVar, 143, 15));
                    }
                } else if (cnVar instanceof af) {
                    af afVar = (af) cnVar;
                    if (afVar.r() == 0) {
                        if (afVar.c == 1 || afVar.c == 2) {
                            vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aH, cnVar, 158, 15));
                        }
                    } else if (afVar.t() == 0 && qVar.K()) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.bb, cnVar, 157, 15));
                    }
                } else if (cnVar instanceof ak) {
                    ak akVar = (ak) cnVar;
                    if (akVar.r() == 0) {
                        if (akVar.c == 1 || akVar.c == 2) {
                            vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aH, cnVar, 160, 15));
                        }
                    } else if (akVar.t() == 0 && qVar.L()) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.bb, cnVar, 159, 15));
                    }
                } else if (cnVar instanceof aj) {
                    aj ajVar = (aj) cnVar;
                    if (ajVar.r() == 0) {
                        if (ajVar.c == 1 || ajVar.c == 2) {
                            vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aH, cnVar, 162, 15));
                        }
                    } else if (ajVar.t() == 0 && qVar.M()) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.bb, cnVar, 161, 15));
                    }
                } else if (cnVar instanceof jk) {
                    jk jkVar = (jk) cnVar;
                    if (jkVar.r() == 0) {
                        if (jkVar.g != 1 && jkVar.g != 2) {
                            if (jkVar.g == 3) {
                                vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aH, cnVar, 121, 15));
                            }
                        } else {
                            vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.ak, cnVar, 122, 15));
                            if (jkVar.v() == 1) {
                                vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.fw, cnVar, Input.Keys.END, 15));
                            }
                        }
                    } else if (jkVar.t() == 0 && !qVar.q && qVar.g == -1) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.bb, cnVar, 120, 15));
                    }
                } else if (cnVar instanceof ji) {
                    ji jiVar = (ji) cnVar;
                    if (jiVar.r() == 1) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aH, cnVar, 124, 15));
                    } else if (jiVar.r() == jiVar.q() && jiVar.t() == 0 && !qVar.q) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.bb, cnVar, 123, 15));
                    }
                } else if (cnVar instanceof bu) {
                    bu buVar = (bu) cnVar;
                    if (buVar.r() == 0) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aH, cnVar, Input.Keys.CONTROL_RIGHT, 15));
                    } else if (buVar.t() == 0) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.bb, cnVar, Input.Keys.CONTROL_LEFT, 15));
                    }
                } else if (cnVar instanceof jd) {
                    jd jdVar = (jd) cnVar;
                    if (jdVar.r() == 0) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aH, cnVar, 156, 15));
                    } else if (jdVar.t() == 0) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.bb, cnVar, 155, 15));
                    }
                } else if (cnVar instanceof hc) {
                    hc hcVar = (hc) cnVar;
                    if (hcVar.r() == 0) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aH, cnVar, 136, 15));
                    } else if (hcVar.b == 78 && hcVar.t() == 0 && qVar.V() > 0) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.bb, cnVar, 135, 15));
                    } else if (((hcVar.b == 61 || hcVar.b == 69) && hcVar.t() == 0 && qVar.R()) || (((hcVar.b == 60 || hcVar.b == 68) && hcVar.t() == 0 && qVar.S()) || (((hcVar.b == 62 || hcVar.b == 71) && hcVar.t() == 0 && qVar.T()) || ((hcVar.b == 63 || hcVar.b == 70) && hcVar.t() == 0 && qVar.U())))) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.bb, cnVar, 135, 15));
                    }
                } else if (cnVar instanceof cq) {
                    cq cqVar = (cq) cnVar;
                    if (cqVar.r() == 0) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aH, cnVar, 142, 15));
                    } else if (cqVar.t() == 0 && qVar.c == 310) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.bb, cnVar, 141, 15));
                    }
                } else if (cnVar instanceof dh) {
                    dh dhVar = (dh) cnVar;
                    if (dhVar.r() == 0) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aH, cnVar, 164, 15));
                    } else if (dhVar.t() == 0 && qVar.p()) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.bb, cnVar, 163, 15));
                    }
                } else if (cnVar instanceof ao) {
                    ao aoVar = (ao) cnVar;
                    if (aoVar.r() == 0) {
                        if (aoVar.c == 1) {
                            vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aH, cnVar, 139, 15));
                        }
                    } else if (aoVar.t() == 0 && qVar.o()) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.bb, cnVar, 138, 15));
                    }
                } else if (cnVar instanceof ah) {
                    ah ahVar = (ah) cnVar;
                    if (ahVar.r() == 0) {
                        if (ahVar.c == 0) {
                            vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aH, cnVar, Input.Keys.NUMPAD_7, 15));
                            vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.l, cnVar, Input.Keys.NUMPAD_8, 15));
                        }
                    } else if (ahVar.t() == 0 && qVar.r()) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.bb, cnVar, Input.Keys.NUMPAD_6, 15));
                    }
                } else if (cnVar instanceof bp) {
                    bp bpVar = (bp) cnVar;
                    if (bpVar.r() == 0) {
                        if (bpVar.d == 1) {
                            vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aH, cnVar, 154, 15));
                        }
                    } else if (bpVar.t() == 0 && qVar.s()) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.bb, cnVar, Input.Keys.NUMPAD_9, 15));
                    }
                } else if ((cnVar instanceof hq) && ((hq) cnVar).h == 5) {
                    vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.fx, cnVar, 140, 15));
                }
                if (this.g != null) {
                    vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.ak, cnVar, 134, 15));
                }
                if (vector.size() > 6 && this.k != null) {
                    vector.remove(this.k);
                }
                this.a = new dg[vector.size()];
                for (int i4 = 0; i4 < vector.size(); i4++) {
                    this.a[i4] = (dg) vector.elementAt(i4);
                    if (i4 > 0) {
                        this.a[i4].a_(42, 22);
                        this.a[i4].g((this.aM - 3) - (i4 * 47), this.aN - 26);
                    }
                }
                if ((cnVar instanceof ih) || (this.a.length <= 1 && !qVar.i() && qVar.c != 435)) {
                    a_(220, 120);
                }
                this.a[0].g(this.aM - 17, 3);
                this.e = a(this.a);
                if (qVar.i() || qVar.p > 0) {
                    this.h.addElement(new ey("", -1, -16777216));
                }
                if (qVar.g >= 0) {
                    if (cnVar instanceof bn) {
                        if (((et) cnVar).r() == 0) {
                            this.h.addElement(new ey(com.donglh.narutoninjasaga.c.a.fy + qVar.P(), -2560, -16777216));
                        } else {
                            this.h.addElement(new ey(com.donglh.narutoninjasaga.c.a.fy + qVar.Q(), -2560, -16777216));
                        }
                    } else if (!(cnVar instanceof ih) && !(cnVar instanceof bs) && !(cnVar instanceof ay) && !(cnVar instanceof bc) && !(cnVar instanceof ac) && !(cnVar instanceof bv) && !(cnVar instanceof ad) && !(cnVar instanceof bt)) {
                        if (cnVar instanceof hq) {
                            if (qVar.g > 2592000000L) {
                                this.h.addElement(new ey(com.donglh.narutoninjasaga.c.a.fy + qVar.Q(), -2560, -16777216));
                            }
                        } else {
                            this.h.addElement(new ey(com.donglh.narutoninjasaga.c.a.fy + qVar.Q(), -2560, -16777216));
                        }
                    } else {
                        this.h.addElement(new ey(com.donglh.narutoninjasaga.c.a.fy + qVar.P(), -2560, -16777216));
                    }
                }
                if (qVar.h().g > 0 && qVar.h().g != d.a().H) {
                    this.h.addElement(new ey(com.donglh.narutoninjasaga.c.a.fz + com.donglh.narutoninjasaga.e.f.c().R[qVar.h().g].b, -65536, -16777216));
                }
                if (qVar.h().i > 0) {
                    if (d.a().i() < qVar.h().i) {
                        this.h.addElement(new ey(com.donglh.narutoninjasaga.c.a.fA + ((int) qVar.h().i), -65536, -16777216));
                    } else {
                        this.h.addElement(new ey(com.donglh.narutoninjasaga.c.a.fA + ((int) qVar.h().i), -1, -16777216));
                    }
                }
                if (qVar.o > 0) {
                    String str = "";
                    if (cnVar instanceof ih) {
                        ih ihVar = (ih) cnVar;
                        if (ihVar.a == 32) {
                            str = com.donglh.narutoninjasaga.c.a.kv;
                        } else if (ihVar.a == 34) {
                            str = com.donglh.narutoninjasaga.c.a.kw;
                        } else if (ihVar.a == 33) {
                            str = com.donglh.narutoninjasaga.c.a.kx;
                        } else if (ihVar.a == 35) {
                            str = com.donglh.narutoninjasaga.c.a.kt;
                        } else {
                            switch (qVar.h().f) {
                                case 0:
                                    str = com.donglh.narutoninjasaga.c.a.rG[0];
                                    break;
                                case 1:
                                    str = com.donglh.narutoninjasaga.c.a.rG[1];
                                    break;
                                case 2:
                                    str = com.donglh.narutoninjasaga.c.a.rG[2];
                                    break;
                                case 3:
                                    str = com.donglh.narutoninjasaga.c.a.rG[3];
                                    break;
                                case 4:
                                    str = com.donglh.narutoninjasaga.c.a.rG[4];
                                    break;
                                case 5:
                                    str = com.donglh.narutoninjasaga.c.a.rG[5];
                                    break;
                                case 6:
                                    str = com.donglh.narutoninjasaga.c.a.rG[6];
                                    break;
                                case 7:
                                    str = com.donglh.narutoninjasaga.c.a.rG[7];
                                    break;
                                case 8:
                                    str = com.donglh.narutoninjasaga.c.a.rG[8];
                                    break;
                                case 9:
                                    str = com.donglh.narutoninjasaga.c.a.rG[9];
                                    break;
                            }
                        }
                        this.h.addElement(new ey(com.donglh.narutoninjasaga.c.a.fB + str + ": " + com.donglh.narutoninjasaga.e.aw.c(qVar.o), -16711681, -16777216));
                    } else if (cnVar instanceof bn) {
                        bn bnVar2 = (bn) cnVar;
                        if (bnVar2.a == 32) {
                            str = com.donglh.narutoninjasaga.c.a.kv;
                        } else if (bnVar2.a == 34) {
                            str = com.donglh.narutoninjasaga.c.a.kw;
                        } else if (bnVar2.a == 33) {
                            str = com.donglh.narutoninjasaga.c.a.kx;
                        } else if (bnVar2.a == 35) {
                            str = com.donglh.narutoninjasaga.c.a.kt;
                        } else {
                            switch (qVar.h().f) {
                                case 0:
                                    str = com.donglh.narutoninjasaga.c.a.rG[0];
                                    break;
                                case 1:
                                    str = com.donglh.narutoninjasaga.c.a.rG[1];
                                    break;
                                case 2:
                                    str = com.donglh.narutoninjasaga.c.a.rG[2];
                                    break;
                                case 3:
                                    str = com.donglh.narutoninjasaga.c.a.rG[3];
                                    break;
                                case 4:
                                    str = com.donglh.narutoninjasaga.c.a.rG[4];
                                    break;
                                case 5:
                                    str = com.donglh.narutoninjasaga.c.a.rG[5];
                                    break;
                                case 6:
                                    str = com.donglh.narutoninjasaga.c.a.rG[6];
                                    break;
                                case 7:
                                    str = com.donglh.narutoninjasaga.c.a.rG[7];
                                    break;
                                case 8:
                                    str = com.donglh.narutoninjasaga.c.a.rG[8];
                                    break;
                                case 9:
                                    str = com.donglh.narutoninjasaga.c.a.rG[9];
                                    break;
                            }
                        }
                        this.h.addElement(new ey(com.donglh.narutoninjasaga.c.a.fB + str + ": " + com.donglh.narutoninjasaga.e.aw.c(qVar.o), -16711681, -16777216));
                    }
                }
                if (qVar.h().j > 0) {
                    if (d.a().F < qVar.h().j) {
                        this.h.addElement(new ey(com.donglh.narutoninjasaga.c.a.fC + qVar.h().j, -65536, -16777216));
                    } else {
                        this.h.addElement(new ey(com.donglh.narutoninjasaga.c.a.fC + qVar.h().j, -1, -16777216));
                    }
                }
                if (qVar.h().e < 2 && qVar.h().e != d.a().G) {
                    this.h.addElement(new ey(com.donglh.narutoninjasaga.c.a.fD + com.donglh.narutoninjasaga.c.a.rj[qVar.h().e], -65536, -16777216));
                }
                switch (qVar.f) {
                    case 1:
                        this.h.addElement(new ey(com.donglh.narutoninjasaga.c.a.fE + b.e() + qVar.b(), -1, -16777216));
                        break;
                    case 2:
                        this.h.addElement(new ey(com.donglh.narutoninjasaga.c.a.fE + b.f() + qVar.b(), -1, -16777216));
                        break;
                    case 3:
                        this.h.addElement(new ey(com.donglh.narutoninjasaga.c.a.fE + b.g() + qVar.b(), -1, -16777216));
                        break;
                    case 4:
                        this.h.addElement(new ey(com.donglh.narutoninjasaga.c.a.fE + b.h() + qVar.b(), -1, -16777216));
                        break;
                    case 5:
                        this.h.addElement(new ey(com.donglh.narutoninjasaga.c.a.fE + b.i() + qVar.b(), -1, -16777216));
                        break;
                }
                if (qVar.q) {
                    this.h.addElement(new ey(com.donglh.narutoninjasaga.c.a.fF, -1, -16777216));
                } else {
                    this.h.addElement(new ey(com.donglh.narutoninjasaga.c.a.fG, -1, -16777216));
                }
                if (qVar.V() > 0) {
                    String b = com.donglh.narutoninjasaga.e.aw.b(com.donglh.narutoninjasaga.c.a.fH, com.donglh.narutoninjasaga.e.aw.c(qVar.V()));
                    if (qVar.Y() || qVar.Z()) {
                        int i5 = ((qVar.h().i / 10) * 100) - 100;
                        if (qVar.h().i / 10 == 6) {
                            i5 = 600;
                        }
                        if (qVar.Z()) {
                            i5 <<= 1;
                        }
                        if (qVar.k()) {
                            b = b + com.donglh.narutoninjasaga.e.aw.b(com.donglh.narutoninjasaga.c.a.oh, com.donglh.narutoninjasaga.e.aw.c(i5)) + com.donglh.narutoninjasaga.e.f.c().U[353].b;
                        } else if (qVar.F()) {
                            b = b + com.donglh.narutoninjasaga.e.aw.b(com.donglh.narutoninjasaga.c.a.oh, com.donglh.narutoninjasaga.e.aw.c(i5)) + com.donglh.narutoninjasaga.e.f.c().U[565].b;
                        } else if (qVar.G()) {
                            b = b + com.donglh.narutoninjasaga.e.aw.b(com.donglh.narutoninjasaga.c.a.oh, com.donglh.narutoninjasaga.e.aw.c(i5)) + com.donglh.narutoninjasaga.e.f.c().U[563].b;
                        } else if (qVar.H()) {
                            b = b + com.donglh.narutoninjasaga.e.aw.b(com.donglh.narutoninjasaga.c.a.oh, com.donglh.narutoninjasaga.e.aw.c(i5)) + com.donglh.narutoninjasaga.e.f.c().U[567].b;
                        }
                    }
                    this.h.addElement(new ey(b + com.donglh.narutoninjasaga.c.a.fI, -4675551, -16777216));
                }
                if (qVar.h().d) {
                    this.h.addElement(new ey(com.donglh.narutoninjasaga.c.a.fJ, -1, -16777216));
                }
                if (this.g != null) {
                    this.h.addElement(new ey(com.donglh.narutoninjasaga.c.a.fK + com.donglh.narutoninjasaga.e.aw.c(this.g.c), -2560, -16777216));
                } else if (qVar.d() > 0) {
                    this.h.addElement(new ey(com.donglh.narutoninjasaga.c.a.fK + com.donglh.narutoninjasaga.e.aw.b(qVar.f()), -1, -16777216));
                } else {
                    this.h.addElement(new ey(com.donglh.narutoninjasaga.c.a.fL + com.donglh.narutoninjasaga.e.aw.b(qVar.g()), -1, -16777216));
                }
                if (qVar.h().c.length() > 0 && qVar.h().f != 10) {
                    if (qVar.h().f == 34) {
                        c = kk.c(kk.c, com.donglh.narutoninjasaga.c.a.ml + " " + qVar.h().b, this.aM - 30);
                    } else {
                        c = kk.c(kk.c, qVar.h().c, this.aM - 30);
                    }
                    for (String str2 : c) {
                        this.h.addElement(new ey(str2, -1, -16777216));
                    }
                }
                s[] N = qVar.N();
                int i6 = 1;
                if (N != null) {
                    int i7 = 0;
                    if (this.l instanceof hg) {
                        hg hgVar2 = (hg) this.l;
                        if (hgVar2.w() == 0) {
                            i7 = qVar.a(hgVar2.x(), hgVar2.G);
                        }
                    }
                    boolean z = false;
                    boolean z2 = false;
                    for (int i8 = 0; i8 < N.length; i8++) {
                        if ((N[i8].a().c < 3 || N[i8].a().c > 7) && N[i8].a().c != 10 && N[i8].a().c != 11 && N[i8].a().c != 15 && N[i8].a().c != 16) {
                            if (N[i8].a().c == 2) {
                                if (!z2) {
                                    z2 = true;
                                    this.h.addElement(new ey(com.donglh.narutoninjasaga.c.a.fO + b(qVar.h().f, qVar.f), -1, -11184811));
                                }
                                if (i7 > 0) {
                                    this.h.addElement(new ey(N[i8].b(), -7812062, -16777216));
                                    i7--;
                                } else {
                                    this.h.addElement(new ey(N[i8].b(), -7631732, -16777216));
                                }
                            } else if (N[i8].a().c == 8) {
                                this.h.addElement(new ey("(" + com.donglh.narutoninjasaga.e.f.c().U[N[i8].h()].b + " " + com.donglh.narutoninjasaga.c.a.de + " " + N[i8].i() + ") " + N[i8].b(), -7340813, -16777216));
                            } else if (N[i8].a().c == 14) {
                                this.h.addElement(new ey(i6 + ". " + N[i8].b(), -16742145, -16777216));
                                i6++;
                            } else if (N[i8].a().a >= 53 && N[i8].a().a <= 62) {
                                this.h.addElement(new ey(N[i8].c(), -10831436, -16777216));
                            } else if (N[i8].a().a != 128 && N[i8].a().a != 305) {
                                if (N[i8].a().a == 336) {
                                    this.h.addElement(new ey(N[i8].c(), -623877, -16777216));
                                } else if (N[i8].a().a == 337) {
                                    this.h.addElement(new ey(N[i8].b() + com.donglh.narutoninjasaga.e.aw.c((N[i8 - 1].e() + 1) * 5000000), -2560, -16777216));
                                } else if (N[i8].d() == 105) {
                                    this.h.addElement(new ey(com.donglh.narutoninjasaga.c.a.fP + b.c() + com.donglh.narutoninjasaga.e.f.c().N[N[i8].a[1]].b, -1, -16777216));
                                    this.h.addElement(new ey(com.donglh.narutoninjasaga.c.a.fQ, -1, -16777216));
                                } else if (N[i8].d() == 148) {
                                    this.h.addElement(new ey(N[i8].b(), -3407617, -16777216));
                                } else if (N[i8].d() == 159) {
                                    this.h.addElement(new ey(N[i8].b(), -196483, -16777216));
                                } else if (N[i8].d() == 163) {
                                    this.h.addElement(new ey(N[i8].b(), -48128, -16777216));
                                } else if (N[i8].d() != 164 && N[i8].a().a != 340) {
                                    if (N[i8].d() == 165) {
                                        this.h.addElement(new ey(N[i8].b(), -16712186, -16777216));
                                    } else if (N[i8].d() == 361) {
                                        this.h.addElement(new ey(N[i8].b(), -13176412, -16777216));
                                    } else {
                                        this.h.addElement(new ey(N[i8].b(), -10831436, -16777216));
                                    }
                                } else {
                                    this.h.addElement(new ey(N[i8].b(), -4588032, -16777216));
                                }
                            } else {
                                this.h.addElement(new ey(N[i8].c(), -2560, -16777216));
                            }
                        } else {
                            if (!z) {
                                z = true;
                                if (qVar.h().f == 11) {
                                    this.h.addElement(new ey(com.donglh.narutoninjasaga.c.a.fM, -1, -11184811));
                                } else {
                                    this.h.addElement(new ey(com.donglh.narutoninjasaga.c.a.fN, -1, -11184811));
                                }
                            }
                            if ((N[i8].a().c != 15 || qVar.p < 2) && ((N[i8].a().c != 3 || qVar.p < 4) && ((N[i8].a().c != 4 || qVar.p < 8) && ((N[i8].a().c != 5 || qVar.p < 12) && ((N[i8].a().c != 6 || qVar.p < 14) && ((N[i8].a().c != 7 || qVar.p < 16) && ((N[i8].a().c != 10 || qVar.p < 17) && ((N[i8].a().c != 11 || qVar.p < 18) && (N[i8].a().c != 16 || qVar.p < 19))))))))) {
                                this.h.addElement(new ey(N[i8].b(), -7631732, -16777216));
                            } else {
                                this.h.addElement(new ey(N[i8].b(), -2560, -16777216));
                            }
                        }
                    }
                }
                if (qVar.h().f == 99 && qVar.h.length() > 0) {
                    this.h.addElement(new ey("", -10831436, -16777216));
                    this.h.addElement(new ey("", -10831436, -16777216));
                }
                r0 = this;
                r0.f = new fw((byte) 1, 4, 4, this.aM - 25, Input.Keys.BUTTON_R2, 15, 1 + this.h.size());
            } catch (Exception e) {
                com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
            }
        }
    }

    public final void a() {
        this.i = true;
        if (this.b.h().f == 100) {
            a_(220, 120);
        } else {
            a_(Input.Keys.F7, 120);
        }
        this.f = new fw((byte) 1, 4, 4, this.aM - 25, Input.Keys.BUTTON_R2, 15, 1 + this.h.size());
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        int i = lVar.a;
        int i2 = lVar.b;
        a(lVar, 0, 0, this.aM, this.aN, -11, 55, 56);
        a(lVar, this.f);
        if (this.g != null) {
            b(lVar, this.aM - 58, 3, this.g.e, false);
        }
        b(lVar, 5, 7);
        if (this.b.i() || this.b.p > 0) {
            int i3 = 8;
            if (this.b.p >= 17) {
                i3 = this.b.p - 8;
            }
            for (int i4 = 0; i4 < i3; i4++) {
                com.donglh.narutoninjasaga.e.r.a(lVar, HttpStatus.SC_METHOD_FAILURE, 0, 15 + (i4 * 20), 22, 3);
                if (this.b.p >= 18) {
                    if (i4 == this.j) {
                        com.donglh.narutoninjasaga.e.r.a(lVar, 827, 0, 15 + (i4 * 20), 22, 3);
                    } else {
                        if (this.b.p == 19) {
                            lVar.d(10.0f);
                        }
                        com.donglh.narutoninjasaga.e.r.a(lVar, 545 + ((com.donglh.narutoninjasaga.e.f.c().i / 3) % 5), 0, 15 + (i4 * 20), 22, 3);
                        if (this.b.p == 19) {
                            lVar.i();
                        }
                    }
                } else if (this.b.p >= 17) {
                    if (i4 == this.j) {
                        com.donglh.narutoninjasaga.e.r.a(lVar, 827, 0, 15 + (i4 * 20), 22, 3);
                    } else {
                        com.donglh.narutoninjasaga.e.r.a(lVar, 822 + ((com.donglh.narutoninjasaga.e.f.c().i / 5) % 5), 0, 15 + (i4 * 20), 22, 3);
                    }
                } else if (this.b.p > 0) {
                    if (this.b.p >= 2 && this.b.p / 2 > i4) {
                        if (this.b.p >= 16) {
                            if (i4 == this.j) {
                                lVar.d();
                            }
                            com.donglh.narutoninjasaga.e.r.a(lVar, 430, 0, 15 + (i4 * 20), 22, 3);
                            lVar.e();
                        } else if (this.b.p >= 14) {
                            if (i4 == this.j) {
                                lVar.d();
                            }
                            com.donglh.narutoninjasaga.e.r.a(lVar, 428, 0, 15 + (i4 * 20), 22, 3);
                            lVar.e();
                        } else if (this.b.p >= 12) {
                            if (i4 == this.j) {
                                lVar.d();
                            }
                            com.donglh.narutoninjasaga.e.r.a(lVar, HttpStatus.SC_FAILED_DEPENDENCY, 0, 15 + (i4 * 20), 22, 3);
                            lVar.e();
                        } else if (this.b.p >= 8) {
                            if (this.b.p >= 10) {
                                lVar.c(-184786);
                            } else {
                                lVar.c(-93738);
                            }
                            if (i4 == this.j) {
                                lVar.d();
                            }
                            com.donglh.narutoninjasaga.e.r.a(lVar, 426, 0, 15 + (i4 * 20), 22, 3);
                            lVar.e();
                            lVar.c();
                        } else if (this.b.p >= 4) {
                            com.donglh.narutoninjasaga.e.r.a(lVar, HttpStatus.SC_UNPROCESSABLE_ENTITY, 0, 15 + (i4 * 20), 22, 3);
                        } else {
                            com.donglh.narutoninjasaga.e.r.a(lVar, 47, 0, 15 + (i4 * 20), 22, 3);
                        }
                    }
                    if (this.b.p % 2 > 0 && this.b.p / 2 == i4) {
                        if (this.b.p >= 16) {
                            if (i4 == this.j) {
                                lVar.d();
                            }
                            com.donglh.narutoninjasaga.e.r.a(lVar, 431, 0, 15 + (i4 * 20), 22, 3);
                            lVar.e();
                        } else if (this.b.p >= 14) {
                            if (i4 == this.j) {
                                lVar.d();
                            }
                            com.donglh.narutoninjasaga.e.r.a(lVar, 429, 0, 15 + (i4 * 20), 22, 3);
                            lVar.e();
                        } else if (this.b.p >= 12) {
                            if (i4 == this.j) {
                                lVar.d();
                            }
                            com.donglh.narutoninjasaga.e.r.a(lVar, 425, 0, 15 + (i4 * 20), 22, 3);
                            lVar.e();
                        } else if (this.b.p >= 8) {
                            if (this.b.p >= 10) {
                                lVar.c(-184786);
                            } else {
                                lVar.c(-93738);
                            }
                            if (i4 == this.j) {
                                lVar.d();
                            }
                            com.donglh.narutoninjasaga.e.r.a(lVar, 427, 0, 15 + (i4 * 20), 22, 3);
                            lVar.e();
                            lVar.c();
                        } else if (this.b.p >= 4) {
                            com.donglh.narutoninjasaga.e.r.a(lVar, HttpStatus.SC_LOCKED, 0, 15 + (i4 * 20), 22, 3);
                        } else {
                            com.donglh.narutoninjasaga.e.r.a(lVar, 48, 0, 15 + (i4 * 20), 22, 3);
                        }
                    }
                }
            }
        }
        for (int i5 = 0; i5 < this.h.size(); i5++) {
            if (this.f.b(i5)) {
                ey eyVar = (ey) this.h.elementAt(i5);
                if (eyVar.b == -13176412) {
                    kk.a(kk.c, lVar, eyVar.a, 5, this.f.f + 9 + (i5 * this.f.f), 0, -15106770, 2, 1);
                    kk.a(kk.c, lVar, eyVar.a, 5, this.f.f + 8 + (i5 * this.f.f), 0, eyVar.b, 2, 1);
                } else {
                    kk.c(kk.c, lVar, eyVar.a, 5, this.f.f + 8 + (i5 * this.f.f), 0, eyVar.b, eyVar.c);
                }
            }
        }
        if (this.b.h().f == 99 && this.b.h != null && this.b.h.length() > 0) {
            s sVar = this.b.N()[0];
            if (sVar.a != null && sVar.a.length > 4) {
                com.donglh.narutoninjasaga.e.r.b(lVar, sVar.a[4], 0, 40, (this.h.size() - 2) * this.f.f, 0);
            }
        }
        b(lVar);
        a(lVar, i, i2);
        this.f.a(lVar);
        a(lVar, this.a, i, i2);
    }

    public final void b(l lVar, int i, int i2) {
        if (this.b.p > 0) {
            String str = this.b.h().b + " (+" + ((int) this.b.p) + ")";
            if (this.b.h().f == 11 || this.b.h().f == 12 || this.b.h().f == 13) {
                str = this.b.h().b;
            }
            if (this.b.p >= 19) {
                kk.a(kk.e, lVar, str, i + 1, i2 + 1, 0, -14915496, 1, 0);
            }
            if (this.b.p >= 18) {
                kk.a(kk.e, lVar, str, i, i2, 0, a(this.b.p), 2, 0);
                return;
            } else if (this.b.p == 17) {
                kk.a(kk.e, lVar, str, i, i2, 0, a(this.b.p), 2, 0);
                return;
            } else if (this.b.p >= 15) {
                kk.a(kk.e, lVar, str, i, i2, 0, a(this.b.p), 2, 0);
                return;
            } else if (this.b.p < 12 && this.b.p < 8) {
                kk.b(kk.e, lVar, str, i, i2, 0, a(this.b.p), 0);
                return;
            } else {
                kk.a(kk.e, lVar, str, i, i2, 0, a(this.b.p), 3, 0);
                return;
            }
        }
        kk.b(kk.e, lVar, this.b.h().b, i, i2, 0, -1, -16777216);
    }

    private void a(Vector vector, cn cnVar, et etVar) {
        if (this.b.h().i <= d.a().i() && (this.b.h().e == 2 || this.b.h().e == d.a().G)) {
            if (etVar.t() == 0) {
                if (!this.b.n() || d.a().F >= this.b.h().j) {
                    if (this.b.W()) {
                        if (this.b.h().f == 99 && ((this.b.h == null || this.b.h.length() == 0) && this.b.c != 791)) {
                            vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.fR, cnVar, Input.Keys.ESCAPE, 15));
                        } else if (!this.b.i() || !(cnVar instanceof he)) {
                            vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.p, cnVar, Input.Keys.BUTTON_L2, 15));
                            if (this.b.h().d && this.b.h().f != 24 && this.b.O() > 1) {
                                if (this.b.c == 435) {
                                    vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.qn, cnVar, 169, 15));
                                } else if (this.b.c != 763 && this.b.c != 864 && this.b.c != 868) {
                                    vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.mc, cnVar, Input.Keys.NUMPAD_5, 15));
                                }
                            }
                        }
                    }
                    if (this.b.i() && !(cnVar instanceof he)) {
                        vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.fS, cnVar, 127, 15));
                    }
                }
            } else if (etVar.t() == 1) {
                vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.aG, cnVar, Input.Keys.BUTTON_START, 15));
            }
        }
        if (!this.b.q) {
            vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.q, cnVar, Input.Keys.BUTTON_SELECT, 15));
        }
        if (this.b.O() >= 2) {
            vector.add(new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.r, cnVar, 102, 15));
        }
        if (this.b.W() && this.b.c != com.donglh.narutoninjasaga.e.n.n().bm) {
            dg dgVar = new dg(this.aM - 51, this.aN - 26, com.donglh.narutoninjasaga.c.a.bI, cnVar, 137, 15);
            this.k = dgVar;
            vector.add(dgVar);
        }
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void b() {
        if (this.i) {
            for (int i = 1; i < this.a.length; i++) {
                this.a[i].b(true);
            }
        }
        a(this.a, this.e);
        this.f.a();
        if ((this.b.p < 8 || this.b.p > 11 || com.donglh.narutoninjasaga.e.f.c().i % 5 != 0) && (this.b.p < 12 || this.b.p > 16 || com.donglh.narutoninjasaga.e.f.c().i % 4 != 0)) {
            if (this.b.p >= 17 && com.donglh.narutoninjasaga.e.f.c().i % 3 == 0) {
                this.j++;
                if (this.j > this.b.p - 8) {
                    this.j = 0;
                }
            }
        } else {
            this.j++;
            if (this.j > this.b.p / 2) {
                this.j = 0;
            }
        }
        int i2 = com.donglh.narutoninjasaga.e.n.n().aC;
        com.donglh.narutoninjasaga.e.f.c();
        if (i2 <= 12 && this.l.n.equals(this)) {
            com.donglh.narutoninjasaga.e.au J = com.donglh.narutoninjasaga.e.n.n().J();
            int i3 = this.l.aY + this.aY + this.aM;
            int i4 = this.l.aZ + this.aZ + this.aN;
            switch (com.donglh.narutoninjasaga.e.n.n().aC) {
                case 0:
                    if (com.donglh.narutoninjasaga.e.n.n().aD == 0 && this.b.c == 28) {
                        com.donglh.narutoninjasaga.e.f.c().aE.a(i3 - 35, i4 - 12);
                        com.donglh.narutoninjasaga.e.f.c().aE.a = true;
                        return;
                    }
                    return;
                case 1:
                    if (com.donglh.narutoninjasaga.e.n.n().aD == 1 && this.b.c == 378) {
                        com.donglh.narutoninjasaga.e.f.c().aE.a(i3 - 70, i4 - 12);
                        com.donglh.narutoninjasaga.e.f.c().aE.a = true;
                        return;
                    }
                    return;
                case 2:
                    if (com.donglh.narutoninjasaga.e.n.n().aD == 1 && this.b.c == 194) {
                        com.donglh.narutoninjasaga.e.f.c().aE.a(i3 - 70, i4 - 12);
                        com.donglh.narutoninjasaga.e.f.c().aE.a = true;
                        return;
                    }
                    return;
                case 3:
                    if (com.donglh.narutoninjasaga.e.n.n().aD == 1) {
                        com.donglh.narutoninjasaga.e.f.c().aE.a(i3 - 35, i4 - 12);
                        com.donglh.narutoninjasaga.e.f.c().aE.a = true;
                        return;
                    } else if (com.donglh.narutoninjasaga.e.n.n().aD == 4 && this.b.c == 379) {
                        com.donglh.narutoninjasaga.e.f.c().aE.a(i3 - 70, i4 - 12);
                        com.donglh.narutoninjasaga.e.f.c().aE.a = true;
                        return;
                    } else {
                        return;
                    }
                case 4:
                case 5:
                case 7:
                default:
                    return;
                case 6:
                    if (com.donglh.narutoninjasaga.e.n.n().aD >= 0 && this.b.c == 380 && J != null && d.a().aY == J.g && d.a().aZ == J.h) {
                        com.donglh.narutoninjasaga.e.f.c().aE.a(i3 - 35, i4 - 12);
                        com.donglh.narutoninjasaga.e.f.c().aE.a = true;
                        return;
                    }
                    return;
                case 8:
                    q m = d.a().m();
                    if (com.donglh.narutoninjasaga.e.n.n().aD == 9 && m != null && this.b.c == m.c) {
                        com.donglh.narutoninjasaga.e.f.c().aE.a(i3 - 35, i4 - 12);
                        com.donglh.narutoninjasaga.e.f.c().aE.a = true;
                        return;
                    }
                    return;
                case 9:
                    if (com.donglh.narutoninjasaga.e.n.n().aD == 10 && (this.l instanceof am)) {
                        if (((am) this.l).i.b != ((am) this.l).q()) {
                            com.donglh.narutoninjasaga.e.f.c().aE.a(i3 - 35, i4 - 12);
                        } else if (this.b.h().f == 1 || this.b.h().f == 21) {
                            if (this.b.q) {
                                com.donglh.narutoninjasaga.e.f.c().aE.a(i3 - 35, i4 - 12);
                            } else {
                                com.donglh.narutoninjasaga.e.f.c().aE.a(i3 - 70, i4 - 12);
                            }
                        }
                        com.donglh.narutoninjasaga.e.f.c().aE.a = true;
                        return;
                    }
                    return;
                case 10:
                    if (com.donglh.narutoninjasaga.e.n.n().aD == 5 && (this.l instanceof bu)) {
                        com.donglh.narutoninjasaga.e.f.c().aE.a(i3 - 70, i4 - 12);
                        com.donglh.narutoninjasaga.e.f.c().aE.a = true;
                        return;
                    }
                    return;
                case 11:
                    if ((this.l instanceof hg) && this.b.c == 383 && J != null && d.a().aY == J.g && d.a().aZ == J.h) {
                        com.donglh.narutoninjasaga.e.f.c().aE.a(i3 - 35, i4 - 12);
                        com.donglh.narutoninjasaga.e.f.c().aE.a = true;
                        return;
                    }
                    return;
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        Vector vector = new Vector();
        if (this.i) {
            vector.addElement(this.e[0].a(this.a[0].b, this));
        } else {
            for (int i = 0; i < this.e.length; i++) {
                vector.addElement(this.e[i].a(this.a[i].b, this));
            }
        }
        vector.addElement(new gu(1010, this.f.aY, this.f.aZ, this.f.aY + this.f.aM, this.f.aZ + this.f.aN, this.f, this));
        vector.addElement(new gu(1011, 0, 0, this.aM, this.aN, this.f, this));
        return vector;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v11, types: [com.donglh.narutoninjasaga.e.ak] */
    /* JADX WARN: Type inference failed for: r0v119, types: [com.donglh.narutoninjasaga.d.iu] */
    /* JADX WARN: Type inference failed for: r0v120, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v131 */
    /* JADX WARN: Type inference failed for: r0v153, types: [com.donglh.narutoninjasaga.d.iu] */
    /* JADX WARN: Type inference failed for: r0v154, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v165, types: [com.donglh.narutoninjasaga.d.bq] */
    /* JADX WARN: Type inference failed for: r0v192, types: [com.donglh.narutoninjasaga.d.iu] */
    /* JADX WARN: Type inference failed for: r0v193, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v203, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r0v220, types: [com.donglh.narutoninjasaga.d.iu] */
    /* JADX WARN: Type inference failed for: r0v221, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v223, types: [com.donglh.narutoninjasaga.e.ak] */
    /* JADX WARN: Type inference failed for: r0v229, types: [com.donglh.narutoninjasaga.d.iu] */
    /* JADX WARN: Type inference failed for: r0v230, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v232, types: [com.donglh.narutoninjasaga.e.ak] */
    /* JADX WARN: Type inference failed for: r0v255, types: [com.donglh.narutoninjasaga.d.iu] */
    /* JADX WARN: Type inference failed for: r0v256, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v263, types: [com.donglh.narutoninjasaga.d.q[]] */
    /* JADX WARN: Type inference failed for: r0v279, types: [com.donglh.narutoninjasaga.d.iu] */
    /* JADX WARN: Type inference failed for: r0v280, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v295, types: [com.donglh.narutoninjasaga.d.jk] */
    /* JADX WARN: Type inference failed for: r0v401, types: [com.donglh.narutoninjasaga.d.iu] */
    /* JADX WARN: Type inference failed for: r0v402, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v412 */
    /* JADX WARN: Type inference failed for: r0v469, types: [com.donglh.narutoninjasaga.d.iu] */
    /* JADX WARN: Type inference failed for: r0v470, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v472, types: [com.donglh.narutoninjasaga.e.ak] */
    /* JADX WARN: Type inference failed for: r0v477, types: [com.donglh.narutoninjasaga.d.iu] */
    /* JADX WARN: Type inference failed for: r0v478, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v480, types: [com.donglh.narutoninjasaga.e.ak] */
    /* JADX WARN: Type inference failed for: r0v482, types: [com.donglh.narutoninjasaga.d.iu] */
    /* JADX WARN: Type inference failed for: r0v483, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v485, types: [com.donglh.narutoninjasaga.e.ak] */
    /* JADX WARN: Type inference failed for: r0v487, types: [com.donglh.narutoninjasaga.d.iu] */
    /* JADX WARN: Type inference failed for: r0v488, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v490, types: [com.donglh.narutoninjasaga.e.ak] */
    /* JADX WARN: Type inference failed for: r0v8, types: [com.donglh.narutoninjasaga.d.iu] */
    /* JADX WARN: Type inference failed for: r0v86, types: [com.donglh.narutoninjasaga.d.iu] */
    /* JADX WARN: Type inference failed for: r0v87, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v9, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v97 */
    /* JADX WARN: Type inference failed for: r10v0, types: [com.donglh.narutoninjasaga.d.iu, com.donglh.narutoninjasaga.d.co, java.lang.Exception] */
    @Override // com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        switch (guVar.b) {
            case -119:
                j();
                try {
                    v vVar = (v) this.l;
                    q[] b = vVar.b(this.b);
                    if (this.b.h().f == 98) {
                        if (vVar.a[2] != null) {
                            b[vVar.a[2].e] = vVar.a[2];
                        }
                        vVar.a[2] = this.b;
                        b[this.b.e] = null;
                        return;
                    } else if (this.b.h().f == 19) {
                        if (vVar.a[0] != null) {
                            b[vVar.a[0].e] = vVar.a[0];
                        }
                        vVar.a[0] = this.b;
                        b[this.b.e] = null;
                        return;
                    } else {
                        return;
                    }
                } catch (Exception e) {
                    com.donglh.narutoninjasaga.e.aw.a((Exception) this);
                    return;
                }
            case -118:
                j();
                w();
                return;
            case -117:
                j();
                v();
                return;
            case -116:
                j();
                try {
                    u uVar = (u) this.l;
                    q[] b2 = uVar.b(this.b);
                    if (this.b.h().a == 926) {
                        if (uVar.a[2] != null) {
                            b2[uVar.a[2].e] = uVar.a[2];
                        }
                        uVar.a[2] = this.b;
                        b2[this.b.e] = null;
                        return;
                    } else if (this.b.h().f == 17) {
                        if (uVar.a[0] != null) {
                            b2[uVar.a[0].e] = uVar.a[0];
                        }
                        uVar.a[0] = this.b;
                        b2[this.b.e] = null;
                        return;
                    } else {
                        return;
                    }
                } catch (Exception e2) {
                    com.donglh.narutoninjasaga.e.aw.a((Exception) this);
                    return;
                }
            case 100:
                j();
                return;
            case 101:
                j();
                if (this.l instanceof bn) {
                    et etVar = (et) this.l;
                    etVar.s.a(new ih(etVar.s, this.b, ((bn) this.l).a));
                    return;
                } else if (this.l instanceof bs) {
                    bs bsVar = (bs) this.l;
                    if (bsVar.a.b.b == 3) {
                        bsVar.s.a(new ih(bsVar.s, this.b, (byte) 40));
                        return;
                    } else {
                        bsVar.s.a(new ih(bsVar.s, this.b, (byte) 6));
                        return;
                    }
                } else {
                    return;
                }
            case 102:
                j();
                et etVar2 = (et) this.l;
                etVar2.s.a(new bo(etVar2.s, this.b));
                return;
            case Input.Keys.BUTTON_R1 /* 103 */:
                j();
                if (this.b.p > 0 && (this.b.k() || this.b.m() || this.b.l())) {
                    com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.aC, -65536);
                    return;
                } else {
                    com.donglh.narutoninjasaga.e.f.c().am.a(com.donglh.narutoninjasaga.c.a.aD, 2000, (co) this);
                    return;
                }
            case Input.Keys.BUTTON_L2 /* 104 */:
                j();
                if (this.b.c == 791) {
                    com.donglh.narutoninjasaga.e.f.c().am.a(com.donglh.narutoninjasaga.c.a.qv, 2001, (co) this);
                    return;
                } else if (this.b.i() && !this.b.q) {
                    com.donglh.narutoninjasaga.e.f.c().am.a(com.donglh.narutoninjasaga.c.a.aF, 2001, (co) this);
                    return;
                } else {
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.a(this.b);
                    return;
                }
            case Input.Keys.BUTTON_R2 /* 105 */:
                j();
                ?? r0 = this;
                try {
                    com.donglh.narutoninjasaga.e.ak akVar = new com.donglh.narutoninjasaga.e.ak((byte) 113);
                    akVar.a((int) r0.b.h().f);
                    r0 = akVar;
                    r0.l();
                    return;
                } catch (Exception e3) {
                    com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                    return;
                }
            case Input.Keys.BUTTON_THUMBL /* 106 */:
                j();
                ?? r02 = this;
                try {
                    com.donglh.narutoninjasaga.e.ak akVar2 = new com.donglh.narutoninjasaga.e.ak((byte) 115);
                    akVar2.b(r02.b.e);
                    r02 = akVar2;
                    r02.l();
                    return;
                } catch (Exception e4) {
                    com.donglh.narutoninjasaga.e.aw.a((Exception) r02);
                    return;
                }
            case Input.Keys.BUTTON_THUMBR /* 107 */:
                j();
                ?? r03 = this;
                try {
                    com.donglh.narutoninjasaga.e.ak akVar3 = new com.donglh.narutoninjasaga.e.ak((byte) 114);
                    akVar3.b(r03.b.e);
                    r03 = akVar3;
                    r03.l();
                    return;
                } catch (Exception e5) {
                    com.donglh.narutoninjasaga.e.aw.a((Exception) r03);
                    return;
                }
            case Input.Keys.BUTTON_START /* 108 */:
                j();
                if (this.l instanceof eb) {
                    ((eb) this.l).p();
                }
                ?? r04 = this;
                try {
                    com.donglh.narutoninjasaga.e.ak akVar4 = new com.donglh.narutoninjasaga.e.ak((byte) 112);
                    akVar4.a(r04.b.e);
                    r04 = akVar4;
                    r04.l();
                    return;
                } catch (Exception e6) {
                    com.donglh.narutoninjasaga.e.aw.a((Exception) r04);
                    return;
                }
            case Input.Keys.BUTTON_SELECT /* 109 */:
                j();
                q qVar = this.b;
                if ((qVar.c >= 5 && qVar.c <= 11) || qVar.c == 231 || qVar.c == 176 || qVar.c == 160 || qVar.c == 161 || qVar.c == 169 || qVar.c == 170 || qVar.c == 156 || qVar.c == 157 || qVar.c == 158 || qVar.c == 159 || qVar.c == 162 || qVar.c == 166 || qVar.c == 168 || qVar.c == 182 || qVar.c == 183 || qVar.c == 184 || qVar.c == 177) {
                    com.donglh.narutoninjasaga.e.f.c().am.a(com.donglh.narutoninjasaga.c.a.fT + this.b.h().b + com.donglh.narutoninjasaga.c.a.dt, 2005, (co) this);
                    return;
                } else {
                    i();
                    return;
                }
            case Input.Keys.BUTTON_MODE /* 110 */:
                j();
                M();
                return;
            case 111:
                j();
                N();
                return;
            case Input.Keys.FORWARD_DEL /* 112 */:
                j();
                q();
                return;
            case 113:
                j();
                r();
                return;
            case 114:
                j();
                ?? r05 = this;
                try {
                    br brVar = (br) r05.l;
                    q[] b3 = brVar.b(r05.b);
                    if (brVar.a[0] != null) {
                        b3[brVar.a[0].e] = brVar.a[0];
                    }
                    brVar.a[0] = r05.b;
                    r05 = b3;
                    r05[r05.b.e] = 0;
                    return;
                } catch (Exception e7) {
                    com.donglh.narutoninjasaga.e.aw.a((Exception) r05);
                    return;
                }
            case 115:
                j();
                s();
                return;
            case 116:
                j();
                try {
                    t tVar = (t) this.l;
                    q[] b4 = tVar.b(this.b);
                    if (this.b.h().f == 25) {
                        if (tVar.a[2] != null) {
                            b4[tVar.a[2].e] = tVar.a[2];
                        }
                        tVar.a[2] = this.b;
                        b4[this.b.e] = null;
                        return;
                    } else if (this.b.p > 0) {
                        if (tVar.a[0] != null) {
                            b4[tVar.a[0].e] = tVar.a[0];
                        }
                        tVar.a[0] = this.b;
                        b4[this.b.e] = null;
                        return;
                    } else {
                        if (tVar.a[1] != null) {
                            b4[tVar.a[1].e] = tVar.a[1];
                        }
                        tVar.a[1] = this.b;
                        b4[this.b.e] = null;
                        return;
                    }
                } catch (Exception e8) {
                    com.donglh.narutoninjasaga.e.aw.a((Exception) this);
                    return;
                }
            case 117:
                j();
                u();
                return;
            case 118:
                j();
                A();
                return;
            case 119:
                j();
                B();
                return;
            case 120:
                j();
                ?? r06 = this;
                try {
                    jk jkVar = (jk) r06.l;
                    if (jkVar.d != null) {
                        d.a().W[jkVar.d.e] = jkVar.d;
                    }
                    jkVar.d = r06.b;
                    d.a().W[r06.b.e] = null;
                    jkVar.a(0);
                    jkVar.c.a(1);
                    jkVar.y();
                    jkVar.a(jkVar.q());
                    jkVar.c.a(1);
                    r06 = jkVar;
                    r06.a(0);
                    return;
                } catch (Exception e9) {
                    com.donglh.narutoninjasaga.e.aw.a((Exception) r06);
                    return;
                }
            case 121:
                j();
                jk jkVar2 = (jk) this.l;
                d.a().W[jkVar2.d.e] = jkVar2.d;
                jkVar2.d = null;
                return;
            case 122:
                j();
                jk jkVar3 = (jk) this.l;
                jkVar3.a(true, jkVar3.a[jkVar3.b]);
                return;
            case 123:
                j();
                ?? r07 = this;
                try {
                    ji jiVar = (ji) r07.l;
                    if (jiVar.e != null) {
                        d.a().W[jiVar.e.e] = jiVar.e;
                    }
                    jiVar.e = r07.b;
                    r07 = d.a().W;
                    r07[r07.b.e] = 0;
                    return;
                } catch (Exception e10) {
                    com.donglh.narutoninjasaga.e.aw.a((Exception) r07);
                    return;
                }
            case 124:
                j();
                ji jiVar2 = (ji) this.l;
                d.a().W[jiVar2.e.e] = jiVar2.e;
                jiVar2.e = null;
                return;
            case 125:
                j();
                O();
                return;
            case 126:
                j();
                P();
                return;
            case 127:
                j();
                if (this.b.i() && !this.b.q) {
                    com.donglh.narutoninjasaga.e.f.c().am.a(com.donglh.narutoninjasaga.c.a.by, 2002, (co) this);
                    return;
                } else {
                    Q();
                    return;
                }
            case 128:
                j();
                ?? r08 = this;
                try {
                    com.donglh.narutoninjasaga.e.ak akVar5 = new com.donglh.narutoninjasaga.e.ak((byte) 37);
                    akVar5.a((int) r08.b.h().f);
                    r08 = akVar5;
                    r08.l();
                    return;
                } catch (Exception e11) {
                    com.donglh.narutoninjasaga.e.aw.a((Exception) r08);
                    return;
                }
            case Input.Keys.CONTROL_LEFT /* 129 */:
                j();
                T();
                return;
            case Input.Keys.CONTROL_RIGHT /* 130 */:
                j();
                Y();
                return;
            case Input.Keys.ESCAPE /* 131 */:
                j();
                ?? r09 = this;
                try {
                    com.donglh.narutoninjasaga.e.ak akVar6 = new com.donglh.narutoninjasaga.e.ak((byte) -3);
                    akVar6.b(r09.b.e);
                    r09 = akVar6;
                    r09.l();
                    return;
                } catch (Exception e12) {
                    com.donglh.narutoninjasaga.e.aw.a((Exception) r09);
                    return;
                }
            case Input.Keys.END /* 132 */:
                j();
                jk jkVar4 = (jk) this.l;
                if (d.a().u) {
                    jkVar4.a(false);
                    return;
                } else {
                    jkVar4.a(true);
                    return;
                }
            case 134:
                j();
                com.donglh.narutoninjasaga.e.f.c().am.a(com.donglh.narutoninjasaga.c.a.fV + com.donglh.narutoninjasaga.e.aw.c(this.g.c) + com.donglh.narutoninjasaga.c.a.dt, com.donglh.narutoninjasaga.c.a.bq, 2003, 2004, (co) this);
                return;
            case 135:
                j();
                U();
                return;
            case 136:
                j();
                Z();
                return;
            case 137:
                j();
                ?? r010 = this;
                try {
                    com.donglh.narutoninjasaga.e.n n = com.donglh.narutoninjasaga.e.n.n();
                    int i3 = r010.b.c;
                    n.bm = i3;
                    n.ab.b(false);
                    n.b(com.donglh.narutoninjasaga.e.f.c().U[i3].b + com.donglh.narutoninjasaga.c.a.gI, -1);
                    r010 = "shortcutItem";
                    com.donglh.narutoninjasaga.e.c.b("shortcutItem", i3);
                    return;
                } catch (Exception e13) {
                    com.donglh.narutoninjasaga.e.aw.a((Exception) r010);
                    return;
                }
            case 138:
                j();
                y();
                return;
            case 139:
                j();
                z();
                return;
            case 140:
                j();
                try {
                    hq hqVar = (hq) this.l;
                    com.donglh.narutoninjasaga.e.ak b5 = com.donglh.narutoninjasaga.e.ak.b((byte) -90);
                    b5.b(hqVar.x.indexOf(this.b));
                    b5.l();
                    return;
                } catch (Exception unused) {
                    return;
                }
            case 141:
                j();
                W();
                return;
            case 142:
                j();
                X();
                return;
            case 143:
                j();
                E();
                return;
            case Input.Keys.NUMPAD_0 /* 144 */:
                j();
                F();
                return;
            case Input.Keys.NUMPAD_1 /* 145 */:
                j();
                aa();
                return;
            case Input.Keys.NUMPAD_2 /* 146 */:
                j();
                ab();
                return;
            case Input.Keys.NUMPAD_3 /* 147 */:
                j();
                ?? r011 = this;
                try {
                    bq bqVar = (bq) r011.l;
                    q[] b6 = bqVar.b(r011.b);
                    if (bqVar.a[0] != null) {
                        b6[bqVar.a[0].e] = bqVar.a[0];
                    }
                    bqVar.a[0] = r011.b;
                    b6[r011.b.e] = null;
                    r011 = bqVar;
                    r011.e();
                    return;
                } catch (Exception e14) {
                    com.donglh.narutoninjasaga.e.aw.a((Exception) r011);
                    return;
                }
            case Input.Keys.NUMPAD_4 /* 148 */:
                j();
                t();
                return;
            case Input.Keys.NUMPAD_5 /* 149 */:
                j();
                et etVar3 = (et) this.l;
                etVar3.s.a(new aq(etVar3.s, this.b));
                return;
            case Input.Keys.NUMPAD_6 /* 150 */:
                j();
                n();
                return;
            case Input.Keys.NUMPAD_7 /* 151 */:
                j();
                o();
                return;
            case Input.Keys.NUMPAD_8 /* 152 */:
                j();
                ((ah) this.l).a(this.b);
                return;
            case Input.Keys.NUMPAD_9 /* 153 */:
                j();
                ?? r012 = this;
                try {
                    bp bpVar = (bp) r012.l;
                    q[] b7 = bpVar.b(r012.b);
                    if (bpVar.a[0] != null) {
                        d.a().g(bpVar.a[0].r)[bpVar.a[0].e] = bpVar.a[0];
                    }
                    bpVar.a[0] = r012.b;
                    try {
                        com.donglh.narutoninjasaga.e.ak akVar7 = new com.donglh.narutoninjasaga.e.ak((byte) -52);
                        akVar7.a(bpVar.a[0].r);
                        akVar7.b(bpVar.a[0].e);
                        akVar7.l();
                    } catch (Exception unused2) {
                    }
                    r012 = b7;
                    r012[r012.b.e] = 0;
                    return;
                } catch (Exception e15) {
                    com.donglh.narutoninjasaga.e.aw.a((Exception) r012);
                    return;
                }
            case 154:
                j();
                p();
                return;
            case 155:
                j();
                ac();
                return;
            case 156:
                j();
                ad();
                return;
            case 157:
                j();
                G();
                return;
            case 158:
                j();
                H();
                return;
            case 159:
                j();
                I();
                return;
            case 160:
                j();
                J();
                return;
            case 161:
                j();
                K();
                return;
            case 162:
                j();
                L();
                return;
            case 163:
                j();
                ?? r013 = this;
                try {
                    dh dhVar = (dh) r013.l;
                    q[] b8 = dhVar.b(r013.b);
                    if (dhVar.a[0] != null) {
                        d.a().g(dhVar.a[0].r)[dhVar.a[0].e] = dhVar.a[0];
                    }
                    dhVar.a[0] = r013.b;
                    r013 = b8;
                    r013[r013.b.e] = 0;
                    return;
                } catch (Exception e16) {
                    com.donglh.narutoninjasaga.e.aw.a((Exception) r013);
                    return;
                }
            case 164:
                j();
                V();
                return;
            case 165:
                j();
                try {
                    ab abVar = (ab) this.l;
                    q[] b9 = abVar.b(this.b);
                    if (this.b.c == 658) {
                        if (abVar.b[2] != null) {
                            b9[abVar.b[2].e] = abVar.b[2];
                        }
                        abVar.b[2] = this.b;
                        b9[this.b.e] = null;
                        return;
                    }
                    if (abVar.b[0] != null) {
                        b9[abVar.b[0].e] = abVar.b[0];
                    }
                    abVar.b[0] = this.b;
                    b9[this.b.e] = null;
                    abVar.e();
                    return;
                } catch (Exception e17) {
                    com.donglh.narutoninjasaga.e.aw.a((Exception) this);
                    return;
                }
            case 166:
                j();
                x();
                return;
            case 167:
                j();
                try {
                    com.donglh.narutoninjasaga.e.ak.b((byte) -20).l();
                    return;
                } catch (Exception unused3) {
                    return;
                }
            case 168:
                j();
                com.donglh.narutoninjasaga.e.f.c().am.a(com.donglh.narutoninjasaga.e.aw.b(com.donglh.narutoninjasaga.c.a.qm, com.donglh.narutoninjasaga.e.aw.c(100 + ((d.a().au - 10) * 50))), 2007, 2008, (co) this);
                return;
            case 169:
                j();
                Vector vector = new Vector();
                vector.addElement(new hz(30000, com.donglh.narutoninjasaga.c.a.qo));
                vector.addElement(new hz(30001, com.donglh.narutoninjasaga.c.a.qp));
                if (vector.size() > 0) {
                    String[] strArr = new String[vector.size()];
                    short[] sArr = new short[vector.size()];
                    for (int i4 = 0; i4 < vector.size(); i4++) {
                        hz hzVar = (hz) vector.elementAt(i4);
                        strArr[i4] = hzVar.b;
                        sArr[i4] = (short) hzVar.a;
                    }
                    this.l.n = cn.a(this.l, this.aY, this.aZ, new gz(this.b.e, sArr, strArr));
                    return;
                }
                return;
            case 170:
                j();
                com.donglh.narutoninjasaga.e.n.n();
                com.donglh.narutoninjasaga.e.n.m(this.b.h().f);
                return;
            case 171:
                j();
                C();
                return;
            case 172:
                j();
                D();
                return;
            case 2000:
                com.donglh.narutoninjasaga.e.n.n();
                com.donglh.narutoninjasaga.e.n.a(this.b, false);
                return;
            case 2001:
                com.donglh.narutoninjasaga.e.n.n();
                com.donglh.narutoninjasaga.e.n.a(this.b);
                return;
            case 2002:
                Q();
                return;
            case 2003:
                ?? r014 = this;
                try {
                    com.donglh.narutoninjasaga.e.ak akVar8 = new com.donglh.narutoninjasaga.e.ak((byte) 98);
                    akVar8.a(r014.g.a);
                    r014 = akVar8;
                    r014.l();
                    return;
                } catch (Exception e18) {
                    com.donglh.narutoninjasaga.e.aw.a((Exception) r014);
                    return;
                }
            case 2005:
                i();
                return;
            case 2006:
                h();
                return;
            case 2007:
                j();
                try {
                    com.donglh.narutoninjasaga.e.ak.b((byte) -21).l();
                    return;
                } catch (Exception unused4) {
                    return;
                }
            case 2008:
                j();
                return;
            default:
                return;
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v0, types: [com.donglh.narutoninjasaga.d.kc] */
    /* JADX WARN: Type inference failed for: r0v1, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v3, types: [com.donglh.narutoninjasaga.e.ak] */
    private void h() {
        ?? G = kc.G();
        G.b(false);
        try {
            com.donglh.narutoninjasaga.e.ak akVar = new com.donglh.narutoninjasaga.e.ak((byte) 111);
            akVar.b(this.b.e);
            G = akVar;
            G.l();
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) G);
        }
    }

    private static int a(int i) {
        if (i >= 19) {
            return -13333639;
        }
        if (i >= 18) {
            return -7995113;
        }
        if (i >= 17) {
            return -720386;
        }
        if (i >= 16) {
            return -2560;
        }
        if (i >= 14) {
            return -10618832;
        }
        if (i >= 12) {
            return -16551185;
        }
        if (i >= 10) {
            return -65024;
        }
        if (i >= 8) {
            return -391263;
        }
        if (i >= 4) {
            return -1602278;
        }
        return i > 0 ? -16609665 : -1;
    }

    /* JADX WARN: Multi-variable type inference failed */
    private static String b(int i, int i2) {
        try {
            switch (i) {
                case 0:
                    return com.donglh.narutoninjasaga.c.a.ri[3] + " (" + com.donglh.narutoninjasaga.c.a.rg[i2 - 1] + "), " + com.donglh.narutoninjasaga.c.a.ri[4] + " (" + com.donglh.narutoninjasaga.c.a.rg[i2 - 1] + ")";
                case 1:
                    return com.donglh.narutoninjasaga.c.a.ai + " (" + com.donglh.narutoninjasaga.c.a.rg[i2 - 1] + ") " + com.donglh.narutoninjasaga.c.a.ao + " (" + com.donglh.narutoninjasaga.c.a.rg[i2 - 1] + ")";
                case 2:
                    return com.donglh.narutoninjasaga.c.a.ri[7] + " (" + com.donglh.narutoninjasaga.c.a.rg[i2 - 1] + "), " + com.donglh.narutoninjasaga.c.a.ri[8] + " (" + com.donglh.narutoninjasaga.c.a.rg[i2 - 1] + ")";
                case 3:
                    return com.donglh.narutoninjasaga.c.a.ri[0] + " (" + com.donglh.narutoninjasaga.c.a.rg[i2 - 1] + "), " + com.donglh.narutoninjasaga.c.a.ri[4] + " (" + com.donglh.narutoninjasaga.c.a.rg[i2 - 1] + ")";
                case 4:
                    return com.donglh.narutoninjasaga.c.a.ri[0] + " (" + com.donglh.narutoninjasaga.c.a.rg[i2 - 1] + "), " + com.donglh.narutoninjasaga.c.a.ri[3] + " (" + com.donglh.narutoninjasaga.c.a.rg[i2 - 1] + ")";
                case 5:
                    return com.donglh.narutoninjasaga.c.a.ri[6] + " (" + com.donglh.narutoninjasaga.c.a.rg[i2 - 1] + "), " + com.donglh.narutoninjasaga.c.a.ri[9] + " (" + com.donglh.narutoninjasaga.c.a.rg[i2 - 1] + ")";
                case 6:
                    return com.donglh.narutoninjasaga.c.a.ri[5] + " (" + com.donglh.narutoninjasaga.c.a.rg[i2 - 1] + "), " + com.donglh.narutoninjasaga.c.a.ri[9] + " (" + com.donglh.narutoninjasaga.c.a.rg[i2 - 1] + ")";
                case 7:
                    return com.donglh.narutoninjasaga.c.a.ri[2] + " (" + com.donglh.narutoninjasaga.c.a.rg[i2 - 1] + "), " + com.donglh.narutoninjasaga.c.a.ri[8] + " (" + com.donglh.narutoninjasaga.c.a.rg[i2 - 1] + ")";
                case 8:
                    return com.donglh.narutoninjasaga.c.a.ri[2] + " (" + com.donglh.narutoninjasaga.c.a.rg[i2 - 1] + "), " + com.donglh.narutoninjasaga.c.a.ri[7] + " (" + com.donglh.narutoninjasaga.c.a.rg[i2 - 1] + ")";
                case 9:
                    return com.donglh.narutoninjasaga.c.a.ri[6] + " (" + com.donglh.narutoninjasaga.c.a.rg[i2 - 1] + "), " + com.donglh.narutoninjasaga.c.a.ri[5] + " (" + com.donglh.narutoninjasaga.c.a.rg[i2 - 1] + ")";
                default:
                    return "";
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) i);
            return "";
        }
    }

    private void i() {
        if (!kc.G().y()) {
            h();
        } else {
            com.donglh.narutoninjasaga.e.f.c().am.a(com.donglh.narutoninjasaga.c.a.lu, 2006, 2004, this);
        }
    }

    private void j() {
        this.l.b(this);
        if (this.c != null) {
            this.l.b(this.c);
        }
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x00fd: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:34:0x00fc */
    private void n() {
        Exception a;
        try {
            ah ahVar = (ah) this.l;
            q[] b = ahVar.b(this.b);
            int i = 0;
            for (int i2 = 0; i2 < ahVar.b.length; i2++) {
                if (ahVar.b[i2] != null) {
                    i++;
                    if (ahVar.b[i2].c == this.b.c) {
                        com.donglh.narutoninjasaga.e.f.c().am.b(this.b.h().b + " " + com.donglh.narutoninjasaga.c.a.nK, -65536);
                        return;
                    }
                }
            }
            boolean z = false;
            int i3 = 0;
            while (true) {
                if (i3 < ahVar.b.length) {
                    if (ahVar.b[i3] != null) {
                        i3++;
                    } else {
                        z = true;
                        break;
                    }
                } else {
                    break;
                }
            }
            if (z && i < com.donglh.narutoninjasaga.e.n.n().ca) {
                int i4 = 0;
                while (true) {
                    if (i4 < ahVar.b.length) {
                        if (ahVar.b[i4] != null) {
                            i4++;
                        } else {
                            ahVar.b[i4] = this.b;
                            break;
                        }
                    } else {
                        break;
                    }
                }
                ahVar.a(ahVar.a[0]);
                b[this.b.e] = null;
                return;
            }
            com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.oB, -65536);
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a);
        }
    }

    private void o() {
        ah ahVar = (ah) this.l;
        q[] qVarArr = ahVar.b;
        int i = 0;
        while (true) {
            if (i < qVarArr.length) {
                if (qVarArr[i] == null || !qVarArr[i].equals(this.b)) {
                    i++;
                } else {
                    d.a().g(this.b.r)[this.b.e] = this.b;
                    qVarArr[i] = null;
                    break;
                }
            } else {
                break;
            }
        }
        ahVar.a(ahVar.e);
    }

    private void p() {
        q[] qVarArr = ((bp) this.l).a;
        for (int i = 0; i < qVarArr.length; i++) {
            if (qVarArr[i] != null && qVarArr[i].equals(this.b)) {
                d.a().g(this.b.r)[this.b.e] = this.b;
                qVarArr[i] = null;
                return;
            }
        }
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x00d3: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:28:0x00d2 */
    private void q() {
        Exception a;
        try {
            am amVar = (am) this.l;
            q[] b = amVar.b(this.b);
            boolean z = false;
            if (this.b.c == 162) {
                if (amVar.a[2] != null) {
                    b[amVar.a[2].e] = amVar.a[2];
                }
                amVar.a[2] = this.b;
            } else if (this.b.i()) {
                if (amVar.a[0] != null) {
                    d.a().g(amVar.a[0].r)[amVar.a[0].e] = amVar.a[0];
                }
                amVar.a[0] = this.b;
            } else {
                int i = 0;
                while (true) {
                    if (i >= amVar.b.length) {
                        break;
                    } else if (amVar.b[i] != null) {
                        i++;
                    } else {
                        amVar.b[i] = this.b;
                        z = true;
                        break;
                    }
                }
                if (!z) {
                    com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.bc, -65536);
                    return;
                }
            }
            b[this.b.e] = null;
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a);
        }
    }

    private void r() {
        am amVar = (am) this.l;
        q[] qVarArr = amVar.a;
        int i = 0;
        while (true) {
            if (i >= qVarArr.length) {
                break;
            } else if (qVarArr[i] == null || !qVarArr[i].equals(this.b)) {
                i++;
            } else {
                d.a().g(this.b.r)[this.b.e] = this.b;
                qVarArr[i] = null;
                break;
            }
        }
        q[] qVarArr2 = amVar.b;
        for (int i2 = 0; i2 < qVarArr2.length; i2++) {
            if (qVarArr2[i2] != null && qVarArr2[i2].equals(this.b)) {
                d.a().W[this.b.e] = this.b;
                qVarArr2[i2] = null;
                return;
            }
        }
    }

    private void s() {
        q[] qVarArr = ((br) this.l).a;
        for (int i = 0; i < qVarArr.length; i++) {
            if (qVarArr[i] != null && qVarArr[i].equals(this.b)) {
                d.a().g(this.b.r)[this.b.e] = this.b;
                qVarArr[i] = null;
                return;
            }
        }
    }

    private void t() {
        q[] qVarArr = ((bq) this.l).a;
        for (int i = 0; i < qVarArr.length; i++) {
            if (qVarArr[i] != null && qVarArr[i].equals(this.b)) {
                d.a().g(this.b.r)[this.b.e] = this.b;
                qVarArr[i] = null;
                return;
            }
        }
    }

    private void u() {
        q[] qVarArr = ((t) this.l).a;
        for (int i = 0; i < qVarArr.length; i++) {
            if (qVarArr[i] != null && qVarArr[i].equals(this.b)) {
                d.a().g(this.b.r)[this.b.e] = this.b;
                qVarArr[i] = null;
                return;
            }
        }
    }

    private void v() {
        q[] qVarArr = ((u) this.l).a;
        for (int i = 0; i < qVarArr.length; i++) {
            if (qVarArr[i] != null && qVarArr[i].equals(this.b)) {
                d.a().g(this.b.r)[this.b.e] = this.b;
                qVarArr[i] = null;
                return;
            }
        }
    }

    private void w() {
        q[] qVarArr = ((v) this.l).a;
        for (int i = 0; i < qVarArr.length; i++) {
            if (qVarArr[i] != null && qVarArr[i].equals(this.b)) {
                d.a().g(this.b.r)[this.b.e] = this.b;
                qVarArr[i] = null;
                return;
            }
        }
    }

    private void x() {
        q[] qVarArr = ((ab) this.l).b;
        for (int i = 0; i < qVarArr.length; i++) {
            if (qVarArr[i] != null && qVarArr[i].equals(this.b)) {
                d.a().g(this.b.r)[this.b.e] = this.b;
                qVarArr[i] = null;
                return;
            }
        }
    }

    private void y() {
        try {
            boolean z = false;
            ao aoVar = (ao) this.l;
            q[] b = aoVar.b(this.b);
            for (int i = 0; i < aoVar.b.length; i++) {
                if (aoVar.b[i] != null && aoVar.b[i].c == this.b.c) {
                    b[aoVar.b[i].e] = aoVar.b[i];
                    aoVar.b[i] = this.b;
                    b[this.b.e] = null;
                    return;
                }
            }
            int i2 = 0;
            while (true) {
                if (i2 < aoVar.b.length) {
                    if (aoVar.b[i2] != null) {
                        i2++;
                    } else {
                        aoVar.b[i2] = this.b;
                        z = true;
                        break;
                    }
                } else {
                    break;
                }
            }
            if (!z) {
                com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.fU, -65536);
            } else {
                b[this.b.e] = null;
            }
        } catch (Exception unused) {
        }
    }

    private void z() {
        q[] qVarArr = ((ao) this.l).b;
        for (int i = 0; i < qVarArr.length; i++) {
            if (qVarArr[i] != null && qVarArr[i].equals(this.b)) {
                d.a().g(this.b.r)[this.b.e] = this.b;
                qVarArr[i] = null;
                return;
            }
        }
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x00a0: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:22:0x009f */
    private void A() {
        Exception a;
        try {
            an anVar = (an) this.l;
            q[] b = anVar.b(this.b);
            if (this.b.h().f == 13) {
                if (anVar.a[0] != null) {
                    b[anVar.a[0].e] = anVar.a[0];
                }
                anVar.a[0] = this.b;
                b[this.b.e] = null;
            } else {
                boolean z = false;
                int i = 0;
                while (true) {
                    if (i >= anVar.b.length) {
                        break;
                    } else if (anVar.b[i] != null) {
                        i++;
                    } else {
                        anVar.b[i] = this.b;
                        z = true;
                        break;
                    }
                }
                if (!z) {
                    com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.bl, -65536);
                    return;
                }
            }
            b[this.b.e] = null;
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a);
        }
    }

    private void B() {
        an anVar = (an) this.l;
        q[] qVarArr = anVar.a;
        int i = 0;
        while (true) {
            if (i >= qVarArr.length) {
                break;
            } else if (qVarArr[i] == null || !qVarArr[i].equals(this.b)) {
                i++;
            } else {
                d.a().g(this.b.r)[this.b.e] = this.b;
                qVarArr[i] = null;
                break;
            }
        }
        q[] qVarArr2 = anVar.b;
        for (int i2 = 0; i2 < qVarArr2.length; i2++) {
            if (qVarArr2[i2] != null && qVarArr2[i2].equals(this.b)) {
                d.a().W[this.b.e] = this.b;
                qVarArr2[i2] = null;
                return;
            }
        }
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x009f: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:22:0x009e */
    private void C() {
        Exception a;
        try {
            ag agVar = (ag) this.l;
            q[] b = agVar.b(this.b);
            if (this.b.i()) {
                if (agVar.a[0] != null) {
                    d.a().g(agVar.a[0].r)[agVar.a[0].e] = agVar.a[0];
                }
                agVar.a[0] = this.b;
            } else {
                boolean z = false;
                int i = 0;
                while (true) {
                    if (i >= agVar.b.length) {
                        break;
                    } else if (agVar.b[i] != null) {
                        i++;
                    } else {
                        agVar.b[i] = this.b;
                        z = true;
                        break;
                    }
                }
                if (!z) {
                    com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.ov, -65536);
                    return;
                }
            }
            b[this.b.e] = null;
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a);
        }
    }

    private void D() {
        ag agVar = (ag) this.l;
        q[] qVarArr = agVar.a;
        int i = 0;
        while (true) {
            if (i >= qVarArr.length) {
                break;
            } else if (qVarArr[i] == null || !qVarArr[i].equals(this.b)) {
                i++;
            } else {
                d.a().g(this.b.r)[this.b.e] = this.b;
                qVarArr[i] = null;
                break;
            }
        }
        q[] qVarArr2 = agVar.b;
        for (int i2 = 0; i2 < qVarArr2.length; i2++) {
            if (qVarArr2[i2] != null && qVarArr2[i2].equals(this.b)) {
                d.a().W[this.b.e] = this.b;
                qVarArr2[i2] = null;
                return;
            }
        }
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x009f: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:22:0x009e */
    private void E() {
        Exception a;
        try {
            ai aiVar = (ai) this.l;
            q[] b = aiVar.b(this.b);
            if (this.b.i()) {
                if (aiVar.a[0] != null) {
                    d.a().g(aiVar.a[0].r)[aiVar.a[0].e] = aiVar.a[0];
                }
                aiVar.a[0] = this.b;
            } else {
                boolean z = false;
                int i = 0;
                while (true) {
                    if (i >= aiVar.b.length) {
                        break;
                    } else if (aiVar.b[i] != null) {
                        i++;
                    } else {
                        aiVar.b[i] = this.b;
                        z = true;
                        break;
                    }
                }
                if (!z) {
                    com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.ov, -65536);
                    return;
                }
            }
            b[this.b.e] = null;
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a);
        }
    }

    private void F() {
        ai aiVar = (ai) this.l;
        q[] qVarArr = aiVar.a;
        int i = 0;
        while (true) {
            if (i >= qVarArr.length) {
                break;
            } else if (qVarArr[i] == null || !qVarArr[i].equals(this.b)) {
                i++;
            } else {
                d.a().g(this.b.r)[this.b.e] = this.b;
                qVarArr[i] = null;
                break;
            }
        }
        q[] qVarArr2 = aiVar.b;
        for (int i2 = 0; i2 < qVarArr2.length; i2++) {
            if (qVarArr2[i2] != null && qVarArr2[i2].equals(this.b)) {
                d.a().W[this.b.e] = this.b;
                qVarArr2[i2] = null;
                return;
            }
        }
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x009f: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:22:0x009e */
    private void G() {
        Exception a;
        try {
            af afVar = (af) this.l;
            q[] b = afVar.b(this.b);
            if (this.b.i()) {
                if (afVar.a[0] != null) {
                    d.a().g(afVar.a[0].r)[afVar.a[0].e] = afVar.a[0];
                }
                afVar.a[0] = this.b;
            } else {
                boolean z = false;
                int i = 0;
                while (true) {
                    if (i >= afVar.b.length) {
                        break;
                    } else if (afVar.b[i] != null) {
                        i++;
                    } else {
                        afVar.b[i] = this.b;
                        z = true;
                        break;
                    }
                }
                if (!z) {
                    com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.ov, -65536);
                    return;
                }
            }
            b[this.b.e] = null;
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a);
        }
    }

    private void H() {
        af afVar = (af) this.l;
        q[] qVarArr = afVar.a;
        int i = 0;
        while (true) {
            if (i >= qVarArr.length) {
                break;
            } else if (qVarArr[i] == null || !qVarArr[i].equals(this.b)) {
                i++;
            } else {
                d.a().g(this.b.r)[this.b.e] = this.b;
                qVarArr[i] = null;
                break;
            }
        }
        q[] qVarArr2 = afVar.b;
        for (int i2 = 0; i2 < qVarArr2.length; i2++) {
            if (qVarArr2[i2] != null && qVarArr2[i2].equals(this.b)) {
                d.a().W[this.b.e] = this.b;
                qVarArr2[i2] = null;
                return;
            }
        }
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x009f: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:22:0x009e */
    private void I() {
        Exception a;
        try {
            ak akVar = (ak) this.l;
            q[] b = akVar.b(this.b);
            if (this.b.i()) {
                if (akVar.a[0] != null) {
                    d.a().g(akVar.a[0].r)[akVar.a[0].e] = akVar.a[0];
                }
                akVar.a[0] = this.b;
            } else {
                boolean z = false;
                int i = 0;
                while (true) {
                    if (i >= akVar.b.length) {
                        break;
                    } else if (akVar.b[i] != null) {
                        i++;
                    } else {
                        akVar.b[i] = this.b;
                        z = true;
                        break;
                    }
                }
                if (!z) {
                    com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.ov, -65536);
                    return;
                }
            }
            b[this.b.e] = null;
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a);
        }
    }

    private void J() {
        ak akVar = (ak) this.l;
        q[] qVarArr = akVar.a;
        int i = 0;
        while (true) {
            if (i >= qVarArr.length) {
                break;
            } else if (qVarArr[i] == null || !qVarArr[i].equals(this.b)) {
                i++;
            } else {
                d.a().g(this.b.r)[this.b.e] = this.b;
                qVarArr[i] = null;
                break;
            }
        }
        q[] qVarArr2 = akVar.b;
        for (int i2 = 0; i2 < qVarArr2.length; i2++) {
            if (qVarArr2[i2] != null && qVarArr2[i2].equals(this.b)) {
                d.a().W[this.b.e] = this.b;
                qVarArr2[i2] = null;
                return;
            }
        }
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x009f: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:22:0x009e */
    private void K() {
        Exception a;
        try {
            aj ajVar = (aj) this.l;
            q[] b = ajVar.b(this.b);
            if (this.b.i()) {
                if (ajVar.a[0] != null) {
                    d.a().g(ajVar.a[0].r)[ajVar.a[0].e] = ajVar.a[0];
                }
                ajVar.a[0] = this.b;
            } else {
                boolean z = false;
                int i = 0;
                while (true) {
                    if (i >= ajVar.b.length) {
                        break;
                    } else if (ajVar.b[i] != null) {
                        i++;
                    } else {
                        ajVar.b[i] = this.b;
                        z = true;
                        break;
                    }
                }
                if (!z) {
                    com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.ov, -65536);
                    return;
                }
            }
            b[this.b.e] = null;
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a);
        }
    }

    private void L() {
        aj ajVar = (aj) this.l;
        q[] qVarArr = ajVar.a;
        int i = 0;
        while (true) {
            if (i >= qVarArr.length) {
                break;
            } else if (qVarArr[i] == null || !qVarArr[i].equals(this.b)) {
                i++;
            } else {
                d.a().g(this.b.r)[this.b.e] = this.b;
                qVarArr[i] = null;
                break;
            }
        }
        q[] qVarArr2 = ajVar.b;
        for (int i2 = 0; i2 < qVarArr2.length; i2++) {
            if (qVarArr2[i2] != null && qVarArr2[i2].equals(this.b)) {
                d.a().W[this.b.e] = this.b;
                qVarArr2[i2] = null;
                return;
            }
        }
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x005a: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:15:0x0059 */
    private void M() {
        Exception a;
        try {
            boolean z = false;
            ap apVar = (ap) this.l;
            int i = 0;
            while (true) {
                if (i >= apVar.b.length) {
                    break;
                } else if (apVar.b[i] != null) {
                    i++;
                } else {
                    apVar.b[i] = this.b;
                    z = true;
                    break;
                }
            }
            if (!z) {
                com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.bc, -65536);
            } else {
                d.a().W[this.b.e] = null;
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a);
        }
    }

    private void N() {
        ap apVar = (ap) this.l;
        if (apVar.a != null && this.b.equals(apVar.a)) {
            apVar.e(this.b.e);
            apVar.a = null;
            return;
        }
        q[] qVarArr = apVar.b;
        for (int i = 0; i < qVarArr.length; i++) {
            if (qVarArr[i] != null && qVarArr[i].equals(this.b)) {
                d.a().W[this.b.e] = this.b;
                qVarArr[i] = null;
                return;
            }
        }
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x005a: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:15:0x0059 */
    private void O() {
        Exception a;
        try {
            boolean z = false;
            ae aeVar = (ae) this.l;
            int i = 0;
            while (true) {
                if (i >= aeVar.e.length) {
                    break;
                } else if (aeVar.e[i] != null) {
                    i++;
                } else {
                    aeVar.e[i] = this.b;
                    z = true;
                    break;
                }
            }
            if (!z) {
                com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.bu, -65536);
            } else {
                d.a().W[this.b.e] = null;
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a);
        }
    }

    private void P() {
        q[] qVarArr = ((ae) this.l).e;
        for (int i = 0; i < qVarArr.length; i++) {
            if (qVarArr[i] != null && qVarArr[i].equals(this.b)) {
                d.a().W[this.b.e] = this.b;
                qVarArr[i] = null;
                return;
            }
        }
    }

    /* JADX WARN: Type inference failed for: r0v1, types: [com.donglh.narutoninjasaga.e.ak, java.lang.Exception] */
    private void Q() {
        ?? akVar;
        try {
            akVar = new com.donglh.narutoninjasaga.e.ak((byte) 36);
            akVar.b(this.b.e);
            akVar.l();
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) akVar);
        }
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x005a: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:15:0x0059 */
    private void T() {
        Exception a;
        try {
            boolean z = false;
            bu buVar = (bu) this.l;
            int i = 0;
            while (true) {
                if (i >= buVar.a.length) {
                    break;
                } else if (buVar.a[i] != null) {
                    i++;
                } else {
                    buVar.a[i] = this.b;
                    z = true;
                    break;
                }
            }
            if (!z) {
                com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.fW, -65536);
            } else {
                d.a().W[this.b.e] = null;
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a);
        }
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x00b6: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:28:0x00b5 */
    private void U() {
        Exception a;
        try {
            boolean z = false;
            hc hcVar = (hc) this.l;
            q[] b = hcVar.b(this.b);
            if (hcVar.b != 68 && hcVar.b != 69 && hcVar.b != 70 && hcVar.b != 71) {
                int i = 0;
                while (true) {
                    if (i >= hcVar.a.length) {
                        break;
                    } else if (hcVar.a[i] != null) {
                        i++;
                    } else {
                        hcVar.a[i] = this.b;
                        z = true;
                        break;
                    }
                }
                if (!z) {
                    com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.fW, -65536);
                    return;
                }
            } else {
                if (hcVar.a[0] != null) {
                    d.a().g(hcVar.a[0].r)[hcVar.a[0].e] = hcVar.a[0];
                }
                hcVar.a[0] = this.b;
            }
            b[this.b.e] = null;
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a);
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v4, types: [com.donglh.narutoninjasaga.d.q[], java.lang.Exception] */
    private void V() {
        ?? r0;
        try {
            r0 = ((dh) this.l).a;
            for (int i = 0; i < r0.length; i++) {
                if (r0[i] != 0 && r0[i].equals(this.b)) {
                    d.a().g(this.b.r)[this.b.e] = this.b;
                    r0[i] = 0;
                    return;
                }
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
        }
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x005a: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:15:0x0059 */
    private void W() {
        Exception a;
        try {
            boolean z = false;
            cq cqVar = (cq) this.l;
            int i = 0;
            while (true) {
                if (i >= cqVar.a.length) {
                    break;
                } else if (cqVar.a[i] != null) {
                    i++;
                } else {
                    cqVar.a[i] = this.b;
                    z = true;
                    break;
                }
            }
            if (!z) {
                com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.fW, -65536);
            } else {
                d.a().W[this.b.e] = null;
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a);
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v4, types: [com.donglh.narutoninjasaga.d.q[], java.lang.Exception] */
    private void X() {
        ?? r0;
        try {
            r0 = ((cq) this.l).a;
            for (int i = 0; i < r0.length; i++) {
                if (r0[i] != 0 && r0[i].equals(this.b)) {
                    d.a().W[this.b.e] = this.b;
                    r0[i] = 0;
                    return;
                }
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v4, types: [com.donglh.narutoninjasaga.d.q[], java.lang.Exception] */
    private void Y() {
        ?? r0;
        try {
            r0 = ((bu) this.l).a;
            for (int i = 0; i < r0.length; i++) {
                if (r0[i] != 0 && r0[i].equals(this.b)) {
                    d.a().W[this.b.e] = this.b;
                    r0[i] = 0;
                    return;
                }
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v4, types: [com.donglh.narutoninjasaga.d.q[], java.lang.Exception] */
    private void Z() {
        ?? r0;
        try {
            r0 = ((hc) this.l).a;
            for (int i = 0; i < r0.length; i++) {
                if (r0[i] != 0 && r0[i].equals(this.b)) {
                    d.a().g(this.b.r)[this.b.e] = this.b;
                    r0[i] = 0;
                    return;
                }
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
        }
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x00d7: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:31:0x00d6 */
    private void aa() {
        Exception a;
        try {
            jn jnVar = (jn) this.l;
            q[] b = jnVar.b(this.b);
            boolean z = false;
            if (this.b.v()) {
                if (jnVar.a[0] != null) {
                    d.a().g(jnVar.a[0].r)[jnVar.a[0].e] = jnVar.a[0];
                }
                jnVar.a[0] = this.b;
            } else {
                int i = 0;
                while (true) {
                    if (i < jnVar.b.length) {
                        if (jnVar.b[i] == null || jnVar.b[i].c == this.b.c) {
                            i++;
                        } else {
                            jnVar.e();
                            break;
                        }
                    } else {
                        break;
                    }
                }
                int i2 = 0;
                while (true) {
                    if (i2 >= jnVar.b.length) {
                        break;
                    } else if (jnVar.b[i2] != null) {
                        i2++;
                    } else {
                        jnVar.b[i2] = this.b;
                        z = true;
                        break;
                    }
                }
                if (!z) {
                    com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.bc, -65536);
                    return;
                }
            }
            b[this.b.e] = null;
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a);
        }
    }

    private void ab() {
        jn jnVar = (jn) this.l;
        q[] qVarArr = jnVar.a;
        int i = 0;
        while (true) {
            if (i >= qVarArr.length) {
                break;
            } else if (qVarArr[i] == null || !qVarArr[i].equals(this.b)) {
                i++;
            } else {
                d.a().g(this.b.r)[this.b.e] = this.b;
                qVarArr[i] = null;
                break;
            }
        }
        q[] qVarArr2 = jnVar.b;
        for (int i2 = 0; i2 < qVarArr2.length; i2++) {
            if (qVarArr2[i2] != null && qVarArr2[i2].equals(this.b)) {
                d.a().W[this.b.e] = this.b;
                qVarArr2[i2] = null;
                return;
            }
        }
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x005a: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:15:0x0059 */
    private void ac() {
        Exception a;
        try {
            boolean z = false;
            jd jdVar = (jd) this.l;
            int i = 0;
            while (true) {
                if (i >= jdVar.a.length) {
                    break;
                } else if (jdVar.a[i] != null) {
                    i++;
                } else {
                    jdVar.a[i] = this.b;
                    z = true;
                    break;
                }
            }
            if (!z) {
                com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.fW, -65536);
            } else {
                d.a().W[this.b.e] = null;
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a);
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v4, types: [com.donglh.narutoninjasaga.d.q[], java.lang.Exception] */
    private void ad() {
        ?? r0;
        try {
            r0 = ((jd) this.l).a;
            for (int i = 0; i < r0.length; i++) {
                if (r0[i] != 0 && r0[i].equals(this.b)) {
                    d.a().W[this.b.e] = this.b;
                    r0[i] = 0;
                    return;
                }
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
        }
    }

    public final dg e() {
        for (int i = 0; i < this.a.length; i++) {
            if (this.a[i].a.equals(com.donglh.narutoninjasaga.c.a.l) || this.a[i].a.equals(com.donglh.narutoninjasaga.c.a.r) || this.a[i].a.equals(com.donglh.narutoninjasaga.c.a.qs) || this.a[i].a.equals(com.donglh.narutoninjasaga.c.a.qu)) {
                return this.a[i];
            }
        }
        return null;
    }
}
