package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.Input;
import java.util.Vector;
/* compiled from: LangLa_lb.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/hu.class */
public final class hu extends et {
    private fw[] f;
    private fx g;
    private dg h;
    private dg B;
    public int a;
    public int b;
    public int c;
    public int d;
    public boolean e;

    public hu(com.donglh.narutoninjasaga.e.ai aiVar) {
        super(aiVar, new String[]{com.donglh.narutoninjasaga.c.a.aJ});
        this.f = new fw[4];
        this.h = a(this.aM - 52, this.aN - 30, com.donglh.narutoninjasaga.c.a.s, this, 0, -8);
        this.h.a_(45, this.h.aN);
        a(this.h, 0);
        this.B = a(this.aM - 100, this.aN - 30, com.donglh.narutoninjasaga.c.a.kK, this, 5003, -8);
        this.B.a_(45, this.B.aN);
        a(this.B, 0);
        this.f[0] = new fw((byte) 0, 7, a_() + Input.Keys.ESCAPE, 93, 21, 93, 1);
        this.f[1] = new fw((byte) 0, 7, a_() + 155, 93, 21, 93, 1);
        this.f[2] = new fw((byte) 0, Input.Keys.BUTTON_R1, a_() + Input.Keys.ESCAPE, 93, 21, 93, 1);
        this.f[3] = new fw((byte) 0, Input.Keys.BUTTON_R1, a_() + 155, 93, 21, 93, 1);
        this.g = new fx((byte) 1, 7, a_() + 3, 288, 128, 32, com.donglh.narutoninjasaga.e.aw.c(d.a().X.length, 9), 9);
    }

    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        super.a(guVar, i, i2);
        if (this.i.b == 0) {
            switch (guVar.b) {
                case 0:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.k(1);
                    return;
                case 2000:
                    try {
                        com.donglh.narutoninjasaga.e.ak.b((byte) -82).l();
                        return;
                    } catch (Exception unused) {
                        return;
                    }
                case 5001:
                    if (guVar.j.i >= 0) {
                        this.n = a(guVar, this, d.a().X[guVar.j.i]);
                        return;
                    }
                    return;
                case 5002:
                    this.n = a(guVar, this, (q) null);
                    return;
                case 5003:
                    com.donglh.narutoninjasaga.e.f.c().am.a(com.donglh.narutoninjasaga.c.a.kO, 2000, this);
                    return;
                case 5004:
                    a(guVar.j, guVar.j.i);
                    if (guVar.j.i >= 0) {
                        a(11000, com.donglh.narutoninjasaga.c.a.pg + " " + com.donglh.narutoninjasaga.c.a.rh[0], guVar.j);
                        return;
                    }
                    return;
                case 5005:
                    a(guVar.j, guVar.j.i);
                    if (guVar.j.i >= 0) {
                        a(11001, com.donglh.narutoninjasaga.c.a.pg + " " + com.donglh.narutoninjasaga.c.a.rn[0], guVar.j);
                        return;
                    }
                    return;
                case 5006:
                    a(guVar.j, guVar.j.i);
                    if (guVar.j.i >= 0) {
                        a(11002, com.donglh.narutoninjasaga.c.a.pg + " " + com.donglh.narutoninjasaga.c.a.rh[1], guVar.j);
                        return;
                    }
                    return;
                case 5007:
                    a(guVar.j, guVar.j.i);
                    if (guVar.j.i >= 0) {
                        a(11003, com.donglh.narutoninjasaga.c.a.pg + " " + com.donglh.narutoninjasaga.c.a.rn[1], guVar.j);
                        return;
                    }
                    return;
                default:
                    return;
            }
        }
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x00bc: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:10:0x00bb */
    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.cn
    public final void a(int i, Object obj, co coVar) {
        Exception a;
        try {
            super.a(i, obj, coVar);
            switch (i) {
                case 11000:
                    com.donglh.narutoninjasaga.e.n.n().a((cn) new bi(this.s, (byte) 0, this.a, this.b, this.c, this.d));
                    return;
                case 11001:
                    com.donglh.narutoninjasaga.e.n.n().a((cn) new bi(this.s, (byte) 2, this.a, this.b, this.c, this.d));
                    return;
                case 11002:
                    com.donglh.narutoninjasaga.e.n.n().a((cn) new bi(this.s, (byte) 1, this.a, this.b, this.c, this.d));
                    return;
                case 11003:
                    com.donglh.narutoninjasaga.e.n.n().a((cn) new bi(this.s, (byte) 3, this.a, this.b, this.c, this.d));
                    return;
                default:
                    return;
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a);
        }
    }

    private void a(fw fwVar, int i) {
        this.g.i = -1;
        fwVar.i = i;
    }

    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.cn
    public final void a_(int i) {
    }

    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        super.a(lVar);
        if (this.i.b == 0) {
            a(lVar, this.g);
            for (int i = 0; i < this.g.g; i++) {
                for (int i2 = 0; i2 < this.g.o; i2++) {
                    if (this.g.b(i)) {
                        b(lVar, i2 * this.g.f, i * this.g.f, d.a().X[(i * this.g.o) + i2], (i * this.g.o) + i2 == this.g.i);
                    }
                }
            }
            b(lVar);
            a(lVar, this.aY + 4, this.aZ + a_());
            int i3 = 0;
            for (int i4 = 0; i4 < d.a().X.length; i4++) {
                if (d.a().X[i4] != null) {
                    i3++;
                }
            }
            kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.aK + " " + i3 + "/" + d.a().X.length, 288, 140, 1, -1, -16777216);
            if (this.e) {
                for (int i5 = 0; i5 < this.f.length; i5++) {
                    a(lVar, this.aY + this.f[i5].aY, this.aZ + this.f[i5].aZ);
                    com.donglh.narutoninjasaga.e.r.c(lVar, 26, 27, 0, 0, this.f[i5].aM, this.f[i5].aN);
                    switch (i5) {
                        case 0:
                            a(lVar, 0, 12, "", (byte) 0);
                            kk.b(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.c(this.c), 30, 11, 20, -3089954, -16777216);
                            break;
                        case 1:
                            a(lVar, 0, 8, "", (byte) 2);
                            kk.b(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.c(this.a), 30, 11, 20, -2560, -16777216);
                            break;
                        case 2:
                            a(lVar, 0, 10, "", (byte) 1);
                            kk.b(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.c(this.d), 30, 11, 20, -3089954, -16777216);
                            break;
                        case 3:
                            a(lVar, 0, 8, "", (byte) 3);
                            kk.b(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.c(this.b), 30, 11, 20, -2560, -16777216);
                            break;
                    }
                }
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void b() {
        super.b();
        if (this.i.b == 0) {
            this.g.a();
        }
    }

    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        Vector c = super.c();
        if (this.i.b == 0) {
            c.addElement(this.g.a(5001, this));
            if (this.e) {
                for (int i = 0; i < this.f.length; i++) {
                    c.addElement(this.f[i].a(i + 5004, this));
                }
            }
        }
        return c;
    }

    public final int e() {
        return this.i.b;
    }

    @Override // com.donglh.narutoninjasaga.d.et
    public final void a(int i, String str, fw fwVar) {
        hi a = a(this, fwVar.aY + 30, fwVar.aZ, new gz(0, new short[]{(short) i}, new String[]{str}));
        a.a();
        this.n = a;
    }
}
