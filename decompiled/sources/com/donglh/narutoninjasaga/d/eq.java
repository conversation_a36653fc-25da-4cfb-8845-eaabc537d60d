package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.net.HttpStatus;
import java.util.Vector;
/* compiled from: LangLa_g.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/eq.class */
public final class eq extends em {
    private q[] a;
    private fx b;
    private dg c;
    private dg d;
    private int e = -1;
    private int f = 0;

    public eq(com.donglh.narutoninjasaga.e.ai aiVar, com.donglh.narutoninjasaga.e.ak akVar) {
        try {
            this.s = aiVar;
            d(208, HttpStatus.SC_OK);
            this.u = false;
            this.c = c(com.donglh.narutoninjasaga.c.a.nZ, 100);
            this.d = b(com.donglh.narutoninjasaga.c.a.oN, 101);
            this.d.a(true);
            this.a = new q[akVar.b.a.readShort()];
            for (int i = 0; i < this.a.length; i++) {
                q qVar = new q();
                qVar.a(akVar);
                qVar.e = i;
                this.a[qVar.e] = qVar;
            }
            this.b = new fx((byte) 1, 9, a_() + 5, 192, 128, 32, 6, 4);
        } catch (Exception unused) {
        }
    }

    public final void e(int i, int i2) {
        this.e = i;
        this.f = i2;
        this.c.a(true);
        this.d.a(false);
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a(int i, Object obj, co coVar) {
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a_(int i) {
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        a(lVar, (int) this.aY, (int) this.aZ, (int) this.aM, (int) this.aN, com.donglh.narutoninjasaga.c.a.eU, (byte) 2, false);
        a(lVar, this.b);
        int i = 0;
        boolean z = false;
        for (int i2 = 0; i2 < this.b.o; i2++) {
            for (int i3 = 0; i3 < this.b.g; i3++) {
                if (i < this.a.length) {
                    a(lVar, i3 * this.b.f, i2 * this.b.f, this.a[i], (i2 * this.b.g) + i3 == this.b.i, 0);
                } else {
                    a(lVar, i3 * this.b.f, i2 * this.b.f, (q) null, (i2 * this.b.g) + i3 == this.b.i, 0);
                }
                if (!z && this.a[i].c == this.e && this.a[i].O() == this.f && com.donglh.narutoninjasaga.e.f.c().i % 10 > 5) {
                    z = true;
                    lVar.e(-2560);
                    lVar.b(i3 * this.b.f, i2 * this.b.f, 28, 28);
                }
                i++;
            }
        }
        b(lVar);
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void b() {
    }

    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        iz izVar;
        super.a(guVar, i, i2);
        switch (guVar.b) {
            case 100:
                try {
                    com.donglh.narutoninjasaga.e.ak.b((byte) -85).l();
                    return;
                } catch (Exception unused) {
                    return;
                }
            case 101:
                this.e = -1;
                this.f = 0;
                this.c.a(false);
                this.d.a(true);
                return;
            case 5001:
                int i3 = guVar.j.i;
                fw fwVar = guVar.j;
                this.b.i = -1;
                fwVar.i = i3;
                if (guVar.j.i >= 0) {
                    q qVar = this.a[guVar.j.i];
                    if (qVar != null) {
                        fx fxVar = (fx) guVar.j;
                        if (fxVar.i >= 0 && fxVar.i < fxVar.g * fxVar.o) {
                            fxVar.d();
                            int i4 = fxVar.i % fxVar.g;
                            int i5 = (fxVar.i / fxVar.g) - (fxVar.h >= 0 ? fxVar.h : 0);
                            if (fxVar.l == 0) {
                                i4 = (fxVar.i % fxVar.g) - (fxVar.h >= 0 ? fxVar.h : 0);
                                i5 = fxVar.i / fxVar.g;
                            }
                            izVar = a(this, (i4 * fxVar.f) + fxVar.f + fxVar.aY, (i5 * fxVar.f) + fxVar.aZ, fxVar.f, qVar);
                            this.n = izVar;
                            return;
                        }
                    }
                    izVar = null;
                    this.n = izVar;
                    return;
                }
                return;
            default:
                return;
        }
    }

    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        Vector c = super.c();
        c.addElement(this.b.a(5001, this));
        return c;
    }
}
