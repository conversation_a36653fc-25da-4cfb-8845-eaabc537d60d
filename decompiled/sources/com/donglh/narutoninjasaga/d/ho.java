package com.donglh.narutoninjasaga.d;

import java.util.Vector;
/* compiled from: LangLa_kw.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/ho.class */
public final class ho extends cl {
    private Vector f = new Vector();
    public Vector b = new Vector();
    private Vector g = new Vector();
    private fw h;
    private fw i;
    private byte j;
    private int k;
    private int s;
    public static hz d;
    private int t;
    private static String u;
    private static dv[] e = new dv[5];
    public static byte[] a = new byte[9];
    public static String c = "";

    static {
        for (int i = 0; i < e.length; i++) {
            e[i] = new dv();
        }
        u = "";
    }

    public ho(int i, int i2, int i3, int i4, cn cnVar, byte b, int i5, int i6) {
        this.t = 16;
        this.l = cnVar;
        this.j = b;
        this.k = i5;
        this.t = i6;
        g(i, i2);
        a_(i3, i4);
        if (b == 1) {
            this.i = new fw((byte) 1, i3 - 92, 0, 92, i4, i6, this.b.size());
        } else {
            this.i = new fw((byte) 1, 0, 0, 0, i4, i6, this.b.size());
        }
        this.h = new fw((byte) 1, 0, 0, i3 - this.i.aM, i4, i6, this.f.size());
        hh.e();
    }

    /* JADX WARN: Code restructure failed: missing block: B:8:0x001e, code lost:
        r0 = new com.donglh.narutoninjasaga.d.dz(com.donglh.narutoninjasaga.d.ho.a[1], r6, r7);
        r0.f = r8;
        r0 = com.donglh.narutoninjasaga.d.ho.e[0];
        r0.a(r0);
     */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v12, types: [com.donglh.narutoninjasaga.d.dv[]] */
    /* JADX WARN: Type inference failed for: r0v13, types: [com.donglh.narutoninjasaga.d.dv] */
    /* JADX WARN: Type inference failed for: r0v14 */
    /* JADX WARN: Type inference failed for: r0v2 */
    /* JADX WARN: Type inference failed for: r0v3 */
    /* JADX WARN: Type inference failed for: r0v4, types: [java.lang.Exception] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static void a(java.lang.String r6, java.lang.String r7, com.donglh.narutoninjasaga.d.ew r8) {
        /*
            r0 = r7
            java.lang.String r1 = com.donglh.narutoninjasaga.d.ho.u
            boolean r0 = r0.equals(r1)
            r9 = r0
            r0 = 0
            r10 = r0
        Lb:
            r0 = r10
            byte[] r1 = com.donglh.narutoninjasaga.d.ho.a     // Catch: java.lang.Exception -> L47
            int r1 = r1.length     // Catch: java.lang.Exception -> L47
            if (r0 >= r1) goto L44
            byte[] r0 = com.donglh.narutoninjasaga.d.ho.a     // Catch: java.lang.Exception -> L47
            r1 = r10
            r0 = r0[r1]     // Catch: java.lang.Exception -> L47
            r1 = 1
            if (r0 != r1) goto L3e
            com.donglh.narutoninjasaga.d.dz r0 = new com.donglh.narutoninjasaga.d.dz     // Catch: java.lang.Exception -> L47
            r1 = r0
            byte[] r2 = com.donglh.narutoninjasaga.d.ho.a     // Catch: java.lang.Exception -> L47
            r3 = 1
            r2 = r2[r3]     // Catch: java.lang.Exception -> L47
            r3 = r6
            r4 = r7
            r1.<init>(r2, r3, r4)     // Catch: java.lang.Exception -> L47
            r1 = r0
            r6 = r1
            r1 = r8
            r0.f = r1     // Catch: java.lang.Exception -> L47
            com.donglh.narutoninjasaga.d.dv[] r0 = com.donglh.narutoninjasaga.d.ho.e     // Catch: java.lang.Exception -> L47
            r1 = 0
            r0 = r0[r1]     // Catch: java.lang.Exception -> L47
            r1 = r6
            r0.a(r1)     // Catch: java.lang.Exception -> L47
            goto L4d
        L3e:
            int r10 = r10 + 1
            goto Lb
        L44:
            goto L4d
        L47:
            r1 = move-exception
            r10 = r1
            com.donglh.narutoninjasaga.e.aw.a(r0)
        L4d:
            r0 = r7
            com.donglh.narutoninjasaga.d.ho.u = r0
            r0 = r9
            if (r0 != 0) goto L5b
            com.donglh.narutoninjasaga.e.n r0 = com.donglh.narutoninjasaga.e.n.n()
            r0.G()
        L5b:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: com.donglh.narutoninjasaga.d.ho.a(java.lang.String, java.lang.String, com.donglh.narutoninjasaga.d.ew):void");
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v15, types: [byte[]] */
    /* JADX WARN: Type inference failed for: r0v16 */
    public static void a(int i, String str, String str2) {
        boolean equals = str2.equals(u);
        Exception exc = null;
        int i2 = 0;
        while (true) {
            try {
                if (i2 >= a.length) {
                    break;
                }
                ?? r0 = a[i2];
                if (r0 != i) {
                    i2++;
                    exc = r0;
                } else {
                    e[0].a(new dz(a[i], str, str2));
                    break;
                }
            } catch (Exception e2) {
                com.donglh.narutoninjasaga.e.aw.a(exc);
            }
        }
        switch (i) {
            case 3:
                e[4].a(new dz(3, str, str2));
                break;
            case 4:
                e[2].a(new dz(4, str, str2));
                break;
            case 5:
                e[3].a(new dz(5, str, str2));
        }
        u = str2;
        if (!equals) {
            com.donglh.narutoninjasaga.e.n.n().G();
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v11, types: [java.util.Vector] */
    /* JADX WARN: Type inference failed for: r0v17 */
    /* JADX WARN: Type inference failed for: r0v2 */
    /* JADX WARN: Type inference failed for: r0v3 */
    /* JADX WARN: Type inference failed for: r0v4, types: [java.lang.Exception] */
    public static void a(String str, String str2, String str3) {
        boolean equals = str2.equals(u);
        ?? r0 = 0;
        int i = 0;
        while (true) {
            try {
                if (i >= a.length) {
                    break;
                }
                byte b = a[i];
                if (b != 6) {
                    i++;
                    r0 = b;
                } else {
                    e[0].a.addElement(new dz(a[6], str, str2, str3));
                    break;
                }
            } catch (Exception e2) {
                com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
            }
        }
        r0 = e[1].a;
        r0.addElement(new dz(6, str, str2, str3));
        u = str2;
        if (!equals) {
            com.donglh.narutoninjasaga.e.n.n().G();
        }
    }

    public final void a() {
        this.b.clear();
        this.f.clear();
        this.g.clear();
        Vector vector = new Vector();
        for (int i = 0; i < e[this.j].a.size(); i++) {
            String str = "";
            dz dzVar = (dz) e[this.j].a.elementAt(i);
            switch (dzVar.a) {
                case 0:
                    if (dzVar.b.equals(d.a().N)) {
                        str = str + ":-lc c#ME" + dzVar.b + ": c#white";
                        break;
                    } else {
                        str = str + ":-lc c#LC" + dzVar.b + ": c#white";
                        break;
                    }
                case 1:
                    if (dzVar.b.equals(d.a().N)) {
                        str = str + ":-tg c#ME" + dzVar.b + ": c#white";
                        break;
                    } else if (dzVar.b.equals(com.donglh.narutoninjasaga.c.a.f0do)) {
                        str = str + ":-tg c#yellow" + dzVar.b + ": c#white";
                        break;
                    } else {
                        str = str + ":-tg c#TG" + dzVar.b + ": c#white";
                        break;
                    }
                case 2:
                    if (dzVar.b.equals(d.a().N)) {
                        str = str + ":-tt c#ME" + dzVar.b + ": c#white";
                        break;
                    } else {
                        str = str + ":-tt c#TT" + dzVar.b + ": c#white";
                        break;
                    }
                case 3:
                    if (dzVar.b.equals(d.a().N)) {
                        str = str + ":-mp c#ME" + dzVar.b + ": c#white";
                        break;
                    } else {
                        str = str + ":-mp c#MP" + dzVar.b + ": c#white";
                        break;
                    }
                case 4:
                    if (dzVar.b.equals(d.a().N)) {
                        str = str + ":-b c#ME" + dzVar.b + ": c#white";
                        break;
                    } else {
                        str = str + ":-b c#BG" + dzVar.b + ": c#white";
                        break;
                    }
                case 5:
                    if (dzVar.b.equals(d.a().N)) {
                        str = str + ":-n c#ME" + dzVar.b + ": c#white";
                        break;
                    } else {
                        str = str + ":-n c#NM" + dzVar.b + ": c#white";
                        break;
                    }
                case 6:
                    if (dzVar.b.equals(d.a().N)) {
                        str = str + ":-r c#ME" + dzVar.b + ": c#white";
                        break;
                    } else {
                        str = str + ":-r c#RG" + dzVar.b + ": c#white";
                        break;
                    }
                case 7:
                    if (dzVar.b.equals(d.a().N)) {
                        str = str + ":-ht c#ME" + dzVar.b + ": c#white";
                        break;
                    } else {
                        str = str + ":-ht c#HT" + dzVar.b + ": c#white";
                        break;
                    }
                case 8:
                    if (dzVar.b.equals(d.a().N)) {
                        str = str + ":-sv c#ME" + dzVar.b + ": c#white";
                        break;
                    } else if (dzVar.b.equals(com.donglh.narutoninjasaga.c.a.f0do)) {
                        str = str + ":-sv c#yellow" + dzVar.b + ": c#white";
                        break;
                    } else {
                        str = str + ":-sv c#SV" + dzVar.b + ": c#white";
                        break;
                    }
            }
            String str2 = str + dzVar.d;
            if (this.j != 1 || c.length() <= 0 || c.equals(dzVar.c) || c.equals(dzVar.b)) {
                Vector a2 = kk.a(kk.b, str2, this.h.aM - 19);
                com.donglh.narutoninjasaga.e.aw.a(this.f, a2);
                for (int i2 = 0; i2 < a2.size(); i2++) {
                    if (((String) a2.elementAt(i2)).contains("c#item")) {
                        this.g.addElement(dzVar.f);
                    }
                }
            }
            if (this.j == 1) {
                if (c.equals(com.donglh.narutoninjasaga.c.a.dp) || dzVar.c.equals(c) || dzVar.b.equals(c)) {
                    dzVar.e = true;
                }
            } else {
                dzVar.e = true;
            }
            if (dzVar.c.length() > 0) {
                if (!vector.contains(dzVar.c) && !dzVar.c.equals(d.a().N)) {
                    vector.addElement(dzVar.c);
                    this.b.addElement(new hz(0, dzVar.c));
                }
                if (!vector.contains(dzVar.b) && !dzVar.b.equals(d.a().N)) {
                    vector.addElement(dzVar.b);
                    this.b.addElement(new hz(0, dzVar.b));
                }
            }
        }
        this.i.a(this.i.e, this.i.f, this.b.size());
        this.h.a(this.h.e, this.h.f, this.f.size());
        for (int i3 = 0; i3 < this.b.size(); i3++) {
            hz hzVar = (hz) this.b.elementAt(i3);
            int i4 = 0;
            for (int i5 = 0; i5 < e[this.j].a.size(); i5++) {
                dz dzVar2 = (dz) e[this.j].a.elementAt(i5);
                if (!dzVar2.e && ((i5 == 0 && dzVar2.c.length() == 0) || dzVar2.b.equals(hzVar.b) || dzVar2.c.equals(hzVar.b))) {
                    i4++;
                }
            }
            hzVar.a = i4;
        }
    }

    public final short e() {
        short s = 0;
        for (int i = 0; i < e[this.j].a.size(); i++) {
            if (!((dz) e[this.j].a.elementAt(i)).e) {
                s = (short) (s + 1);
            }
        }
        return s;
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        boolean z;
        try {
            if (!this.o) {
                int i = lVar.a;
                int i2 = lVar.b;
                int i3 = -1;
                if (this.s > 0) {
                    this.s--;
                }
                if (!(this.l instanceof hj)) {
                    for (int i4 = 0; i4 < this.f.size(); i4++) {
                        String str = (String) this.f.elementAt(i4);
                        if (this.s == 0 && str.contains("c#select")) {
                            this.f.set(i4, str.replaceAll("c#select", ""));
                        }
                    }
                    int i5 = 1;
                    loop1: while (true) {
                        if (i5 >= e.length) {
                            z = false;
                            break;
                        }
                        for (int i6 = 0; i6 < e[i5].a.size(); i6++) {
                            if (!((dz) e[i5].a.elementAt(i6)).e) {
                                z = true;
                                break loop1;
                            }
                        }
                        i5++;
                    }
                    if (z) {
                        com.donglh.narutoninjasaga.e.r.a(lVar, 713, 0, -2, -4, 3);
                    }
                    a(lVar, this.h);
                    int size = this.f.size() - 2;
                    int i7 = size;
                    if (size < 0) {
                        i7 = 0;
                    }
                    for (int size2 = this.f.size() - 1; size2 >= i7; size2--) {
                        String str2 = (String) this.f.elementAt(size2);
                        if (this.h.b(size2)) {
                            i3 = kk.c(kk.b, lVar, str2, 5, (this.t / 2) + (size2 * this.t), 20, i3, -16777216);
                        }
                    }
                    b(lVar);
                } else {
                    a(lVar, 0, -3, this.aM, this.aN + 6, -22, 84, 5, 1, 1);
                    a(lVar, this.h);
                    for (int i8 = 0; i8 < this.f.size(); i8++) {
                        String str3 = (String) this.f.elementAt(i8);
                        if (this.h.b(i8)) {
                            i3 = kk.c(kk.b, lVar, str3, 5, 8 + (i8 << 4), 20, i3, -16777216);
                        }
                        if (this.s == 0 && str3.contains("c#select")) {
                            this.f.set(i8, str3.replaceAll("c#select", ""));
                        }
                    }
                    b(lVar);
                    this.h.d(lVar, (-i) - 15, (-i2) - 8);
                }
                if (this.j == 1) {
                    a(lVar, i, i2);
                    a.a(lVar, 646, -90, this.i.aY - 2, this.i.aZ - 2, 20, this.aN + 4);
                    lVar.e(13136426);
                    lVar.c(this.i.aY + 1, this.i.aZ - 2, this.i.aM - 3, this.i.aN + 3);
                    a(lVar, this.i);
                    for (int i9 = 0; i9 < this.b.size(); i9++) {
                        if (this.i.b(i9)) {
                            hz hzVar = (hz) this.b.elementAt(i9);
                            String str4 = hzVar.b;
                            int i10 = 5;
                            int i11 = 8 + (i9 << 4);
                            if (i9 == this.i.j && !this.i.m) {
                                i10 = 5 + 1;
                                i11++;
                            }
                            if (str4.equals(c)) {
                                kk.c(kk.d, lVar, str4 + (hzVar.a > 0 ? " (" + hzVar.a + ")" : ""), i10, i11, 20, -2560, -10275328);
                            } else {
                                kk.c(kk.d, lVar, str4 + (hzVar.a > 0 ? " (" + hzVar.a + ")" : ""), i10, i11, 20, -6488, -10275328);
                            }
                            if (i9 == this.i.j && this.i.m) {
                                lVar.d();
                                com.donglh.narutoninjasaga.e.r.a(lVar, 288, 0, (this.i.aM - 5) + 1, 8 + (i9 << 4) + 1, 10);
                                lVar.e();
                            } else {
                                com.donglh.narutoninjasaga.e.r.a(lVar, 288, 0, this.i.aM - 5, 8 + (i9 << 4), 10);
                            }
                        }
                    }
                    b(lVar);
                    a(lVar, i, i2);
                }
            }
        } catch (Exception e2) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) this);
        }
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void b() {
        this.i.a();
        this.h.a();
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        String str;
        Vector vector = new Vector();
        if (this.o) {
            return vector;
        }
        if (!(this.l instanceof hj)) {
            vector.add(new gu(5011, 0, 0, this.aM, this.aN, null, this));
            return vector;
        }
        for (int i = 0; i < this.b.size(); i++) {
            if (this.i.b(i)) {
                gu guVar = new gu(5009, (this.i.aY + this.i.aM) - 18, (i << 4) - this.i.d, this.i.aY + this.i.aM, ((i << 4) + 16) - this.i.d, this.i, this, new hz(0, ((hz) this.b.elementAt(i)).b));
                if (com.donglh.narutoninjasaga.e.aw.a((int) this.i.aY, (int) this.i.aZ, this.i.aY + this.i.aM, this.i.aZ + this.i.aN, guVar.a(), guVar.b())) {
                    vector.addElement(guVar);
                }
            }
        }
        int i2 = -1;
        for (int i3 = 0; i3 < this.f.size(); i3++) {
            if (((String) this.f.elementAt(i3)).contains(":-")) {
                String str2 = "c#" + str.split(": ")[0].split(" c#")[1];
                String substring = str2.substring(eo.b(str2).d.length(), str2.length());
                if (!substring.equals(com.donglh.narutoninjasaga.c.a.dq) && !substring.equals(com.donglh.narutoninjasaga.c.a.f0do) && !substring.equals(com.donglh.narutoninjasaga.c.a.dr) && !substring.equals(d.a().N)) {
                    gu guVar2 = new gu(5008, 20, (i3 << 4) - this.h.d, 23 + kk.b(kk.c, substring), ((i3 << 4) + 16) - this.h.d, this.h, this, new hz(0, substring));
                    if (this.h.b(i3) && com.donglh.narutoninjasaga.e.aw.a((int) this.h.aY, (int) this.h.aZ, this.h.aY + this.h.aM, this.h.aZ + this.h.aN, guVar2.a(), guVar2.b())) {
                        vector.addElement(guVar2);
                    }
                }
            }
            String str3 = (String) this.f.elementAt(i3);
            if (str3.contains("c#item")) {
                i2++;
                String[] split = str3.split("c#item");
                int c2 = this.h.aY + 3 + kk.c(kk.c, split[0]);
                int i4 = (this.h.aZ + (i3 * this.h.f)) - this.h.d;
                String str4 = split[1];
                String str5 = str4;
                gj d2 = eo.d(str4);
                if (d2 != null) {
                    str5 = str5.split(d2.d)[0].trim();
                }
                gu guVar3 = new gu(5010, c2, i4, c2 + kk.c(kk.c, str5) + 2, i4 + this.h.f, this.h, this, new hz(i2, str5));
                if (this.h.b(i3) && com.donglh.narutoninjasaga.e.aw.a((int) this.h.aY, (int) this.h.aZ, this.h.aY + this.h.aM, this.h.aZ + this.h.aN, guVar3.a(), guVar3.b())) {
                    vector.addElement(guVar3);
                }
            }
        }
        vector.addElement(this.i.a(5007, this));
        vector.addElement(this.h.a(this.k, this));
        return vector;
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        if (!this.o) {
            switch (guVar.b) {
                case 2001:
                    String str = c;
                    int i3 = 0;
                    while (i3 < this.b.size() && !((hz) this.b.elementAt(i3)).b.equals(str)) {
                        i3++;
                    }
                    int i4 = i3 - 1;
                    if (i4 < 0) {
                        i4 = 0;
                    }
                    for (int size = e[this.j].a.size() - 1; size >= 0; size--) {
                        dz dzVar = (dz) e[this.j].a.elementAt(size);
                        if (dzVar.b.equals(str) || dzVar.c.equals(str)) {
                            e[this.j].a.remove(dzVar);
                        }
                    }
                    a(i4);
                    return;
                case 5007:
                    if (guVar.j.i >= 0) {
                        a(guVar.j.i);
                        return;
                    }
                    return;
                case 5008:
                    if (guVar.j.i >= 0) {
                        int i5 = this.aY + i;
                        int i6 = this.aZ + i2;
                        hz hzVar = (hz) guVar.k;
                        int i7 = guVar.j.i;
                        this.f.set(i7, ((String) this.f.elementAt(i7)).replaceAll(hzVar.b, "c#select" + hzVar.b));
                        this.s = 6;
                        d = hzVar;
                        Vector vector = new Vector();
                        if (!com.donglh.narutoninjasaga.e.n.n().j(hzVar.b)) {
                            vector.addElement(new hz(6004, com.donglh.narutoninjasaga.c.a.du));
                        }
                        if (com.donglh.narutoninjasaga.e.n.n().ay != null && !com.donglh.narutoninjasaga.e.n.n().ay.b()) {
                            if (com.donglh.narutoninjasaga.e.n.n().ay.a() && !com.donglh.narutoninjasaga.e.n.n().ay.a(hzVar.b)) {
                                vector.addElement(new hz(6008, com.donglh.narutoninjasaga.c.a.dw));
                            }
                        } else {
                            vector.addElement(new hz(6008, com.donglh.narutoninjasaga.c.a.dv));
                        }
                        vector.addElement(new hz(6005, com.donglh.narutoninjasaga.c.a.dx));
                        vector.addElement(new hz(6006, com.donglh.narutoninjasaga.c.a.cc));
                        vector.addElement(new hz(6007, com.donglh.narutoninjasaga.c.a.cS));
                        if (vector.size() > 0) {
                            String[] strArr = new String[vector.size()];
                            short[] sArr = new short[vector.size()];
                            for (int i8 = 0; i8 < vector.size(); i8++) {
                                hz hzVar2 = (hz) vector.elementAt(i8);
                                strArr[i8] = hzVar2.b;
                                sArr[i8] = (short) hzVar2.a;
                            }
                            this.l.n = a(this.l, i5 + 25, i6, new gz(0, sArr, strArr));
                            return;
                        }
                        return;
                    }
                    return;
                case 5009:
                    a(((hz) guVar.k).b);
                    com.donglh.narutoninjasaga.e.f.c().am.a(com.donglh.narutoninjasaga.c.a.ds + ((hz) guVar.k).b + com.donglh.narutoninjasaga.c.a.dt, 2001, this);
                    return;
                case 5010:
                    if (guVar.j.i >= 0) {
                        a(this.aY + i, this.aZ + i2, guVar.j.i, (hz) guVar.k);
                        return;
                    }
                    return;
                case 5011:
                    db.a().h();
                    com.donglh.narutoninjasaga.e.n n = com.donglh.narutoninjasaga.e.n.n();
                    n.a((cn) new hk(n, n.ad.e()));
                    return;
                default:
                    return;
            }
        }
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x017d: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:35:0x017b */
    private void a(int i, int i2, int i3, hz hzVar) {
        Exception a2;
        iz izVar;
        try {
            ew ewVar = (ew) this.g.elementAt(hzVar.a);
            if (ewVar != null) {
                cn cnVar = this.l;
                cn cnVar2 = this.l;
                if (ewVar == null) {
                    izVar = null;
                } else {
                    iu iuVar = new iu(i, i2, cnVar2, ewVar);
                    if (iuVar.aY > (com.donglh.narutoninjasaga.e.f.c().o - iuVar.aM) - cnVar2.aY) {
                        iuVar.aY = (short) ((com.donglh.narutoninjasaga.e.f.c().o - iuVar.aM) - cnVar2.aY);
                    }
                    if (iuVar.aY < i) {
                        iuVar.aY = (short) (i - (iuVar.aM + 3));
                    }
                    if (iuVar.aY < (-cnVar2.aY)) {
                        iuVar.aY = (short) (-cnVar2.aY);
                    }
                    if (iuVar.aZ > (com.donglh.narutoninjasaga.e.f.c().p - iuVar.aN) - cnVar2.aZ) {
                        iuVar.aZ = (short) ((com.donglh.narutoninjasaga.e.f.c().p - iuVar.aN) - cnVar2.aZ);
                    }
                    iz izVar2 = new iz(i - 32, i2 + 32, iuVar);
                    cnVar2.a(izVar2);
                    izVar = izVar2;
                }
                cnVar.n = izVar;
                int i4 = -1;
                for (int i5 = 0; i5 < this.f.size(); i5++) {
                    String str = (String) this.f.elementAt(i5);
                    if (str.contains("c#item")) {
                        i4++;
                        if (i4 == hzVar.a) {
                            this.s = 6;
                            this.f.set(i5, str.replaceAll(hzVar.b, "c#select" + hzVar.b));
                            return;
                        }
                    }
                }
            } else if (!hzVar.b.trim().equalsIgnoreCase(d.a().N.trim())) {
                b(i, i2, i3, hzVar);
            }
        } catch (Exception e2) {
            com.donglh.narutoninjasaga.e.aw.a(a2);
        }
    }

    private void b(int i, int i2, int i3, hz hzVar) {
        this.f.set(i3, ((String) this.f.elementAt(i3)).replaceAll(hzVar.b, "c#select" + hzVar.b));
        this.s = 6;
        d = hzVar;
        Vector vector = new Vector();
        vector.addElement(new hz(6007, com.donglh.narutoninjasaga.c.a.cS));
        if (vector.size() > 0) {
            String[] strArr = new String[vector.size()];
            short[] sArr = new short[vector.size()];
            for (int i4 = 0; i4 < vector.size(); i4++) {
                hz hzVar2 = (hz) vector.elementAt(i4);
                strArr[i4] = hzVar2.b;
                sArr[i4] = (short) hzVar2.a;
            }
            this.l.n = a(this.l, i + 25, i2, new gz(0, sArr, strArr));
        }
    }

    public final void h() {
        this.h.h = this.h.g;
        this.h.d();
        fw fwVar = this.h;
        fwVar.a = fwVar.h * fwVar.f;
        if (fwVar.a < fwVar.b) {
            fwVar.a = fwVar.b;
        }
        if (fwVar.a > fwVar.c) {
            fwVar.a = fwVar.c;
        }
        fwVar.d = fwVar.a;
    }

    private void a(int i) {
        try {
            c = ((hz) this.b.elementAt(i)).b;
        } catch (Exception unused) {
            c = "";
        }
        a();
        if (this.l instanceof hj) {
            hj hjVar = (hj) this.l;
            hjVar.c.b.d[hjVar.c.b.b] = e();
            if (hjVar.e.a) {
                h();
            }
        }
    }

    public final void a(String str) {
        c = str;
        a();
        if (this.l instanceof hj) {
            hj hjVar = (hj) this.l;
            hjVar.c.b.d[hjVar.c.b.b] = e();
            if (hjVar.e.a) {
                h();
            }
        }
    }
}
