package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.Input;
import com.badlogic.gdx.net.HttpStatus;
import java.util.Vector;
/* compiled from: LangLa_ar.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/am.class */
public final class am extends he {
    public q[] a;
    public q[] b;
    private fx[] g;
    private dg h;
    private dg B;
    public int c;
    private long C;
    private long D;
    private long E;
    private int F;
    private boolean G;
    private boolean H;
    public int d;
    public int e;
    public gt f;
    private cd I;
    private Vector J;

    public am(com.donglh.narutoninjasaga.e.ai aiVar) {
        super(aiVar, new String[]{com.donglh.narutoninjasaga.c.a.rp[1], com.donglh.narutoninjasaga.c.a.F});
        this.a = new q[3];
        this.b = new q[16];
        this.g = new fx[4];
        this.C = 0L;
        this.D = 0L;
        this.E = 0L;
        this.F = 0;
        this.G = false;
        this.d = 0;
        this.e = 0;
        this.g[0] = new fx((byte) 1, 162, a_() + 20, 30, 30, 30, 1, 1);
        this.g[1] = new fx((byte) 1, 242, a_() + 20, 30, 30, 30, 1, 1);
        this.g[2] = new fx((byte) 1, HttpStatus.SC_ACCEPTED, a_() + 65, 30, 30, 30, 1, 1);
        this.g[3] = new fx((byte) 1, 14, a_() + 20, 128, 128, 32, 4, 4);
        this.h = a(this.aM - 70, this.aN - 33, com.donglh.narutoninjasaga.c.a.rp[1], this, 0, -8);
        a(this.h, 0);
        this.B = a(Input.Keys.BUTTON_SELECT, this.aN - 33, com.donglh.narutoninjasaga.c.a.jY, this, 2002, -8);
        a(this.B, 0);
        gz gzVar = new gz(2000, com.donglh.narutoninjasaga.c.a.rQ);
        int length = gzVar.c.length;
        this.I = a(14, this.aN - 32, 90, com.donglh.narutoninjasaga.e.f.c().p <= 240 ? length - 3 : length, gzVar, this, 1);
        this.I.a(com.donglh.narutoninjasaga.e.ay.a().b(7));
        a(this.I, 0);
    }

    @Override // com.donglh.narutoninjasaga.d.et
    public final void a() {
        this.J = new Vector();
        this.A = new q[d.a().W.length];
        int i = 0;
        for (int i2 = 0; i2 < d.a().W.length; i2++) {
            q qVar = d.a().W[i2];
            if (qVar != null && qVar.t()) {
                this.A[i] = qVar;
                i++;
            }
            if (qVar != null && qVar.h().f == 21) {
                this.J.add(qVar);
            }
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void b() {
        short[] sArr;
        q qVar;
        int[] iArr;
        try {
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) this);
            return;
        }
        if (com.donglh.narutoninjasaga.e.n.n().aC == 9 && com.donglh.narutoninjasaga.e.n.n().aD == 10) {
            if (this.a[0] == null) {
                d a = d.a();
                int length = d.a().Y.length - 1;
                while (true) {
                    if (length < 0) {
                        qVar = null;
                        break;
                    } else if (a.Y[length] != null && a.Y[length].h().f == 1) {
                        qVar = a.Y[length];
                        break;
                    } else {
                        length--;
                    }
                }
                q qVar2 = qVar;
                if (qVar != null && qVar2.p > 0) {
                    if (this.i.b != q()) {
                        com.donglh.narutoninjasaga.e.f.c().aE.a(this.aY + Input.Keys.F2, this.aZ + 15);
                    } else {
                        int i = 0;
                        while (true) {
                            if (i > this.A.length - 1) {
                                iArr = null;
                                break;
                            } else if (this.A[i] != null && this.A[i].h().f == 1 && this.A[i].p == 0) {
                                iArr = new int[]{(i % 9) << 5, (i / 9) << 5};
                                break;
                            } else {
                                i--;
                            }
                        }
                        int[] iArr2 = iArr;
                        if (this.n == null) {
                            com.donglh.narutoninjasaga.e.f.c().aE.a(this.aY + iArr2[0] + 20, this.aZ + 30 + iArr2[1] + 15);
                        }
                    }
                } else if (this.i.b != 1) {
                    com.donglh.narutoninjasaga.e.f.c().aE.a(this.aY + 170, this.aZ + 15);
                } else if (this.n == null) {
                    com.donglh.narutoninjasaga.e.f.c().aE.a(this.aY + 270, this.aZ + 50);
                }
                com.donglh.narutoninjasaga.e.aw.a((Exception) this);
                return;
            } else if (this.J.size() == 0 || this.E >= 100) {
                if (this.i.b != 0) {
                    com.donglh.narutoninjasaga.e.f.c().aE.a(this.aY + 85, this.aZ + 15);
                } else {
                    com.donglh.narutoninjasaga.e.f.c().aE.a(this.aY + 255, (this.aZ + this.aN) - 15);
                }
            } else if (this.i.b != q()) {
                com.donglh.narutoninjasaga.e.f.c().aE.a(this.aY + Input.Keys.F2, this.aZ + 15);
            } else if (this.n == null) {
                if (this.J.size() == 0) {
                    sArr = null;
                } else {
                    q qVar3 = (q) this.J.elementAt(0);
                    short[] sArr2 = null;
                    int i2 = 0;
                    while (true) {
                        if (i2 < this.A.length) {
                            if (!this.A[i2].equals(qVar3)) {
                                i2++;
                            } else {
                                sArr2 = new int[]{(i2 % 9) << 5, (i2 / 9) << 5};
                                this.J.removeElementAt(0);
                                break;
                            }
                        } else {
                            break;
                        }
                    }
                    sArr = sArr2;
                }
                short[] sArr3 = sArr;
                e();
                if (sArr3 == null && this.E < 100) {
                    com.donglh.narutoninjasaga.e.f.c().aE.a(this.aY + 85, this.aZ + 10);
                    com.donglh.narutoninjasaga.e.f.c().aE.a = true;
                } else if (this.E < 100) {
                    com.donglh.narutoninjasaga.e.f.c().aE.a(this.aY + sArr3[0] + 15, this.aZ + 25 + sArr3[1] + 15);
                } else {
                    com.donglh.narutoninjasaga.e.f.c().aE.a(this.aY + 85, this.aZ + 10);
                }
            }
            com.donglh.narutoninjasaga.e.f.c().aE.a = true;
        }
        super.b();
        if (this.i.b == 0) {
            e();
            for (int i3 = 0; i3 < this.g.length; i3++) {
                this.g[i3].a();
            }
        } else {
            a();
        }
        if (this.f != null) {
            this.f.b();
            if (this.f.h()) {
                this.f = null;
            }
        }
        if (this.d > 0) {
            this.d--;
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r3v0, types: [com.donglh.narutoninjasaga.d.am] */
    private void e() {
        this.G = true;
        this.F = 0;
        ?? r3 = 0;
        this.E = 0L;
        this.D = 0L;
        r3.C = this;
        for (int i = 0; i < this.b.length; i++) {
            if (this.b[i] != null) {
                this.C += com.donglh.narutoninjasaga.e.f.c().av[this.b[i].c];
            }
        }
        if (this.a[0] != null) {
            this.a[1] = this.a[0].a();
            this.a[1].q = true;
            int i2 = this.a[1].p + 1;
            int i3 = i2;
            if (i2 > 19) {
                i3 = 19;
            }
            this.a[1].a(i3);
            if (this.a[0].k() && this.a[1].p <= 19) {
                this.F = com.donglh.narutoninjasaga.e.f.c().ar[this.a[1].p];
                this.D = com.donglh.narutoninjasaga.e.f.c().aw[this.a[1].p];
            } else if (this.a[0].l() && this.a[1].p <= 19) {
                this.F = com.donglh.narutoninjasaga.e.f.c().as[this.a[1].p];
                this.D = com.donglh.narutoninjasaga.e.f.c().ax[this.a[1].p];
            } else if (this.a[0].m() && this.a[1].p <= 19) {
                this.F = com.donglh.narutoninjasaga.e.f.c().at[this.a[1].p];
                this.D = com.donglh.narutoninjasaga.e.f.c().ay[this.a[1].p];
            }
            if (d.a().B + d.a().C < this.F) {
                this.G = false;
            }
            if (this.D > 0) {
                this.E = (this.C * 100) / this.D;
            }
            if (!this.a[0].u()) {
                this.a[1] = null;
                this.G = false;
                return;
            }
            return;
        }
        this.a[1] = null;
    }

    /* JADX WARN: Type inference failed for: r0v23, types: [com.donglh.narutoninjasaga.d.gt, java.lang.Exception] */
    @Override // com.donglh.narutoninjasaga.d.he, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        ?? r0;
        try {
            if (q() <= 0) {
                a(lVar, (int) this.aY, (int) this.aZ, (int) this.aM, (int) this.aN, com.donglh.narutoninjasaga.c.a.o, (byte) 2, false);
            } else {
                super.a(lVar);
            }
            if (this.i.b == 0) {
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.aL, 14, a_() + 9, 0, -10831436, -16777216);
                a(lVar, this.g[0].aY, this.g[0].aZ, this.a[0], this.g[0].i >= 0, com.donglh.narutoninjasaga.c.a.F);
                a(lVar, this.g[1].aY, this.g[1].aZ, this.a[1], this.g[1].i >= 0, com.donglh.narutoninjasaga.c.a.G);
                a(lVar, this.g[2].aY, this.g[2].aZ, this.a[2], this.g[2].i >= 0, com.donglh.narutoninjasaga.c.a.be);
                com.donglh.narutoninjasaga.e.r.a(lVar, 95, 0, 210 - ((com.donglh.narutoninjasaga.e.f.c().i / 3) % 3), a_() + 25, 20);
                if (this.a[0] != null) {
                    kk.b(kk.c, lVar, "(+" + ((int) this.a[0].p) + ")", 169, a_() + 60, 33, -1, -16777216);
                    if (this.a[1] != null) {
                        kk.b(kk.c, lVar, "(+" + ((int) this.a[1].p) + ")", Input.Keys.F6, a_() + 60, 33, -1, -16777216);
                    }
                    if (!this.G) {
                        if (this.d == 0 || this.d % 10 > 5) {
                            if (this.a[0].u()) {
                                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.jC + this.F + com.donglh.narutoninjasaga.c.a.jB, Input.Keys.NUMPAD_5, a_() + Input.Keys.BUTTON_START, 0, -65536, -16777216);
                            } else {
                                kk.c(kk.c, lVar, com.donglh.narutoninjasaga.c.a.jD, Input.Keys.NUMPAD_5, a_() + Input.Keys.BUTTON_START, 0, -2560, -16777216);
                            }
                        }
                    } else if (this.d > 0) {
                        if (this.d % 14 > 7) {
                            if (this.e == 1) {
                                kk.c(kk.c, lVar, com.donglh.narutoninjasaga.c.a.jE, Input.Keys.NUMPAD_5, a_() + Input.Keys.BUTTON_START, 0, -2560, -16777216);
                                kk.c(kk.c, lVar, com.donglh.narutoninjasaga.c.a.jF, Input.Keys.NUMPAD_5, a_() + 124, 0, -2560, -16777216);
                            } else if (this.e == 2) {
                                kk.c(kk.c, lVar, com.donglh.narutoninjasaga.c.a.jG, Input.Keys.NUMPAD_5, a_() + Input.Keys.BUTTON_START, 0, -1, -16777216);
                                kk.c(kk.c, lVar, com.donglh.narutoninjasaga.c.a.jI, Input.Keys.NUMPAD_5, a_() + 124, 0, -1, -16777216);
                            } else if (this.e == 3) {
                                kk.c(kk.c, lVar, com.donglh.narutoninjasaga.c.a.jG, Input.Keys.NUMPAD_5, a_() + Input.Keys.BUTTON_START, 0, -1, -16777216);
                                kk.c(kk.c, lVar, com.donglh.narutoninjasaga.c.a.jJ, Input.Keys.NUMPAD_5, a_() + 124, 0, -1, -16777216);
                            }
                        }
                    } else if (!this.a[0].u()) {
                        kk.c(kk.c, lVar, com.donglh.narutoninjasaga.c.a.jD, Input.Keys.NUMPAD_5, a_() + Input.Keys.BUTTON_START, 0, -2560, -16777216);
                    } else {
                        long j = this.E;
                        int i = 99;
                        if (this.a[2] != null) {
                            j += 3;
                            i = 100;
                        }
                        if (j > 100) {
                            j = 100;
                        }
                        if (this.a[1].p > 10 && j >= 100) {
                            kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.aM + " " + i + " %", Input.Keys.NUMPAD_5, a_() + Input.Keys.BUTTON_START, 0, -30976, -16777216);
                        } else {
                            kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.aM + " " + j + " %", Input.Keys.NUMPAD_5, a_() + Input.Keys.BUTTON_START, 0, -30976, -16777216);
                        }
                        if (this.F <= d.a().C) {
                            kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.aN + " 0", Input.Keys.NUMPAD_5, a_() + 124, 0, -30976, -16777216);
                            kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.aO + " " + com.donglh.narutoninjasaga.e.aw.c(this.F), Input.Keys.NUMPAD_5, a_() + 140, 0, -30976, -16777216);
                        } else {
                            kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.aN + " " + com.donglh.narutoninjasaga.e.aw.c(this.F - d.a().C), Input.Keys.NUMPAD_5, a_() + 124, 0, -30976, -16777216);
                            kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.aO + " " + com.donglh.narutoninjasaga.e.aw.c(d.a().C), Input.Keys.NUMPAD_5, a_() + 140, 0, -30976, -16777216);
                        }
                    }
                }
                a(lVar, this.g[3]);
                for (int i2 = 0; i2 < this.g[3].g; i2++) {
                    for (int i3 = 0; i3 < this.g[3].o; i3++) {
                        b(lVar, i3 * this.g[3].f, i2 * this.g[3].f, this.b[(i2 * this.g[3].o) + i3], (i2 * this.g[3].g) + i3 == this.g[3].i, "");
                    }
                }
                b(lVar);
                if (this.f != null) {
                    r0 = this.f;
                    r0.b(lVar, this.g[0].aY + (this.g[0].f / 2), this.g[0].aZ + (this.g[0].f / 2));
                }
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
        }
    }

    @Override // com.donglh.narutoninjasaga.d.he, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        Vector c = super.c();
        if (this.i.b == 0) {
            for (int i = 0; i < this.g.length; i++) {
                c.addElement(this.g[i].a(i + 1001, this));
            }
        }
        return c;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v32 */
    /* JADX WARN: Type inference failed for: r0v33 */
    /* JADX WARN: Type inference failed for: r0v34, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v35 */
    /* JADX WARN: Type inference failed for: r0v69 */
    @Override // com.donglh.narutoninjasaga.d.he, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        if (this.i.b == q() && guVar.b == 0) {
            d();
        }
        super.a(guVar, i, i2);
        if (this.i.b == 0) {
            switch (guVar.b) {
                case 0:
                    a(true);
                    return;
                case 1001:
                    this.c = 1;
                    a(guVar.j, guVar.j.i);
                    if (this.a[0] == null) {
                        a(2);
                        com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.mg, -1);
                        return;
                    }
                    this.n = a(guVar, this, this.a[0]);
                    return;
                case 1002:
                    this.c = 2;
                    a(guVar.j, guVar.j.i);
                    this.n = a(guVar, this, this.a[1]);
                    return;
                case 1003:
                    this.c = 3;
                    a(guVar.j, guVar.j.i);
                    if (this.a[2] == null) {
                        this.n = b(com.donglh.narutoninjasaga.c.a.oG, this, guVar.j.aY - 124, guVar.j.aZ, 100);
                        return;
                    } else {
                        this.n = a(guVar, this, this.a[2]);
                        return;
                    }
                case 1004:
                    this.c = 4;
                    a(guVar.j, guVar.j.i);
                    this.n = a(guVar, this, this.b[guVar.j.i]);
                    return;
                case 2001:
                    a(false);
                    return;
                case 2002:
                    int i3 = this.I.b.b;
                    int i4 = 0;
                    for (int i5 = 0; i5 < d.a().W.length && i4 < 16; i5++) {
                        if (d.a().W[i5] != null && d.a().W[i5].q() && d.a().W[i5].h().a <= i3) {
                            q qVar = d.a().W[i5];
                            ?? r0 = 0;
                            int i6 = 0;
                            while (true) {
                                try {
                                    r0 = i6;
                                    if (r0 < this.b.length) {
                                        q qVar2 = this.b[i6];
                                        if (qVar2 != null) {
                                            i6++;
                                            r0 = qVar2;
                                        } else {
                                            this.b[i6] = qVar;
                                        }
                                    }
                                } catch (Exception e) {
                                    com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                                }
                            }
                            d.a().W[qVar.e] = null;
                            i4++;
                        }
                    }
                    b();
                    return;
                default:
                    return;
            }
        }
    }

    private void a(fw fwVar, int i) {
        for (int i2 = 0; i2 < this.g.length; i2++) {
            this.g[i2].i = -1;
        }
        fwVar.i = i;
    }

    @Override // com.donglh.narutoninjasaga.d.el
    public final void a(int i) {
        super.a(i);
        if (this.a != null && this.a[0] != null && !this.a[0].u()) {
            d.a().g(this.a[0].r)[this.a[0].e] = this.a[0];
            this.a[0] = null;
        }
    }

    @Override // com.donglh.narutoninjasaga.d.ej
    public final void d() {
        super.d();
        this.H = false;
        if (this.a[0] != null) {
            d.a().g(this.a[0].r)[this.a[0].e] = this.a[0];
            this.a[0] = null;
        }
        if (this.a[2] != null) {
            d.a().W[this.a[2].e] = this.a[2];
            this.a[2] = null;
        }
        for (int i = 0; i < this.b.length; i++) {
            if (this.b[i] != null) {
                d.a().W[this.b[i].e] = this.b[i];
                this.b[i] = null;
            }
        }
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x0118: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:42:0x0117 */
    private void a(boolean z) {
        Exception a;
        try {
            if (!this.G) {
                this.d = 50;
            } else if (this.a[0] == null) {
                com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.jK, -65536);
            } else if (this.E < 10) {
                com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.jL, -65536);
            } else {
                if (z) {
                    if (this.E > 120) {
                        com.donglh.narutoninjasaga.e.f.c().am.a(com.donglh.narutoninjasaga.c.a.jM, 2001, this);
                        return;
                    } else if (this.F > d.a().C) {
                        com.donglh.narutoninjasaga.e.f.c().am.a(com.donglh.narutoninjasaga.c.a.jN, 2001, this);
                        return;
                    }
                }
                com.donglh.narutoninjasaga.e.ak akVar = new com.donglh.narutoninjasaga.e.ak((byte) 107);
                akVar.a(this.a[0].r);
                akVar.b(this.a[0].e);
                if (this.a[2] != null) {
                    akVar.b(this.a[2].e);
                } else {
                    akVar.b(-1);
                }
                int i = 0;
                for (int i2 = 0; i2 < this.b.length; i2++) {
                    if (this.b[i2] != null) {
                        i++;
                    }
                }
                akVar.a(i);
                for (int i3 = 0; i3 < this.b.length; i3++) {
                    if (this.b[i3] != null) {
                        akVar.b(this.b[i3].e);
                    }
                }
                akVar.l();
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a);
        }
    }
}
