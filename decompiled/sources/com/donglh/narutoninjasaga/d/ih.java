package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.Input;
import java.util.Vector;
/* compiled from: LangLa_lv.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/ih.class */
public final class ih extends em {
    private kn b;
    private q c;
    private byte d;
    private fx e;
    private long f;
    private boolean g = true;
    private String h = "";
    private int i = 0;
    public byte a;
    private boolean v;

    public ih(com.donglh.narutoninjasaga.e.ai aiVar, q qVar, byte b) {
        this.a = b;
        this.j = (byte) 2;
        this.s = aiVar;
        this.c = qVar;
        this.u = false;
        if (qVar.k > 0) {
            this.d = (byte) 2;
        } else if (qVar.l > 0) {
            this.d = (byte) 3;
        } else if (qVar.i > 0) {
            this.d = (byte) 0;
        } else if (qVar.j > 0) {
            this.d = (byte) 1;
        } else if (qVar.m > 0) {
            this.d = (byte) 4;
        }
        d(220, 155);
        this.b = a(54, 43, 60, "", this, 2);
        this.b.a = 4;
        this.b.j = 1;
        this.b.k = 999;
        this.b.a("1");
        a(this.b);
        dg a = a(119, 46, "", this, 1001, 58);
        dg a2 = a(140, 46, "", this, 1002, 57);
        dg a3 = a(com.donglh.narutoninjasaga.c.a.e, 1003);
        a3.a(-8);
        a3.g(a3.aY - 26, a3.aZ);
        dg a4 = a(com.donglh.narutoninjasaga.c.a.t, -7);
        a4.a(-8);
        a4.g(a4.aY + 40, a4.aZ);
        this.e = new fx((byte) 1, 9, 9, 30, 30, 30, 1, 1);
        if (b == 40) {
            a.b(true);
            a2.b(true);
            this.b.a(true);
        }
    }

    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.cn
    public final void a(int i, Object obj) {
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a_(int i) {
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a(int i, Object obj, co coVar) {
    }

    @Override // com.donglh.narutoninjasaga.d.ej
    public final int a_() {
        return 5;
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        a(lVar, 0, 0, this.aM, this.aN, 80, 55, 56);
        b(lVar, this.e.aY, this.e.aZ, this.c, this.v);
        kk.b(kk.c, lVar, this.c.h().b, 44, 15, 0, -1, -16777216);
        kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.ah, 44, 30, 0, -10831436, -16777216);
        kk.b(kk.c, lVar, new StringBuilder().append(this.c.d()).toString(), 64, 30, 0, -1, -16777216);
        a(lVar, 64, 30, new StringBuilder().append(this.c.d()).toString(), this.d);
        kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.al, 9, 55, 0, -10831436, -16777216);
        kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.am, 9, 75, 0, -10831436, -16777216);
        kk.b(kk.c, lVar, new StringBuilder().append(this.f).toString(), 54, 75, 0, -1, -16777216);
        a(lVar, 54, 75, new StringBuilder().append(this.f).toString(), this.d);
        kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.an, 9, 95, 0, -10831436, -16777216);
        kk.b(kk.c, lVar, this.h, 54, 95, 0, -2560, -16777216);
        a(lVar, 54, 95, this.h, this.d);
        if (this.g) {
            return;
        }
        if (this.i == 0 || this.i % 10 > 5) {
            kk kkVar = kk.c;
            StringBuilder append = new StringBuilder().append(com.donglh.narutoninjasaga.c.a.aB).append(" ");
            q qVar = this.c;
            kk.b(kkVar, lVar, append.append(qVar.k > 0 ? com.donglh.narutoninjasaga.c.a.rk[0] : qVar.l > 0 ? com.donglh.narutoninjasaga.c.a.rk[1] : qVar.i > 0 ? com.donglh.narutoninjasaga.c.a.rk[2] : qVar.j > 0 ? com.donglh.narutoninjasaga.c.a.rk[3] : qVar.m > 0 ? com.donglh.narutoninjasaga.c.a.rk[4] : "").toString(), 9, 113, 0, -65536, -16777216);
        }
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void b() {
        this.g = true;
        this.f = this.b.j() * this.c.d();
        if (this.c.k > 0) {
            this.h = new StringBuilder().append(d.a().E).toString();
            if (d.a().E < this.f) {
                this.g = false;
            }
        } else if (this.c.l > 0) {
            this.h = new StringBuilder().append(d.a().D).toString();
            if (d.a().D < this.f) {
                this.g = false;
            }
        } else if (this.c.i > 0) {
            this.h = new StringBuilder().append(d.a().B).toString();
            if (d.a().B < this.f) {
                this.g = false;
            }
        } else if (this.c.j > 0) {
            this.h = new StringBuilder().append(d.a().C).toString();
            if (d.a().C < this.f) {
                this.g = false;
            }
        } else if (this.c.m > 0) {
            this.h = new StringBuilder().append(com.donglh.narutoninjasaga.e.n.n().bj).toString();
            if (com.donglh.narutoninjasaga.e.n.n().bj < this.f) {
                this.g = false;
            }
        }
        if (this.i > 0) {
            this.i--;
        }
        if (com.donglh.narutoninjasaga.e.n.n().aC == 3 && com.donglh.narutoninjasaga.e.n.n().aD == 0) {
            if (this.f >= 3) {
                com.donglh.narutoninjasaga.e.f.c().aE.a(this.aY + 75, this.aZ + 140);
            } else {
                com.donglh.narutoninjasaga.e.f.c().aE.a(this.aY + Input.Keys.NUMPAD_6, this.aZ + 48);
            }
            com.donglh.narutoninjasaga.e.f.c().aE.a = true;
        }
    }

    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        super.c();
        Vector vector = new Vector();
        vector.addElement(this.e.a(1000, this));
        vector.addElement(new gu(0, 0, 0, this.aM, this.aN, this.t, this));
        return vector;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v12, types: [com.donglh.narutoninjasaga.e.ak] */
    /* JADX WARN: Type inference failed for: r0v8, types: [com.donglh.narutoninjasaga.d.ih] */
    /* JADX WARN: Type inference failed for: r0v9, types: [java.lang.Exception] */
    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        this.v = false;
        switch (guVar.b) {
            case 1000:
                this.v = true;
                this.n = a(this, 39, 10, 30, this.c);
                return;
            case 1001:
                this.b.a(this.b.j() - 1);
                if (this.b.j() < this.b.j) {
                    this.b.a(this.b.j);
                    return;
                }
                return;
            case 1002:
                this.b.a(this.b.j() + 1);
                return;
            case 1003:
                if (this.f < 1) {
                    return;
                }
                if (!this.g) {
                    this.i = 50;
                    return;
                }
                ?? r0 = this;
                try {
                    com.donglh.narutoninjasaga.e.ak akVar = new com.donglh.narutoninjasaga.e.ak((byte) 121);
                    akVar.b(r0.c.b);
                    akVar.b(r0.b.j());
                    r0 = akVar;
                    r0.l();
                    return;
                } catch (Exception e) {
                    com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                    return;
                }
            default:
                return;
        }
    }
}
