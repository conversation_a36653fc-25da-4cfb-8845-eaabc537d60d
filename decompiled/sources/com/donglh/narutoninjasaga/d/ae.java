package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.Input;
import java.util.Vector;
/* compiled from: LangLa_aj.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/ae.class */
public final class ae extends et {
    private fx B;
    private fx C;
    private dg D;
    private dg E;
    private dg F;
    public int a;
    public int b;
    public int c;
    public int d;
    private int G;
    public q[] e;
    public q[] f;
    private String H;
    public long g;
    public int h;

    public ae(com.donglh.narutoninjasaga.e.ai aiVar, String str) {
        super(aiVar, new String[]{com.donglh.narutoninjasaga.c.a.br});
        this.a = 0;
        this.b = 0;
        this.c = 0;
        this.d = 0;
        this.G = 32;
        this.e = new q[12];
        this.f = new q[12];
        this.H = "";
        this.g = com.donglh.narutoninjasaga.e.aw.a() + 6000;
        this.H = str;
        this.D = a((this.aM / 2) - 29, this.aN - 31, com.donglh.narutoninjasaga.c.a.bv, this, 1001, -8);
        this.E = a(this.aM - 70, this.aN - 33, com.donglh.narutoninjasaga.c.a.bs, this, 1002, -8);
        this.B = new fx((byte) 1, 7, a_() + (this.G / 2), this.G << 2, this.G << 2, this.G, com.donglh.narutoninjasaga.e.aw.c(this.e.length, 4), 4);
        this.C = new fx((byte) 1, 7 + (5 * this.G), a_() + (this.G / 2), this.G << 2, this.G << 2, this.G, com.donglh.narutoninjasaga.e.aw.c(this.f.length, 4), 4);
        this.F = a(Input.Keys.FORWARD_DEL, Input.Keys.NUMPAD_1, "", this, 1002, 288);
        a(this.F, 0);
        a(this.D, 0);
        a(this.E, q());
        a(0);
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        super.a(guVar, i, i2);
        if (this.i.b == 0) {
            switch (guVar.b) {
                case 1001:
                    try {
                        if (this.a == 0) {
                            this.a = 1;
                            Vector vector = new Vector();
                            for (int i3 = 0; i3 < this.e.length; i3++) {
                                if (this.e[i3] != null) {
                                    vector.add(this.e[i3]);
                                }
                            }
                            com.donglh.narutoninjasaga.e.ak akVar = new com.donglh.narutoninjasaga.e.ak((byte) 82);
                            akVar.c(this.c);
                            akVar.a(vector.size());
                            for (int i4 = 0; i4 < vector.size(); i4++) {
                                akVar.b(((q) vector.elementAt(i4)).e);
                            }
                            akVar.l();
                            if (this.a == this.b) {
                                this.g = com.donglh.narutoninjasaga.e.aw.a() + 5999;
                                return;
                            }
                            return;
                        } else if (this.a == 1 && this.b > 0 && this.g < com.donglh.narutoninjasaga.e.aw.a() + 999) {
                            this.a = 2;
                            new com.donglh.narutoninjasaga.e.ak((byte) 81).l();
                            return;
                        } else {
                            return;
                        }
                    } catch (Exception e) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) this);
                        return;
                    }
                case 1002:
                    if (this.a == 0 && this.c > 0) {
                        d.a().B += this.c;
                        this.c = 0;
                        return;
                    }
                    return;
                case 5001:
                    this.h = 1;
                    this.n = a(guVar, this, this.e[guVar.j.i]);
                    return;
                case 5002:
                    this.h = 2;
                    this.n = a(guVar, this, this.f[guVar.j.i]);
                    return;
                default:
                    return;
            }
        } else if (this.i.b == q()) {
            switch (guVar.b) {
                case 1002:
                    this.s.a(new gv(this.s, this));
                    return;
                default:
                    return;
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.el
    public final void a(int i) {
        super.a(i);
        b(this.E);
        if (i == q()) {
            if (this.a == 0) {
                a(this.E);
            }
            b(this.y);
            b(this.z);
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v2 */
    /* JADX WARN: Type inference failed for: r0v3, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v4, types: [com.donglh.narutoninjasaga.e.ak] */
    @Override // com.donglh.narutoninjasaga.d.ej
    public final void d() {
        super.d();
        int i = 0;
        while (true) {
            ?? r0 = i;
            if (r0 < this.e.length) {
                if (this.e[i] != null) {
                    d.a().W[this.e[i].e] = this.e[i];
                    this.e[i] = null;
                }
                i++;
            } else {
                try {
                    r0 = new com.donglh.narutoninjasaga.e.ak((byte) 83);
                    r0.l();
                    return;
                } catch (Exception e) {
                    com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                    return;
                }
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.cn
    public final void a_(int i) {
    }

    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        super.a(lVar);
        if (this.i.b == 0) {
            b(lVar, this.B);
            for (int i = 0; i < this.B.g; i++) {
                for (int i2 = 0; i2 < this.B.o; i2++) {
                    b(lVar, i2 * this.B.f, i * this.B.f, this.e[(i * this.B.o) + i2], (i * this.B.o) + i2 == this.B.i);
                    if (this.a > 0) {
                        a.a(lVar, -13, 0, (i2 * this.B.f) + 2, (i * this.B.f) + 2, 25, 25);
                    }
                }
            }
            b(lVar, this.C);
            for (int i3 = 0; i3 < this.C.g; i3++) {
                for (int i4 = 0; i4 < this.C.o; i4++) {
                    b(lVar, i4 * this.C.f, i3 * this.C.f, this.f[(i3 * this.C.o) + i4], (i3 * this.C.o) + i4 == this.C.i);
                    if (this.b > 0) {
                        a.a(lVar, -13, 0, (i4 * this.C.f) + 2, (i3 * this.C.f) + 2, 25, 25);
                    }
                }
            }
            a(lVar, this.aY + 4, this.aZ + a_());
            for (int i5 = 0; i5 < 3; i5++) {
                com.donglh.narutoninjasaga.e.r.a(lVar, 10, 0, 27 + (i5 * 30), (a_() + (this.aN - 33)) - 64, 6);
                com.donglh.narutoninjasaga.e.r.a(lVar, 10, 0, 188 + (i5 * 30), (a_() + (this.aN - 33)) - 64, 6);
                if (this.a >= i5) {
                    com.donglh.narutoninjasaga.e.r.a(lVar, 11, 0, 31 + (i5 * 30), (a_() + (this.aN - 33)) - 64, 6);
                }
                if (this.b >= i5) {
                    com.donglh.narutoninjasaga.e.r.a(lVar, 11, 0, 192 + (i5 * 30), (a_() + (this.aN - 33)) - 64, 6);
                }
            }
            com.donglh.narutoninjasaga.e.r.c(lVar, 26, 27, 3, Input.Keys.FORWARD_DEL, 125, 21);
            a(lVar, 3, 122, "", (byte) 0);
            kk.b(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.c(this.c), 30, 122, 20, -3089954, -16777216);
            com.donglh.narutoninjasaga.e.r.c(lVar, 26, 27, 163, Input.Keys.FORWARD_DEL, 125, 21);
            a(lVar, 163, 122, "", (byte) 0);
            kk.b(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.c(this.d), 190, 122, 20, -3089954, -16777216);
            kk.b(kk.c, lVar, d.a().N, ((this.aM - 8) / 4) - 5, 7, 2, -1, -16777216);
            kk.b(kk.c, lVar, this.H, (((3 * (this.aM - 8)) / 4) - 10) + (this.G / 2), 7, 2, -1, -16777216);
        }
    }

    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void b() {
        super.b();
        if (this.i.b == 0) {
            if (this.a > 0) {
                b(this.F);
                b(this.E);
            }
            this.B.a();
            this.C.a();
        }
        if (this.a > 0 && this.b > 0) {
            long a = com.donglh.narutoninjasaga.e.aw.a();
            if (this.g > a + 1000) {
                this.D.a = com.donglh.narutoninjasaga.e.aw.b(com.donglh.narutoninjasaga.c.a.bw, new StringBuilder().append((this.g - a) / 1000).toString());
                this.D.a(true);
            } else {
                this.D.a = com.donglh.narutoninjasaga.c.a.aE;
                this.D.a(false);
            }
        } else if (this.a > 0) {
            this.D.a(true);
        }
        if (this.a >= 2) {
            this.D.n = true;
        }
    }

    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        Vector c = super.c();
        if (this.i.b == 0) {
            c.addElement(this.B.a(5001, this));
            c.addElement(this.C.a(5002, this));
        }
        return c;
    }

    public final int e() {
        return this.i.b;
    }
}
