package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.Input;
import com.badlogic.gdx.net.HttpStatus;
import java.util.Collections;
import java.util.Vector;
/* compiled from: LangLa_p.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/je.class */
public final class je extends hj {
    private String[] a;
    private hw f;
    private fw[] g;
    private fw[][] h;
    private Vector x;
    private boolean y;
    private Vector z;
    private Vector A;
    private Vector B;
    private cb C;
    private cb D;
    private cb E;
    private static int F;
    private cb[][] G;
    private int H;

    /* JADX WARN: Type inference failed for: r1v47, types: [com.donglh.narutoninjasaga.d.cb[], com.donglh.narutoninjasaga.d.cb[][]] */
    /* JADX WARN: Type inference failed for: r1v49, types: [com.donglh.narutoninjasaga.d.fw[], com.donglh.narutoninjasaga.d.fw[][]] */
    public je(com.donglh.narutoninjasaga.e.ai aiVar) {
        super(aiVar, 0, new String[]{com.donglh.narutoninjasaga.c.a.A});
        this.a = com.donglh.narutoninjasaga.c.a.sa;
        this.z = new Vector();
        this.A = new Vector();
        this.B = new Vector();
        this.s = aiVar;
        this.u = false;
        d(HttpStatus.SC_MULTIPLE_CHOICES, 220);
        this.f = new hw(new gz(1001, this.a), 4, a_(), this.aM - 8, 66, this, (byte) 0);
        this.C = a(34, (a_() + (this.aN - 33)) - 20, com.donglh.narutoninjasaga.c.a.gb, 100);
        dg a = a(this.aM - 70, (a_() + (this.aN - 33)) - 23, com.donglh.narutoninjasaga.c.a.gz, this, 1004, 7);
        a.a_(60, 20);
        this.f.a(a, 0);
        this.f.a(a, 1);
        dg a2 = a(this.aM - 134, (a_() + (this.aN - 33)) - 23, com.donglh.narutoninjasaga.c.a.pj, this, 1005, 7);
        a2.a_(60, 20);
        this.f.a(a2, 0);
        this.C.a = com.donglh.narutoninjasaga.e.c.g("friend" + d.a().aE);
        this.f.a(this.C, 0);
        this.D = a(9, (a_() + (this.aN - 33)) - 20, com.donglh.narutoninjasaga.c.a.gb, 100);
        this.D.a = com.donglh.narutoninjasaga.e.c.g("enermy" + d.a().aE);
        this.f.a(this.D, 1);
        this.f.a(a(9, (a_() + (this.aN - 33)) - 22, "", this, 1002, 65), 0);
        this.E = a(59, (a_() + (this.aN - 33)) - 20, com.donglh.narutoninjasaga.c.a.qh, Input.Keys.BUTTON_THUMBL);
        this.E.a = com.donglh.narutoninjasaga.e.ay.a().a(24);
        this.f.a(this.E, 2);
        this.G = new cb[2];
        this.h = new fw[2];
        this.g = new fw[3];
        e();
        v();
        w();
        this.f.b(s());
        a(0);
    }

    private void e() {
        this.z.clear();
        if (this.C.a) {
            for (int i = 0; i < com.donglh.narutoninjasaga.e.n.n().i.size(); i++) {
                k kVar = (k) com.donglh.narutoninjasaga.e.n.n().i.elementAt(i);
                if (kVar.d) {
                    this.z.add(kVar);
                }
            }
        } else {
            this.z.addAll(com.donglh.narutoninjasaga.e.n.n().i);
        }
        com.donglh.narutoninjasaga.e.c.a("friend" + d.a().aE, this.C.a);
        Collections.sort(this.z, k.a);
        this.H = 21;
        this.g[0] = new fw((byte) 1, 8, a_() + 30, (this.aM - 8) - 10, this.H * 6, this.H, this.z.size());
        this.G[0] = new cb[com.donglh.narutoninjasaga.e.n.n().i.size()];
        this.h[0] = new fw[com.donglh.narutoninjasaga.e.n.n().i.size()];
        for (int i2 = 0; i2 < com.donglh.narutoninjasaga.e.n.n().i.size(); i2++) {
            this.G[0][i2] = new cb(2, 2 + (i2 * this.g[0].f), "", this, 0);
            this.h[0][i2] = new gb(0, 0, this.G[0][i2].aM, this.G[0][i2].aN, this.G[0][i2].aN, this.g[0]);
        }
    }

    private void v() {
        this.A.clear();
        if (this.D.a) {
            for (int i = 0; i < com.donglh.narutoninjasaga.e.n.n().s.size(); i++) {
                i iVar = (i) com.donglh.narutoninjasaga.e.n.n().s.elementAt(i);
                if (iVar.c) {
                    this.A.add(iVar);
                }
            }
        } else {
            this.A.addAll(com.donglh.narutoninjasaga.e.n.n().s);
        }
        com.donglh.narutoninjasaga.e.c.a("enermy" + d.a().aE, this.D.a);
        Collections.sort(this.A, i.a);
        this.H = 21;
        this.g[1] = new fw((byte) 1, 8, a_() + 30, (this.aM - 8) - 10, this.H * 6, this.H, this.A.size());
        this.G[1] = new cb[com.donglh.narutoninjasaga.e.n.n().s.size()];
        this.h[1] = new fw[com.donglh.narutoninjasaga.e.n.n().s.size()];
        for (int i2 = 0; i2 < com.donglh.narutoninjasaga.e.n.n().s.size(); i2++) {
            this.G[1][i2] = new cb(2, 2 + (i2 * this.g[1].f), "", this, 0);
            this.h[1][i2] = new gb(0, 0, this.G[1][i2].aM, this.G[1][i2].aN, this.G[1][i2].aN, this.g[1]);
        }
    }

    private void w() {
        this.B.clear();
        this.B.add(d.a());
        this.B.addAll(com.donglh.narutoninjasaga.e.n.n().F);
        if (this.E.a) {
            this.g[2] = new fw((byte) 1, 8, a_() + 26, (this.aM - 8) - 10, Input.Keys.END, 22, this.B.size());
        } else {
            this.g[2] = new fw((byte) 1, 8, a_() + 30, (this.aM - 8) - 10, 126, 42, this.B.size());
        }
    }

    @Override // com.donglh.narutoninjasaga.d.hj, com.donglh.narutoninjasaga.d.ej
    public final void d() {
        super.d();
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v17, types: [com.donglh.narutoninjasaga.d.je] */
    /* JADX WARN: Type inference failed for: r0v18, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v21, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r0v24, types: [com.donglh.narutoninjasaga.d.je] */
    /* JADX WARN: Type inference failed for: r0v25, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v28, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r0v31, types: [com.donglh.narutoninjasaga.d.je] */
    /* JADX WARN: Type inference failed for: r0v32, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v35, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r0v38, types: [com.donglh.narutoninjasaga.d.hj, com.donglh.narutoninjasaga.d.je] */
    /* JADX WARN: Type inference failed for: r0v39, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v43, types: [com.donglh.narutoninjasaga.e.n] */
    /* JADX WARN: Type inference failed for: r0v46, types: [com.donglh.narutoninjasaga.d.je] */
    /* JADX WARN: Type inference failed for: r0v47, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v50, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r5v0, types: [com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.hj, java.lang.Exception, com.donglh.narutoninjasaga.d.je] */
    @Override // com.donglh.narutoninjasaga.d.hj, com.donglh.narutoninjasaga.d.cn
    public final void a(int i, Object obj, co coVar) {
        super.a(i, obj, coVar);
        if (this.i.b == 0) {
            switch (i) {
                case 100:
                    e();
                    v();
                    return;
                case Input.Keys.BUTTON_THUMBL /* 106 */:
                    com.donglh.narutoninjasaga.e.ay.a().a(24, this.E.a);
                    w();
                    return;
                case 2000:
                    gz gzVar = (gz) obj;
                    try {
                        if (this.f.b.b != 0) {
                            if (this.f.b.b == 2) {
                                d dVar = (d) this.B.elementAt(gzVar.a);
                                com.donglh.narutoninjasaga.e.ak akVar = new com.donglh.narutoninjasaga.e.ak((byte) 79);
                                akVar.a(dVar.N);
                                akVar.l();
                                return;
                            }
                            return;
                        }
                        k kVar = (k) this.z.elementAt(gzVar.a);
                        com.donglh.narutoninjasaga.e.ak akVar2 = new com.donglh.narutoninjasaga.e.ak((byte) 79);
                        akVar2.a(kVar.c);
                        akVar2.l();
                        return;
                    } catch (Exception e) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) this);
                        return;
                    }
                case 2001:
                    gz gzVar2 = (gz) obj;
                    try {
                        if (this.f.b.b == 0) {
                            com.donglh.narutoninjasaga.e.ak akVar3 = new com.donglh.narutoninjasaga.e.ak((byte) 76);
                            akVar3.a(e(gzVar2.a));
                            akVar3.l();
                            return;
                        } else if (this.f.b.b == 1) {
                            com.donglh.narutoninjasaga.e.ak akVar4 = new com.donglh.narutoninjasaga.e.ak((byte) -18);
                            akVar4.a(e(gzVar2.a));
                            akVar4.l();
                            return;
                        } else {
                            return;
                        }
                    } catch (Exception e2) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) this);
                        return;
                    }
                case 2002:
                    gz gzVar3 = (gz) obj;
                    ?? r0 = this;
                    try {
                        com.donglh.narutoninjasaga.e.n.n();
                        r0 = r0.e(gzVar3.a);
                        com.donglh.narutoninjasaga.e.n.d((String) r0);
                        return;
                    } catch (Exception e3) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                        return;
                    }
                case 2003:
                    gz gzVar4 = (gz) obj;
                    ?? r02 = this;
                    try {
                        r02.y = true;
                        r02.u();
                        r02.a(r02.q());
                        r02 = com.donglh.narutoninjasaga.e.n.n();
                        r02.a(r02.e(gzVar4.a), r02);
                        return;
                    } catch (Exception e4) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r02);
                        return;
                    }
                case 2004:
                    gz gzVar5 = (gz) obj;
                    ?? r03 = this;
                    try {
                        com.donglh.narutoninjasaga.e.n.n();
                        r03 = r03.e(gzVar5.a);
                        com.donglh.narutoninjasaga.e.n.h((String) r03);
                        return;
                    } catch (Exception e5) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r03);
                        return;
                    }
                case 2005:
                    gz gzVar6 = (gz) obj;
                    ?? r04 = this;
                    try {
                        com.donglh.narutoninjasaga.e.n.n();
                        r04 = r04.e(gzVar6.a);
                        com.donglh.narutoninjasaga.e.n.c((String) r04);
                        return;
                    } catch (Exception e6) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r04);
                        return;
                    }
                case 2006:
                    gz gzVar7 = (gz) obj;
                    ?? r05 = this;
                    try {
                        com.donglh.narutoninjasaga.e.n.n();
                        r05 = r05.e(gzVar7.a);
                        com.donglh.narutoninjasaga.e.n.e((String) r05);
                        return;
                    } catch (Exception e7) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r05);
                        return;
                    }
                case 2007:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.f(e(((gz) obj).a));
                    return;
                case 2008:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.g(e(((gz) obj).a));
                    return;
                default:
                    return;
            }
        }
    }

    private void x() {
        int i = 0;
        for (int i2 = 0; i2 < this.G[this.f.b.b].length; i2++) {
            if (this.G[this.f.b.b][i2].a) {
                b(i2);
                i++;
            }
        }
        if (i == 0) {
            com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.on, -65536);
        }
    }

    @Override // com.donglh.narutoninjasaga.d.hj, com.donglh.narutoninjasaga.d.cn
    public final void a_(int i) {
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v122, types: [com.donglh.narutoninjasaga.d.je] */
    /* JADX WARN: Type inference failed for: r0v123, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v125, types: [boolean] */
    @Override // com.donglh.narutoninjasaga.d.hj, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        int i = lVar.a;
        int i2 = lVar.b;
        if (this.y) {
            super.a(lVar);
        } else {
            a(lVar, (int) this.aY, (int) this.aZ, (int) this.aM, (int) this.aN, "", (byte) 2, false);
            a(lVar, this.aY, this.aZ);
            kk.a(kk.e, lVar, com.donglh.narutoninjasaga.c.a.A, this.aM / 2, 15, 2, -2560, 0);
            a(lVar, i, i2);
        }
        if (this.i.b == 0) {
            try {
                this.f.a(lVar);
                a(lVar, this.aY + 4, this.aZ + a_() + 22);
                a(lVar, -1, 0, (this.aM - 8) + 1, (this.aN - 33) - 48, 4, 55, 56);
                a(lVar, this.g[this.f.b.b]);
                int i3 = 0;
                while (i3 < this.g[this.f.b.b].g) {
                    if (this.g[this.f.b.b].b(i3)) {
                        if (i3 == this.g[this.f.b.b].i && this.f.b.b != 2) {
                            lVar.e(13136426);
                            lVar.c(0, i3 * this.g[this.f.b.b].f, this.g[this.f.b.b].aM, this.g[this.f.b.b].f - 1);
                            lVar.c(0, i3 * this.g[this.f.b.b].f, this.g[this.f.b.b].aM, this.g[this.f.b.b].f - 1);
                        }
                        switch (this.f.b.b) {
                            case 0:
                                k kVar = (k) this.x.elementAt(i3);
                                int i4 = i3;
                                ?? r0 = this;
                                try {
                                    r0 = kVar.d;
                                    if (r0 != 0) {
                                        com.donglh.narutoninjasaga.e.r.a(lVar, HttpStatus.SC_MOVED_TEMPORARILY, 0, 22, 6 + (i4 * r0.g[r0.f.b.b].f), 20);
                                    } else if (kVar.b == 1) {
                                        com.donglh.narutoninjasaga.e.r.a(lVar, HttpStatus.SC_NOT_MODIFIED, 0, 22, 6 + (i4 * r0.g[r0.f.b.b].f), 20);
                                    } else {
                                        com.donglh.narutoninjasaga.e.r.a(lVar, HttpStatus.SC_SEE_OTHER, 0, 22, 6 + (i4 * r0.g[r0.f.b.b].f), 20);
                                    }
                                    if (i4 == r0.g[r0.f.b.b].j) {
                                        kk.b(kk.c, lVar, kVar.c + (kVar.b == 2 ? com.donglh.narutoninjasaga.c.a.gd : ""), 35, 10 + (i4 * r0.g[r0.f.b.b].f), 0, -3089954, -16777216);
                                    } else {
                                        kk.b(kk.c, lVar, kVar.c + (kVar.b == 2 ? com.donglh.narutoninjasaga.c.a.gd : ""), 34, 11 + (i4 * r0.g[r0.f.b.b].f), 0, -3089954, -16777216);
                                    }
                                    int i5 = lVar.a;
                                    int i6 = lVar.b;
                                    r0.a(lVar, i5 + r0.G[r0.f.b.b][i4].aY, i6 + r0.G[r0.f.b.b][i4].aZ);
                                    r0.G[r0.f.b.b][i4].a(lVar);
                                    r0.a(lVar, i5, i6);
                                    continue;
                                } catch (Exception e) {
                                    com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                                    break;
                                }
                            case 1:
                                i iVar = (i) this.x.elementAt(i3);
                                if (iVar.c) {
                                    com.donglh.narutoninjasaga.e.r.a(lVar, HttpStatus.SC_MOVED_TEMPORARILY, 0, 22, 6 + (i3 * this.g[this.f.b.b].f), 20);
                                } else {
                                    com.donglh.narutoninjasaga.e.r.a(lVar, HttpStatus.SC_NOT_MODIFIED, 0, 22, 6 + (i3 * this.g[this.f.b.b].f), 20);
                                }
                                if (i3 == this.g[this.f.b.b].j) {
                                    kk.b(kk.c, lVar, iVar.b, 35, 10 + (i3 * this.g[this.f.b.b].f), 0, -1, -16777216);
                                } else {
                                    kk.b(kk.c, lVar, iVar.b, 34, 11 + (i3 * this.g[this.f.b.b].f), 0, -1, -16777216);
                                }
                                int i7 = lVar.a;
                                int i8 = lVar.b;
                                a(lVar, i7 + this.G[this.f.b.b][i3].aY, i8 + this.G[this.f.b.b][i3].aZ);
                                this.G[this.f.b.b][i3].a(lVar);
                                a(lVar, i7, i8);
                                continue;
                            case 2:
                                d dVar = (d) this.x.elementAt(i3);
                                if (i3 == this.g[this.f.b.b].i) {
                                    lVar.e(-3640790);
                                    lVar.c(1, 1 + (i3 * this.g[this.f.b.b].f), this.g[this.f.b.b].aM - 2, this.g[this.f.b.b].f - 2);
                                }
                                a(lVar, 0, i3 * this.g[this.f.b.b].f, this.g[this.f.b.b].aM, this.g[this.f.b.b].f - 2, -13, 84, 5, 1, 1);
                                if (!this.E.a) {
                                    a.a(lVar, com.donglh.narutoninjasaga.e.f.c().S[dVar.b].a, 0, 10, (i3 * this.g[this.f.b.b].f) + 5, 83);
                                    int i9 = i3 == this.g[this.f.b.b].i ? -2560 : -1;
                                    int i10 = 46;
                                    int i11 = (i3 * this.g[this.f.b.b].f) + 10;
                                    if (i3 == this.g[this.f.b.b].j && !dVar.N.equals(d.a().N)) {
                                        i10 = 46 + 1;
                                        i11++;
                                    }
                                    kk.a(kk.c, lVar, com.donglh.narutoninjasaga.c.a.ry[3] + " " + dVar.N, i10, i11, 0, i9);
                                    kk.a(kk.c, lVar, com.donglh.narutoninjasaga.c.a.ry[5] + " " + dVar.i(), i10, i11 + 10, 0, i9);
                                    kk.a(kk.c, lVar, com.donglh.narutoninjasaga.c.a.ry[4] + " " + com.donglh.narutoninjasaga.e.f.c().R[dVar.H].b, i10, i11 + 20, 0, i9);
                                    break;
                                } else {
                                    a.a(lVar, com.donglh.narutoninjasaga.e.f.c().S[dVar.b].a, 0, 6, (i3 * this.g[this.f.b.b].f) + 3, 40);
                                    int i12 = i3 == this.g[this.f.b.b].i ? -2560 : -1;
                                    int i13 = 25;
                                    int i14 = (i3 * this.g[this.f.b.b].f) + 10;
                                    if (i3 == this.g[this.f.b.b].j && !dVar.N.equals(d.a().N)) {
                                        i13 = 25 + 1;
                                        i14++;
                                    }
                                    kk.a(kk.c, lVar, com.donglh.narutoninjasaga.c.a.ry[3] + " " + dVar.N + ", " + com.donglh.narutoninjasaga.c.a.ry[5] + " " + dVar.i() + ", " + com.donglh.narutoninjasaga.c.a.ry[4] + " " + com.donglh.narutoninjasaga.e.f.c().R[dVar.H].b, i13, i14, 0, i12);
                                    continue;
                                }
                                break;
                        }
                    }
                    i3++;
                }
            } catch (Exception unused) {
            }
            b(lVar);
            a(lVar, this.aY + 4, this.aZ + a_());
            switch (this.f.b.b) {
                case 0:
                    kk.a(kk.c, lVar, com.donglh.narutoninjasaga.c.a.nX + this.x.size(), (int) Input.Keys.FORWARD_DEL, (this.aN - 33) - 13, 0, -1);
                    this.g[this.f.b.b].d(lVar, -17, -40);
                    return;
                case 1:
                    kk.a(kk.c, lVar, com.donglh.narutoninjasaga.c.a.nX + this.x.size(), 97, (this.aN - 33) - 13, 0, -1);
                    this.g[this.f.b.b].d(lVar, -17, -40);
                    return;
                case 2:
                    kk.a(kk.c, lVar, com.donglh.narutoninjasaga.c.a.gc + this.B.size(), 10, (this.aN - 33) - 10, 0, -1);
                    return;
                default:
                    return;
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.hj, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void b() {
        super.b();
        if (this.i.b == 0) {
            this.f.b();
            for (int i = 0; i < this.g.length; i++) {
                this.g[this.f.b.b].a();
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.hj, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        Vector c = super.c();
        if (!this.y) {
            c.removeElementAt(0);
        }
        if (this.i.b == 0) {
            if (this.f.b.b < 2) {
                for (int i = 0; i < this.h[this.f.b.b].length; i++) {
                    int i2 = this.g[this.f.b.b].aY + this.G[this.f.b.b][i].aY;
                    int i3 = (this.g[this.f.b.b].aZ + this.G[this.f.b.b][i].aZ) - this.g[this.f.b.b].d;
                    if (com.donglh.narutoninjasaga.e.aw.a((int) this.g[this.f.b.b].aY, (int) this.g[this.f.b.b].aZ, this.g[this.f.b.b].aY + this.g[this.f.b.b].aM, this.g[this.f.b.b].aZ + this.g[this.f.b.b].aN, i2, i3) && com.donglh.narutoninjasaga.e.aw.a((int) this.g[this.f.b.b].aY, (int) this.g[this.f.b.b].aZ, this.g[this.f.b.b].aY + this.g[this.f.b.b].aM, this.g[this.f.b.b].aZ + this.g[this.f.b.b].aN, i2 + this.G[this.f.b.b][i].aM, i3 + this.G[this.f.b.b][i].aN)) {
                        this.h[this.f.b.b][i].aY = (short) i2;
                        this.h[this.f.b.b][i].aZ = (short) i3;
                        c.addElement(this.h[this.f.b.b][i].a(i + 8000, this));
                    }
                }
            }
            c.addElement(this.f.c());
            c.addElement(this.g[this.f.b.b].a(1003, this));
        }
        return c;
    }

    @Override // com.donglh.narutoninjasaga.d.hj, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        super.a(guVar, i, i2);
        if (guVar.b >= 8000 && guVar.b < 9000) {
            int i3 = guVar.b - 8000;
            this.G[this.f.b.b][i3].a = !this.G[this.f.b.b][i3].a;
        } else if (this.i.b == 0) {
            switch (guVar.b) {
                case 1001:
                    if (guVar.j.i >= 0) {
                        F = guVar.j.i;
                        this.f.a(guVar.j.i);
                        z();
                        return;
                    }
                    return;
                case 1002:
                    return;
                case 1003:
                    if (guVar.j.i >= 0) {
                        int i4 = guVar.j.i;
                        Vector vector = new Vector();
                        if (this.f.b.b == 0) {
                            k kVar = (k) this.z.elementAt(i4);
                            if (kVar.b == 2) {
                                vector.addElement(new hz(2000, com.donglh.narutoninjasaga.c.a.ge));
                                vector.addElement(new hz(2001, com.donglh.narutoninjasaga.c.a.gf));
                            }
                            if (kVar.b < 2) {
                                vector.addElement(new hz(2001, com.donglh.narutoninjasaga.c.a.gg));
                            }
                            if (com.donglh.narutoninjasaga.e.n.n().ay != null && !com.donglh.narutoninjasaga.e.n.n().ay.b()) {
                                if (com.donglh.narutoninjasaga.e.n.n().ay.a() && !com.donglh.narutoninjasaga.e.n.n().ay.a(kVar.c)) {
                                    vector.addElement(new hz(2005, com.donglh.narutoninjasaga.c.a.dw));
                                }
                            } else {
                                vector.addElement(new hz(2005, com.donglh.narutoninjasaga.c.a.dv));
                            }
                            vector.addElement(new hz(2002, com.donglh.narutoninjasaga.c.a.dx));
                            vector.addElement(new hz(2003, com.donglh.narutoninjasaga.c.a.cc));
                            vector.addElement(new hz(2004, com.donglh.narutoninjasaga.c.a.ca));
                            vector.addElement(new hz(2008, com.donglh.narutoninjasaga.c.a.oy));
                        } else if (this.f.b.b == 1) {
                            i iVar = (i) this.x.elementAt(i4);
                            vector.addElement(new hz(2001, com.donglh.narutoninjasaga.c.a.gh));
                            if (com.donglh.narutoninjasaga.e.n.n().ay != null && !com.donglh.narutoninjasaga.e.n.n().ay.b()) {
                                if (com.donglh.narutoninjasaga.e.n.n().ay.a() && !com.donglh.narutoninjasaga.e.n.n().ay.a(iVar.b)) {
                                    vector.addElement(new hz(2005, com.donglh.narutoninjasaga.c.a.dw));
                                }
                            } else {
                                vector.addElement(new hz(2005, com.donglh.narutoninjasaga.c.a.bz));
                            }
                            vector.addElement(new hz(2002, com.donglh.narutoninjasaga.c.a.dx));
                            vector.addElement(new hz(2003, com.donglh.narutoninjasaga.c.a.cc));
                            vector.addElement(new hz(2004, com.donglh.narutoninjasaga.c.a.ca));
                            vector.addElement(new hz(2008, com.donglh.narutoninjasaga.c.a.oy));
                        } else if (this.f.b.b == 2) {
                            d dVar = (d) this.x.elementAt(i4);
                            if (!dVar.equals(d.a())) {
                                if (com.donglh.narutoninjasaga.e.n.n().ay != null && !com.donglh.narutoninjasaga.e.n.n().ay.b()) {
                                    if (com.donglh.narutoninjasaga.e.n.n().ay.a() && !com.donglh.narutoninjasaga.e.n.n().ay.a(dVar.N)) {
                                        vector.addElement(new hz(2005, com.donglh.narutoninjasaga.c.a.dw));
                                    }
                                } else {
                                    vector.addElement(new hz(2005, com.donglh.narutoninjasaga.c.a.bz));
                                }
                                vector.addElement(new hz(2006, com.donglh.narutoninjasaga.c.a.D));
                                vector.addElement(new hz(2002, com.donglh.narutoninjasaga.c.a.dx));
                                vector.addElement(new hz(2003, com.donglh.narutoninjasaga.c.a.cc));
                                vector.addElement(new hz(2004, com.donglh.narutoninjasaga.c.a.ca));
                                vector.addElement(new hz(2008, com.donglh.narutoninjasaga.c.a.oy));
                            }
                        }
                        if (vector.size() > 0) {
                            String[] strArr = new String[vector.size()];
                            short[] sArr = new short[vector.size()];
                            for (int i5 = 0; i5 < vector.size(); i5++) {
                                hz hzVar = (hz) vector.elementAt(i5);
                                strArr[i5] = hzVar.b;
                                sArr[i5] = (short) hzVar.a;
                            }
                            this.n = a(this, i + 25, i2, new gz(i4, sArr, strArr));
                            return;
                        }
                        return;
                    }
                    return;
                case 1004:
                    x();
                    return;
                case 1005:
                    y();
                    return;
                default:
                    return;
            }
        }
    }

    private void y() {
        int i = 0;
        for (int i2 = 0; i2 < this.z.size(); i2++) {
            if (((k) this.z.get(i2)).b == 2) {
                b(i2);
                i++;
            }
        }
        com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.e.aw.b(com.donglh.narutoninjasaga.c.a.pk, String.valueOf(i)), -1);
    }

    @Override // com.donglh.narutoninjasaga.d.hj, com.donglh.narutoninjasaga.d.el
    public final void a(int i) {
        super.a(i);
        if (this.f != null && this.i.b == 0) {
            this.f.a(F);
            z();
        }
    }

    private void z() {
        if (this.f != null) {
            switch (this.f.b.b) {
                case 0:
                    this.x = this.z;
                    return;
                case 1:
                    this.x = this.A;
                    return;
                case 2:
                    this.x = this.B;
                    return;
                default:
                    return;
            }
        }
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x004c: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:11:0x004b */
    private void b(int i) {
        Exception a;
        try {
            if (this.i.b != 1) {
                if (this.i.b == 2) {
                    com.donglh.narutoninjasaga.e.ak akVar = new com.donglh.narutoninjasaga.e.ak((byte) -18);
                    akVar.a(e(i));
                    akVar.l();
                    return;
                }
                return;
            }
            com.donglh.narutoninjasaga.e.ak akVar2 = new com.donglh.narutoninjasaga.e.ak((byte) 76);
            akVar2.a(e(i));
            akVar2.l();
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a);
        }
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x005b: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:16:0x005a */
    private String e(int i) {
        Exception a;
        try {
            if (this.f.b.b == 0) {
                return ((k) this.x.elementAt(i)).c;
            }
            if (this.f.b.b == 1) {
                return ((i) this.x.elementAt(i)).b;
            }
            if (this.f.b.b == 2) {
                return ((d) this.B.elementAt(i)).N;
            }
            return "";
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a);
            return "";
        }
    }
}
