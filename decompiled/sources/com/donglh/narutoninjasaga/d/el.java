package com.donglh.narutoninjasaga.d;

import java.util.Vector;
/* compiled from: LangLa_fn.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/el.class */
public abstract class el extends em {
    public gz i;
    private fw a;
    private Vector[] b;
    public int v = 10;
    public boolean[] w;
    private boolean[] c;

    public el(com.donglh.narutoninjasaga.e.ai aiVar) {
        this.s = aiVar;
        d(com.donglh.narutoninjasaga.e.f.c().o - 20, com.donglh.narutoninjasaga.e.f.c().p - 20);
    }

    public final void a(gz gzVar, int i, int i2) {
        int length = gzVar.c.length;
        int i3 = length;
        if (length > i2) {
            i3 = i2;
        }
        this.i = gzVar;
        this.a = new fw((byte) 0, 20, 3, i * i3, 25, i, gzVar.c.length);
        this.b = new Vector[gzVar.c.length];
        this.w = new boolean[gzVar.c.length];
        this.c = new boolean[gzVar.c.length];
        for (int i4 = 0; i4 < this.b.length; i4++) {
            this.b[i4] = new Vector();
        }
    }

    public final int q() {
        return this.i.c.length - 1;
    }

    public final void a(cl clVar, int i) {
        this.b[i].addElement(clVar);
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public void b() {
        this.a.a();
    }

    private void a(l lVar, int i, int i2, int i3, String str, boolean z) {
        a.a(lVar, i3, 0, i, 3, i2 - this.v, 19);
        if (z) {
            kk.a(l.a(23.0f), lVar, str, i + ((i2 - this.v) / 2) + 1, 12, -2560, 0);
        } else {
            kk.a(l.a(23.0f), lVar, str, i + ((i2 - this.v) / 2), 11, -2560, 0);
        }
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public void a(l lVar) {
        a(lVar, (int) this.aY, (int) this.aZ, (int) this.aM, (int) this.aN, "", (byte) 20, false);
        a(lVar, this.a);
        int i = 0;
        while (i < this.i.c.length) {
            if (!this.c[i]) {
                if (i == this.i.b) {
                    a(lVar, i * this.a.f, this.a.f, 78, this.i.c[i], i == this.a.j);
                } else {
                    a(lVar, i * this.a.f, this.a.f, 76, this.i.c[i], i == this.a.j);
                }
                if (this.w[i]) {
                    com.donglh.narutoninjasaga.e.r.a(lVar, 713, 0, (((i + 1) * this.a.f) - this.v) - 4, 2, 0);
                }
            }
            i++;
        }
        b(lVar);
    }

    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public Vector c() {
        Vector vector = new Vector();
        vector.addElement(this.a.a(-9997, this));
        if (this.t == null) {
            this.t = new fz(this.aM, 28);
            this.t.n = this;
        }
        vector.addElement(new gu(-9999, 0, 0, this.t.aM, this.t.aN, this.t, this));
        return vector;
    }

    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public void a(gu guVar, int i, int i2) {
        try {
            switch (guVar.b) {
                case -9997:
                    if (guVar.j.i >= 0 && this.i.b != guVar.j.i) {
                        if (this instanceof hq) {
                            com.donglh.narutoninjasaga.e.n.n().bE = (byte) guVar.j.i;
                        }
                        a(guVar.j.i);
                        return;
                    }
                    return;
                default:
                    super.a(guVar, i, i2);
                    return;
            }
        } catch (Exception unused) {
        }
    }

    public final int r() {
        return this.i.b;
    }

    public void a(int i) {
        this.i.b = i;
        for (int i2 = 0; i2 < this.b.length; i2++) {
            for (int i3 = 0; i3 < this.b[i2].size(); i3++) {
                this.k.removeElement(this.b[i2].elementAt(i3));
            }
        }
        for (int i4 = 0; i4 < this.b[this.i.b].size(); i4++) {
            this.k.addElement(this.b[this.i.b].elementAt(i4));
        }
    }

    public final Vector s() {
        return this.b[0];
    }
}
