package com.donglh.narutoninjasaga.d;
/* compiled from: InputCanvas.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/p.class */
public final class p extends c {
    public static long g = System.currentTimeMillis();
    private ki h = new ki();
    private boolean i;

    /* JADX WARN: Type inference failed for: r0v13, types: [java.lang.Exception, com.donglh.narutoninjasaga.d.ep] */
    @Override // com.donglh.narutoninjasaga.d.c
    public final void a(char c) {
        ?? r0;
        try {
            g = System.currentTimeMillis();
            if (com.donglh.narutoninjasaga.e.f.c().am != null) {
                com.donglh.narutoninjasaga.e.ai aiVar = com.donglh.narutoninjasaga.e.f.c().am;
                if (!aiVar.cg.a(c)) {
                    r0 = aiVar.cg.q;
                    r0.a(c);
                }
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
        }
    }

    /* JADX WARN: Type inference failed for: r0v13, types: [java.lang.Exception, com.donglh.narutoninjasaga.d.ep] */
    @Override // com.donglh.narutoninjasaga.d.c
    public final void a(int i) {
        ?? r0;
        try {
            g = System.currentTimeMillis();
            if (com.donglh.narutoninjasaga.e.f.c().am != null) {
                com.donglh.narutoninjasaga.e.ai aiVar = com.donglh.narutoninjasaga.e.f.c().am;
                if (!aiVar.cg.c(i)) {
                    r0 = aiVar.cg.q;
                    r0.a(i);
                }
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
        }
    }

    /* JADX WARN: Type inference failed for: r0v13, types: [java.lang.Exception, com.donglh.narutoninjasaga.d.ep] */
    @Override // com.donglh.narutoninjasaga.d.c
    public final void b(int i) {
        ?? r0;
        try {
            g = System.currentTimeMillis();
            if (com.donglh.narutoninjasaga.e.f.c().am != null) {
                com.donglh.narutoninjasaga.e.ai aiVar = com.donglh.narutoninjasaga.e.f.c().am;
                if (!aiVar.cg.d(i)) {
                    r0 = aiVar.cg.q;
                    r0.b(i);
                }
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v0, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v17, types: [com.donglh.narutoninjasaga.d.cy] */
    /* JADX WARN: Type inference failed for: r0v4, types: [com.donglh.narutoninjasaga.e.ai] */
    /* JADX WARN: Type inference failed for: r0v5, types: [java.lang.Exception] */
    @Override // com.donglh.narutoninjasaga.d.c
    public final void e() {
        ?? r0;
        try {
            if (com.donglh.narutoninjasaga.e.f.c().am != null) {
                r0 = com.donglh.narutoninjasaga.e.f.c().am;
                try {
                    fg.a().b();
                    gm.a().b();
                    go.a().b();
                    gq.a().b();
                    r0.a();
                    for (int i = 0; i < r0.cf.size(); i++) {
                        cn cnVar = (cn) r0.cf.elementAt(i);
                        for (int i2 = 0; i2 < cnVar.k.size(); i2++) {
                            try {
                                try {
                                    ((cl) cnVar.k.elementAt(i2)).l();
                                } catch (Exception unused) {
                                }
                            } catch (Exception unused2) {
                            }
                        }
                        cnVar.l();
                        try {
                            if (cnVar.o != null && cnVar.o.a()) {
                                cnVar.o = null;
                            }
                            for (int i3 = 0; i3 < cnVar.k.size(); i3++) {
                                try {
                                    ((cl) cnVar.k.elementAt(i3)).b();
                                } catch (Exception unused3) {
                                }
                            }
                            cnVar.b();
                        } catch (Exception unused4) {
                        }
                        for (int i4 = 0; i4 < cnVar.k.size(); i4++) {
                            try {
                                try {
                                    cl clVar = (cl) cnVar.k.elementAt(i4);
                                    clVar.b_();
                                    clVar.q.a();
                                } catch (Exception unused5) {
                                }
                            } catch (Exception unused6) {
                            }
                        }
                        cnVar.b_();
                        cnVar.q.a();
                    }
                    if (!(r0.cg instanceof cy) && a.a != null) {
                        r0 = a.a;
                        r0.p();
                    }
                } catch (Exception e) {
                    com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                }
            }
        } catch (Exception e2) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
        }
    }

    /* JADX WARN: Type inference failed for: r0v29, types: [boolean] */
    /* JADX WARN: Type inference failed for: r0v30 */
    /* JADX WARN: Type inference failed for: r0v32 */
    @Override // com.donglh.narutoninjasaga.d.c
    public final void a(l lVar) {
        Exception exc;
        try {
            if (com.donglh.narutoninjasaga.e.f.c().am != null) {
                com.donglh.narutoninjasaga.e.ai aiVar = com.donglh.narutoninjasaga.e.f.c().am;
                exc = null;
                int i = 0;
                while (i < aiVar.cf.size()) {
                    try {
                        cn cnVar = (cn) aiVar.cf.elementAt(i);
                        cnVar.c(lVar);
                        ?? r0 = cn.p;
                        if (r0 == 0) {
                            int i2 = 0;
                            while (true) {
                                r0 = i2;
                                if (r0 < cnVar.k.size()) {
                                    cl clVar = (cl) cnVar.k.elementAt(i2);
                                    if (clVar.p == null && !(clVar.l instanceof cy)) {
                                        clVar.c(lVar);
                                    }
                                    i2++;
                                }
                            }
                        }
                        i++;
                        exc = r0;
                    } catch (Exception e) {
                        com.donglh.narutoninjasaga.e.aw.a(exc);
                        return;
                    }
                }
                if (!(aiVar instanceof com.donglh.narutoninjasaga.e.n)) {
                    if (!(aiVar instanceof in)) {
                        fg.a().a(lVar);
                        gm.a().a(lVar);
                        go.a().a(lVar);
                    }
                } else if (com.donglh.narutoninjasaga.e.n.n().t()) {
                    fg.a().a(lVar);
                    gm.a().a(lVar);
                    go.a().a(lVar);
                    gq.a().a(lVar);
                }
            }
        } catch (Exception e2) {
            com.donglh.narutoninjasaga.e.aw.a(exc);
        }
    }

    /* JADX WARN: Type inference failed for: r0v6, types: [com.donglh.narutoninjasaga.d.co, java.lang.Exception, com.donglh.narutoninjasaga.d.cn] */
    @Override // com.donglh.narutoninjasaga.d.c
    public final void a(int i, int i2) {
        ?? r0;
        gu c;
        try {
            g = System.currentTimeMillis();
            if (com.donglh.narutoninjasaga.e.f.c().am != null) {
                r0 = com.donglh.narutoninjasaga.e.f.c().am.cg;
                try {
                    if (r0.l != null) {
                        r0.l.r.a(r0.m, i - r0.l.aY, i2 - r0.l.aZ);
                        return;
                    }
                    for (int size = r0.k.size() - 1; size >= 0; size--) {
                        co coVar = (co) r0.k.elementAt(size);
                        if (coVar.r != null && coVar.r.b >= 0) {
                            gu c2 = coVar.r.c(i - coVar.aY, i2 - coVar.aZ);
                            if (c2 != null && c2.j != null) {
                                r0.l = coVar;
                                r0.m = c2.j;
                            }
                            return;
                        }
                    }
                    if (r0.r != null && r0.r.b >= 0 && (c = r0.r.c(i - r0.aY, i2 - r0.aZ)) != null && c.j != null) {
                        r0.l = r0;
                        r0.m = c.j;
                    }
                } catch (Exception e) {
                    com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                }
            }
        } catch (Exception e2) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
        }
    }

    /* JADX WARN: Type inference failed for: r0v11, types: [java.lang.Exception, com.donglh.narutoninjasaga.d.cn] */
    @Override // com.donglh.narutoninjasaga.d.c
    public final void b(int i, int i2) {
        ?? r0;
        try {
            g = System.currentTimeMillis();
            if (this.i) {
                c(this.h.aY, this.h.aZ);
            }
            this.i = true;
            this.h.g(i, i2);
            if (com.donglh.narutoninjasaga.e.f.c().am != null) {
                r0 = com.donglh.narutoninjasaga.e.f.c().am.cg;
                r0.b(i, i2);
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
        }
    }

    /* JADX WARN: Type inference failed for: r0v6, types: [com.donglh.narutoninjasaga.e.ai, java.lang.Exception] */
    @Override // com.donglh.narutoninjasaga.d.c
    public final void c(int i, int i2) {
        ?? r0;
        try {
            g = System.currentTimeMillis();
            this.i = false;
            if (com.donglh.narutoninjasaga.e.f.c().am != null) {
                r0 = com.donglh.narutoninjasaga.e.f.c().am;
                r0.h(i, i2);
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
        }
    }
}
