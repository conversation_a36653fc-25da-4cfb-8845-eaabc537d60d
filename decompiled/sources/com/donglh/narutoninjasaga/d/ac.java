package com.donglh.narutoninjasaga.d;

import java.util.Vector;
/* compiled from: LangLa_ah.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/ac.class */
public final class ac extends el {
    private static int[] c = new int[3];
    private static int d;
    public boolean a;
    public ib[] b;
    private hw e;
    private fw f;
    private cd g;
    private Vector h;
    private gw[] x;
    private boolean y;
    private int z;
    private fw[] A;
    private int B;
    private q C;

    public ac(com.donglh.narutoninjasaga.e.ai aiVar) {
        super(aiVar);
        this.a = true;
        this.b = new ib[0];
        this.h = new Vector();
        this.A = new fw[3];
        this.C = null;
        this.A[0] = new fw((byte) 1, 13, a_() + 11, (this.aM - 8) - 18, 162, 27, 0);
        this.A[1] = new fw((byte) 1, 13, a_() + 45, (this.aM - 8) - 18, 120, 15, 0);
        this.A[2] = new fw((byte) 1, 13, a_() + 11, (this.aM - 8) - 18, 27, 27, 1);
        gz gzVar = new gz(5000);
        gzVar.c = com.donglh.narutoninjasaga.c.a.sy;
        a(gzVar, 72, 4);
        this.v = 4;
        d(315, 213);
        this.u = false;
        this.g = a(173, 56, 80, 6, new gz(1002, com.donglh.narutoninjasaga.c.a.qQ), this, 0);
        a(d);
    }

    public final void a() {
        this.f = new fw((byte) 1, 4, this.e.d() + 28, this.aM - 8, 126, 18, this.b.length);
    }

    public final void a(com.donglh.narutoninjasaga.e.ak akVar) {
        try {
            if (this.x == null) {
                this.x = new gw[akVar.b.a.readByte()];
                for (int i = 0; i < this.x.length; i++) {
                    this.x[i] = new gw();
                    this.x[i].a[0] = akVar.b.d();
                    this.x[i].a[1] = akVar.b.d();
                    this.x[i].b = akVar.b.a.readLong();
                    this.x[i].c = akVar.b.a.readLong();
                    this.x[i].d = new String[akVar.b.a.readByte()];
                    new Vector();
                    for (int i2 = 0; i2 < this.x[i].d.length; i2++) {
                        this.x[i].d[i2] = akVar.j().trim();
                        int readByte = akVar.b.a.readByte();
                        Vector vector = new Vector();
                        for (int i3 = 0; i3 < readByte; i3++) {
                            q qVar = new q();
                            qVar.a(akVar);
                            vector.add(qVar);
                        }
                        if (this.x[i].e.contains(this.x[i].d[i2])) {
                            ((Vector) this.x[i].f.get(this.x[i].e.indexOf(this.x[i].d[i2]))).addAll(vector);
                        } else {
                            this.x[i].e.add(this.x[i].d[i2]);
                            this.x[i].f.add(vector);
                        }
                    }
                }
                this.A = new fw[]{new fw((byte) 1, 13, a_() + 11, (this.aM - 8) - 18, 162, 27, this.x.length), new fw((byte) 1, 13, a_() + 45, (this.aM - 8) - 18, 120, 15, 0), new fw((byte) 1, 13, a_() + 11, (this.aM - 8) - 18, 27, 27, 1)};
            }
        } catch (Exception unused) {
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v12, types: [com.donglh.narutoninjasaga.d.gw[]] */
    /* JADX WARN: Type inference failed for: r0v13, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v15, types: [com.donglh.narutoninjasaga.e.ak] */
    @Override // com.donglh.narutoninjasaga.d.el
    public final void a(int i) {
        super.a(i);
        if (this.i.b == 3) {
            ?? r0 = this.x;
            if (r0 != 0) {
                return;
            }
            try {
                r0 = com.donglh.narutoninjasaga.e.ak.b((byte) -57);
                r0.l();
                return;
            } catch (Exception e) {
                com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                return;
            }
        }
        d = i;
        switch (i) {
            case 0:
                this.e = new hw(new gz(1001, com.donglh.narutoninjasaga.c.a.qS), 3, a_(), (this.aM - 8) + 2, 61, this);
                break;
            case 1:
                this.e = new hw(new gz(1001, com.donglh.narutoninjasaga.c.a.rI), 3, a_(), (this.aM - 8) + 2, 61, this);
                break;
            case 2:
                this.e = new hw(new gz(1001, com.donglh.narutoninjasaga.c.a.rJ), 3, a_(), (this.aM - 8) + 2, 61, this);
                break;
            case 3:
                this.e = new hw(new gz(1001, com.donglh.narutoninjasaga.c.a.rJ), 3, a_(), (this.aM - 8) + 2, 61, this);
                break;
        }
        b(c[i]);
    }

    private void b(int i) {
        this.b = new ib[0];
        a();
        this.e.a(i);
        e();
        if (i > 3) {
            this.e.a.a(this.e.b.c.length);
        } else {
            this.e.a.a(0);
        }
    }

    /* JADX WARN: Type inference failed for: r0v1, types: [com.donglh.narutoninjasaga.e.ak, java.lang.Exception] */
    private void e() {
        ?? akVar;
        try {
            akVar = new com.donglh.narutoninjasaga.e.ak((byte) -22);
            switch (this.i.b) {
                case 0:
                    akVar.a(this.e.b.b);
                    break;
                case 1:
                    akVar.a(this.e.b.b + 6);
                    break;
                case 2:
                    akVar.a(this.e.b.b + 20);
                    break;
            }
            akVar.a(this.g.b.b);
            akVar.l();
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) akVar);
        }
    }

    @Override // com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void b() {
        this.e.b();
        this.f.a();
        for (int i = 0; i < this.A.length; i++) {
            if (this.A[i] != null) {
                this.A[i].a();
            }
        }
        if (!this.a) {
            this.g.b(true);
        } else if (this.i.b == 0 && this.e.b.b == 0) {
            this.g.o = false;
        } else {
            this.g.o = true;
        }
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a(int i, Object obj, co coVar) {
        switch (i) {
            case 1002:
                e();
                return;
            case 2002:
                gz gzVar = (gz) obj;
                try {
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.h(this.b[gzVar.a].b);
                    return;
                } catch (Exception unused) {
                    return;
                }
            case 2003:
                gz gzVar2 = (gz) obj;
                try {
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.e(this.b[gzVar2.a].b);
                    return;
                } catch (Exception unused2) {
                    return;
                }
            case 2004:
                gz gzVar3 = (gz) obj;
                try {
                    ho.c = this.b[gzVar3.a].b;
                    com.donglh.narutoninjasaga.e.n.n().a(this.b[gzVar3.a].b, new hk(com.donglh.narutoninjasaga.e.n.n(), 6));
                    return;
                } catch (Exception unused3) {
                    return;
                }
            default:
                return;
        }
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a_(int i) {
    }

    @Override // com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        Vector c2 = super.c();
        if (this.i.b == 3) {
            if (!this.y) {
                c2.addElement(this.A[0].a(9980, this));
            } else {
                c2.addElement(this.A[2].a(9981, this));
                c2.addElement(new gu(1030, this.A[2].aY, this.A[2].aZ - 12, this.A[2].aY + this.A[2].aM, this.A[1].aZ, null, this));
                c2.addElement(new gu(1030, this.A[1].aY, this.A[1].aZ + this.A[1].aN, this.A[1].aY + this.A[1].aM, this.A[1].aZ + this.A[1].aN + 50, null, this));
                for (int i = 0; i < this.x[this.z].f.size(); i++) {
                    for (int i2 = 0; i2 < ((Vector) this.x[this.z].f.get(i)).size(); i2++) {
                        q qVar = (q) ((Vector) this.x[this.z].f.get(i)).get(i2);
                        int i3 = this.A[1].aY + 4 + (i2 * 35);
                        int i4 = (this.A[1].aZ - this.A[1].d) + ((this.B + (i * 3)) * this.A[1].f) + 15;
                        if (this.A[1].b(this.B + (i * 3) + 3)) {
                            c2.addElement(new gu(6000, i3, i4, i3 + 28, i4 + 28, this.A[1], this, qVar));
                        }
                    }
                }
                c2.addElement(this.A[1].a(1030, this));
            }
        } else {
            c2.addElement(this.e.c());
            if (this.a) {
                c2.addElement(this.f.a(1003, this));
            }
        }
        return c2;
    }

    @Override // com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        super.a(lVar);
        if (this.i.b == 3) {
            try {
                a(lVar, 7, a_() + 5, (this.aM - 8) - 6, (this.aN - 33) - 10, 4, 55, 56);
                if (!this.y) {
                    a(lVar, this.A[0]);
                    int i = 0;
                    while (i < this.A[0].g) {
                        if (this.A[0].b(i)) {
                            a(lVar, 0, i * this.A[0].f, this.A[0].aM, 26, -17, 84, 5, 1, 1);
                            com.donglh.narutoninjasaga.e.r.a(lVar, 395, 0, 6, (i * this.A[0].f) + 8, 20);
                            int i2 = this.A[0].j == i ? 13 + (i * this.A[0].f) : 12 + (i * this.A[0].f);
                            kk.a(lVar, this.x[i].a[0], 22, i2, 0, -1);
                            kk kkVar = kk.c;
                            gw gwVar = this.x[i];
                            long a = com.donglh.narutoninjasaga.e.aw.a();
                            kk.c(kkVar, lVar, (gwVar.b == -1 || a >= gwVar.b) ? (gwVar.c == -1 || a < gwVar.c) ? (gwVar.b == -1 || gwVar.b > a || gwVar.c == -1 || a >= gwVar.c) ? "" : com.donglh.narutoninjasaga.c.a.ni : com.donglh.narutoninjasaga.c.a.nh : com.donglh.narutoninjasaga.c.a.ng, this.aM - 35, i2, 1, -1, -16777216);
                        }
                        i++;
                    }
                    b(lVar);
                    return;
                }
                a(lVar, this.A[2]);
                a(lVar, 0, 0 * this.A[2].f, this.A[2].aM, 26, -17, 84, 5, 1, 1);
                if (this.A[2].j == 0) {
                    kk.a(lVar, this.x[this.z].a[0], 22, 13, 0, -1);
                    lVar.d();
                    com.donglh.narutoninjasaga.e.r.a(lVar, 398, 0, this.A[2].aM - 21, 5, 20);
                    lVar.e();
                } else {
                    kk.a(lVar, this.x[this.z].a[0], 22, 12, 0, -1);
                    com.donglh.narutoninjasaga.e.r.a(lVar, 398, 0, this.A[2].aM - 22, 4, 20);
                }
                com.donglh.narutoninjasaga.e.r.a(lVar, 396, 0, 6, (0 * this.A[2].f) + 8, 20);
                b(lVar);
                a(lVar, this.A[1]);
                for (int i3 = 0; i3 < this.A[1].g; i3++) {
                    kk.c(kk.c, lVar, (String) this.h.get(i3), 2, 6 + (i3 * this.A[1].f), 0, -1, 0);
                }
                for (int i4 = 0; i4 < this.x[this.z].f.size(); i4++) {
                    for (int i5 = 0; i5 < ((Vector) this.x[this.z].f.get(i4)).size(); i5++) {
                        q qVar = (q) ((Vector) this.x[this.z].f.get(i4)).get(i5);
                        b(lVar, 4 + (i5 * 35), ((this.B + (i4 * 3)) * this.A[1].f) + 15, qVar, this.C != null && this.C.equals(qVar));
                    }
                }
                b(lVar);
                this.A[1].d(lVar, -14, -11);
                return;
            } catch (Exception unused) {
                return;
            }
        }
        this.e.a(lVar);
        if (!this.a) {
            kk.a(kk.d, lVar, com.donglh.narutoninjasaga.c.a.ms, this.aM / 2, (this.aN / 2) + 10);
            return;
        }
        b(lVar, this.f);
        a(lVar, 0, -28, this.f.aM, 28, -11, 55, 56);
        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.bU, 20, -15, 2, -6488, -10275328);
        switch (this.i.b) {
            case 0:
                if (this.e.b.b != 4) {
                    kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.ju, 70, -15, 2, -6488, -10275328);
                    if (this.e.b.b == 0) {
                        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.jt, 140, -15, 2, -6488, -10275328);
                    } else if (this.e.b.b == 1) {
                        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.qS[1], 140, -15, 2, -6488, -10275328);
                    } else if (this.e.b.b == 2) {
                        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.jw, 140, -15, 2, -6488, -10275328);
                    } else if (this.e.b.b == 3) {
                        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.jx, 140, -15, 2, -6488, -10275328);
                    }
                    kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.jz, 208, -15, 2, -6488, -10275328);
                    kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.jA, 274, -15, 2, -6488, -10275328);
                    a(lVar, this.f);
                    for (int i6 = 0; i6 < this.f.g; i6++) {
                        if (this.f.b(i6)) {
                            if (i6 == this.f.i) {
                                lVar.e(13136426);
                                lVar.c(0, i6 * this.f.f, this.f.aM, this.f.f);
                            }
                            ib ibVar = this.b[i6];
                            kk.b(kk.c, lVar, new StringBuilder().append(i6 + 1).toString(), 20, 8 + (i6 * this.f.f), 2, -3604601, -16777216);
                            kk.b(kk.c, lVar, ibVar.b, 70, 8 + (i6 * this.f.f), 2, -3604601, -16777216);
                            if (this.e.b.b == 0) {
                                kk.b(kk.c, lVar, new StringBuilder().append(ibVar.c).toString(), 140, 8 + (i6 * this.f.f), 2, -3604601, -16777216);
                            } else {
                                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.c(ibVar.d), 140, 8 + (i6 * this.f.f), 2, -3604601, -16777216);
                            }
                            kk.b(kk.c, lVar, com.donglh.narutoninjasaga.e.f.c().R[ibVar.e].b, 208, 8 + (i6 * this.f.f), 2, -3604601, -16777216);
                            kk.b(kk.c, lVar, ibVar.f, 274, 8 + (i6 * this.f.f), 2, -3604601, -16777216);
                        }
                    }
                    b(lVar);
                    this.f.d(lVar, -14, -11);
                    return;
                }
                kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.bV, 70, -15, 2, -6488, -10275328);
                kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.jt, 140, -15, 2, -6488, -10275328);
                kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.di, 208, -15, 2, -6488, -10275328);
                kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.dj, 274, -15, 2, -6488, -10275328);
                a(lVar, this.f);
                for (int i7 = 0; i7 < this.f.g; i7++) {
                    if (this.f.b(i7)) {
                        if (i7 == this.f.i) {
                            lVar.e(13136426);
                            lVar.c(0, i7 * this.f.f, this.f.aM, this.f.f);
                        }
                        ib ibVar2 = this.b[i7];
                        kk.b(kk.c, lVar, new StringBuilder().append(i7 + 1).toString(), 20, 8 + (i7 * this.f.f), 2, -3604601, -16777216);
                        kk.b(kk.c, lVar, ibVar2.f, 70, 8 + (i7 * this.f.f), 2, -3604601, -16777216);
                        kk.b(kk.c, lVar, ibVar2.c + "+" + ibVar2.g, 140, 8 + (i7 * this.f.f), 2, -3604601, -16777216);
                        kk.b(kk.c, lVar, ibVar2.d + "/" + ibVar2.e, 208, 8 + (i7 * this.f.f), 2, -3604601, -16777216);
                        kk.b(kk.c, lVar, ibVar2.b, 274, 8 + (i7 * this.f.f), 2, -3604601, -16777216);
                    }
                }
                b(lVar);
                return;
            case 1:
                d(lVar);
                return;
            case 2:
                e(lVar);
                return;
            default:
                return;
        }
    }

    private void d(l lVar) {
        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.ju, 70, -15, 2, -6488, -10275328);
        switch (this.e.b.b) {
            case 0:
                kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.jt, 140, -15, 2, -6488, -10275328);
                break;
            case 1:
                kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.jy, 140, -15, 2, -6488, -10275328);
                break;
            case 2:
                kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.rI[2], 140, -15, 2, -6488, -10275328);
                break;
            case 3:
                kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.rI[3], 140, -15, 2, -6488, -10275328);
                break;
        }
        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.jz, 208, -15, 2, -6488, -10275328);
        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.jA, 274, -15, 2, -6488, -10275328);
        a(lVar, this.f);
        for (int i = 0; i < this.f.g; i++) {
            if (this.f.b(i)) {
                if (i == this.f.i) {
                    lVar.e(13136426);
                    lVar.c(0, i * this.f.f, this.f.aM, this.f.f);
                }
                ib ibVar = this.b[i];
                kk.b(kk.c, lVar, new StringBuilder().append(i + 1).toString(), 20, 8 + (i * this.f.f), 2, -3604601, -16777216);
                kk.b(kk.c, lVar, ibVar.b, 70, 8 + (i * this.f.f), 2, -3604601, -16777216);
                if (this.e.b.b == 0) {
                    kk.b(kk.c, lVar, new StringBuilder().append(ibVar.c).toString(), 140, 8 + (i * this.f.f), 2, -3604601, -16777216);
                } else {
                    kk.b(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.c(ibVar.d), 140, 8 + (i * this.f.f), 2, -3604601, -16777216);
                }
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.e.f.c().R[ibVar.e].b, 208, 8 + (i * this.f.f), 2, -3604601, -16777216);
                kk.b(kk.c, lVar, ibVar.f, 274, 8 + (i * this.f.f), 2, -3604601, -16777216);
            }
        }
        b(lVar);
        this.f.d(lVar, -14, -11);
    }

    private void e(l lVar) {
        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.sF[this.e.b.b], 140, -15, 2, -6488, -10275328);
        if (this.e.b.b == 3) {
            kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.dD, 70, -15, 2, -6488, -10275328);
            kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.jt, 208, -15, 2, -6488, -10275328);
            kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.dj, 274, -15, 2, -6488, -10275328);
            a(lVar, this.f);
            for (int i = 0; i < this.f.g; i++) {
                if (this.f.b(i)) {
                    if (i == this.f.i) {
                        lVar.e(13136426);
                        lVar.c(0, i * this.f.f, this.f.aM, this.f.f);
                    }
                    ib ibVar = this.b[i];
                    kk.b(kk.c, lVar, new StringBuilder().append(i + 1).toString(), 20, 8 + (i * this.f.f), 2, -3604601, -16777216);
                    kk.b(kk.c, lVar, ibVar.f, 70, 8 + (i * this.f.f), 2, -3604601, -16777216);
                    kk.b(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.c(ibVar.d), 140, 8 + (i * this.f.f), 2, -3604601, -16777216);
                    kk.b(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.c(ibVar.c), 208, 8 + (i * this.f.f), 2, -3604601, -16777216);
                    kk.b(kk.c, lVar, ibVar.b, 274, 8 + (i * this.f.f), 2, -3604601, -16777216);
                }
            }
            b(lVar);
            this.f.d(lVar, -14, -11);
            return;
        }
        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.ju, 70, -15, 2, -6488, -10275328);
        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.jz, 208, -15, 2, -6488, -10275328);
        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.jA, 274, -15, 2, -6488, -10275328);
        a(lVar, this.f);
        for (int i2 = 0; i2 < this.f.g; i2++) {
            if (this.f.b(i2)) {
                if (i2 == this.f.i) {
                    lVar.e(13136426);
                    lVar.c(0, i2 * this.f.f, this.f.aM, this.f.f);
                }
                ib ibVar2 = this.b[i2];
                kk.b(kk.c, lVar, new StringBuilder().append(i2 + 1).toString(), 20, 8 + (i2 * this.f.f), 2, -3604601, -16777216);
                kk.b(kk.c, lVar, ibVar2.b, 70, 8 + (i2 * this.f.f), 2, -3604601, -16777216);
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.c(ibVar2.d), 140, 8 + (i2 * this.f.f), 2, -3604601, -16777216);
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.e.f.c().R[ibVar2.e].b, 208, 8 + (i2 * this.f.f), 2, -3604601, -16777216);
                kk.b(kk.c, lVar, ibVar2.f, 274, 8 + (i2 * this.f.f), 2, -3604601, -16777216);
            }
        }
        b(lVar);
        this.f.d(lVar, -14, -11);
    }

    @Override // com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        super.a(guVar, i, i2);
        this.C = null;
        switch (guVar.b) {
            case 1001:
                if (guVar.j.i >= 0) {
                    c[this.i.b] = guVar.j.i;
                    b(guVar.j.i);
                    return;
                }
                return;
            case 1003:
                if (guVar.j.i >= 0) {
                    int i3 = guVar.j.i;
                    Vector vector = new Vector();
                    vector.addElement(new hz(2002, com.donglh.narutoninjasaga.c.a.ca));
                    vector.addElement(new hz(2003, com.donglh.narutoninjasaga.c.a.cb));
                    vector.addElement(new hz(2004, com.donglh.narutoninjasaga.c.a.cc));
                    if (vector.size() > 0) {
                        String[] strArr = new String[vector.size()];
                        short[] sArr = new short[vector.size()];
                        for (int i4 = 0; i4 < vector.size(); i4++) {
                            hz hzVar = (hz) vector.elementAt(i4);
                            strArr[i4] = hzVar.b;
                            sArr[i4] = (short) hzVar.a;
                        }
                        this.n = a(this, i + 25, i2, new gz(i3, sArr, strArr));
                        return;
                    }
                    return;
                }
                return;
            case 6000:
                this.C = (q) guVar.k;
                this.n = a(this, i, i2, 28, this.C);
                return;
            case 9980:
                if (guVar.j.i >= 0) {
                    this.z = guVar.j.i;
                    String[] a = com.donglh.narutoninjasaga.e.aw.a(this.x[this.z].a[1], "\\n");
                    this.h.clear();
                    String a2 = this.x[this.z].a();
                    if (a2.length() > 0) {
                        this.h.add("c#yellow" + a2);
                    }
                    for (String str : a) {
                        this.h.addAll(kk.a(kk.c, str, (this.aM - 8) - 40));
                    }
                    this.h.add("c#blue" + com.donglh.narutoninjasaga.c.a.iZ);
                    this.B = this.h.size();
                    for (int i5 = 0; i5 < this.x[this.z].e.size(); i5++) {
                        this.h.add("c#cyan- " + ((String) this.x[this.z].e.get(i5)));
                        this.h.add("");
                        this.h.add("");
                    }
                    this.A[1] = new fw((byte) 1, 13, a_() + 45, (this.aM - 8) - 18, 120, 15, this.h.size());
                    this.y = true;
                    return;
                }
                return;
            case 9981:
                this.y = false;
                return;
            default:
                return;
        }
    }
}
