package com.donglh.narutoninjasaga.d;

import java.util.Vector;
/* compiled from: LangLa_lf.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/hw.class */
public final class hw extends cp {
    public fw a;
    public gz b;
    public fb c;
    private Vector[] d;
    private cn e;
    private short f = 21;

    public hw(gz gzVar, int i, int i2, int i3, int i4, cn cnVar) {
        a(gzVar, i, i2, i3, 23, i4, cnVar, false);
    }

    public hw(gz gzVar, int i, int i2, int i3, int i4, cn cnVar, byte b) {
        a(gzVar, i, i2, i3, 23, i4, cnVar, false);
    }

    private void a(gz gzVar, int i, int i2, int i3, int i4, int i5, cn cnVar, boolean z) {
        int length;
        g(i, i2);
        a_(i3, 23);
        this.e = cnVar;
        this.b = gzVar;
        if (gzVar.c.length * i5 > i3 - 4) {
            length = ((i3 - 4) / i5) * i5;
        } else {
            length = gzVar.c.length * i5;
        }
        this.a = new fw((byte) 0, 2, 2, length, 20, i5, gzVar.c.length);
        this.c = new fb(0, 2, (byte) 7, gzVar.c.length);
        this.d = new Vector[gzVar.c.length];
        for (int i6 = 0; i6 < this.d.length; i6++) {
            this.d[i6] = new Vector();
        }
    }

    public final void a() {
        for (int i = 0; i < this.d.length; i++) {
            for (int i2 = 0; i2 < this.d[i].size(); i2++) {
                this.e.k.removeElement(this.d[i].elementAt(i2));
            }
        }
    }

    public final void b() {
        this.a.a();
        this.c.a();
    }

    public final gu c() {
        return this.a.a(this.b.a, this.aY, this.aZ, this.e);
    }

    public final void a(int i) {
        this.b.b = i;
        for (int i2 = 0; i2 < this.d.length; i2++) {
            for (int i3 = 0; i3 < this.d[i2].size(); i3++) {
                this.e.k.removeElement(this.d[i2].elementAt(i3));
            }
        }
        for (int i4 = 0; i4 < this.d[this.b.b].size(); i4++) {
            this.e.k.addElement(this.d[this.b.b].elementAt(i4));
        }
    }

    public final void a(l lVar) {
        int i = lVar.a;
        int i2 = lVar.b;
        a(lVar, this.e.aY + this.aY, this.e.aZ + this.aZ);
        if (this.b.c.length * this.a.f <= this.aM - 4) {
            a.a(lVar, 24, 2 + (this.b.b * this.a.f), 2, this.a.f);
            a.a(lVar, 60, 2, 2, this.aM - 4);
        }
        com.donglh.narutoninjasaga.e.aw.a(lVar, this.aM, this.aN);
        if (!this.c.a && this.b.c.length * this.a.f <= this.aM - 4) {
            this.a.b(lVar, this.e.aY + this.aY, this.e.aZ + this.aZ);
        } else {
            this.a.a(lVar, this.e.aY + this.aY, this.e.aZ + this.aZ, this.a.aM, this.a.aN);
            if (this.b.c.length * this.a.f > this.aM - 4) {
                a.a(lVar, 24, this.b.b * this.a.f, 0, this.a.f);
                a.a(lVar, 60, 0, 0, this.b.c.length * this.a.f);
            }
        }
        for (int i3 = 0; i3 < this.a.g; i3++) {
            if (this.a.b(i3)) {
                String str = this.b.c[i3];
                if ((this.e instanceof hj) && this.b.d != null && this.b.d[i3] > 0) {
                    str = str + " (" + ((int) this.b.d[i3]) + ")";
                }
                if (i3 == this.b.b) {
                    kk.b(kk.d, lVar, str, ((1 + (i3 * this.a.f)) + (this.a.f / 2)) - 1, (11 + this.c.b(i3)) - 2, 2, -2560, -11184811);
                } else if (i3 == this.a.j) {
                    kk.b(kk.d, lVar, str, 1 + (i3 * this.a.f) + (this.a.f / 2), (12 + this.c.b(i3)) - 2, 2, -1, -11184811);
                } else {
                    kk.b(kk.d, lVar, str, ((1 + (i3 * this.a.f)) + (this.a.f / 2)) - 1, (11 + this.c.b(i3)) - 2, 2, -1, -11184811);
                }
            }
        }
        if (this.c.a || this.b.c.length * this.a.f > this.aM - 4) {
            a(lVar, 0, 0);
            lVar.d(0, 0, com.donglh.narutoninjasaga.e.f.c().o, com.donglh.narutoninjasaga.e.f.c().p);
        }
        a(lVar, i, i2);
    }

    public final void a(cl clVar, int i) {
        this.d[i].addElement(clVar);
    }

    public final void a(Vector vector) {
        for (int i = 0; i < vector.size(); i++) {
            this.d[0].addElement((cl) vector.elementAt(i));
        }
    }

    public final void b(Vector vector) {
        for (int i = 0; i < this.d.length; i++) {
            for (int i2 = 0; i2 < this.d[i].size(); i2++) {
                vector.addElement(this.d[i].elementAt(i2));
            }
        }
    }

    public final void a(boolean z) {
        for (int i = 0; i < this.d.length; i++) {
            for (int i2 = 0; i2 < this.d[i].size(); i2++) {
                cl clVar = (cl) this.d[i].elementAt(i2);
                if (clVar instanceof dg) {
                    ((dg) clVar).b(z);
                } else if (clVar instanceof kn) {
                    clVar.a(z);
                }
            }
        }
    }

    public final int d() {
        return this.aZ + this.aN;
    }
}
