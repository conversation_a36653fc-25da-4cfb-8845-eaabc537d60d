package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.Input;
import java.util.Vector;
/* compiled from: LangLa_au.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/ap.class */
public final class ap extends et {
    public q a;
    public q[] b;
    private fx[] d;
    private dg e;
    private dg f;
    private long g;
    private int h;
    private int B;
    private ci[] C;
    private gy D;
    private int E;
    public gt c;
    private cd F;

    public ap(com.donglh.narutoninjasaga.e.ai aiVar) {
        super(aiVar, new String[]{com.donglh.narutoninjasaga.c.a.rp[0]});
        this.b = new q[16];
        this.d = new fx[2];
        this.g = 0L;
        this.h = 0;
        this.B = 0;
        this.C = new ci[2];
        this.E = 0;
        this.d[0] = new fx((byte) 1, 193, a_() + 20, 30, 30, 30, 1, 1);
        this.d[1] = new fx((byte) 1, 14, a_() + 20, 128, 128, 32, 4, 4);
        this.e = a(this.aM - 70, this.aN - 33, com.donglh.narutoninjasaga.c.a.aZ, this, 0, -8);
        a(this.e, 0);
        this.f = a(Input.Keys.BUTTON_SELECT, this.aN - 33, com.donglh.narutoninjasaga.c.a.jY, this, 2002, -8);
        a(this.f, 0);
        gz gzVar = new gz(2000, com.donglh.narutoninjasaga.c.a.rQ);
        int length = gzVar.c.length;
        this.F = a(14, this.aN - 32, 90, com.donglh.narutoninjasaga.e.f.c().p <= 240 ? length - 3 : length, gzVar, this, 1);
        this.F.a(com.donglh.narutoninjasaga.e.ay.a().b(7));
        a(this.F, 0);
        this.D = new gy(0, 1);
        this.C[0] = a(Input.Keys.NUMPAD_5, a_() + 58, com.donglh.narutoninjasaga.c.a.bi, this, this.D);
        this.C[1] = a(Input.Keys.NUMPAD_5, a_() + 78, com.donglh.narutoninjasaga.c.a.bj, this, this.D);
        a(this.C[0], 0);
        a(this.C[1], 0);
        if (kc.g) {
            kc.g = false;
            e();
        }
    }

    @Override // com.donglh.narutoninjasaga.d.et
    public final void a() {
        this.A = new q[d.a().W.length];
        int i = 0;
        for (int i2 = 0; i2 < d.a().W.length; i2++) {
            if (d.a().W[i2] != null && d.a().W[i2].q()) {
                this.A[i] = d.a().W[i2];
                i++;
            }
        }
    }

    private void e() {
        if (this.a != null) {
            e(this.a.e);
        }
        this.a = null;
        int i = this.F.b.b;
        int i2 = 0;
        boolean z = this.D.b != 0;
        for (int i3 = 0; i3 < d.a().W.length && i2 < 16; i3++) {
            if (d.a().W[i3] != null && d.a().W[i3].q == z && d.a().W[i3].q() && d.a().W[i3].h().a <= i) {
                a(d.a().W[i3]);
                i2++;
            }
        }
        b();
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v0 */
    /* JADX WARN: Type inference failed for: r0v1 */
    /* JADX WARN: Type inference failed for: r0v11 */
    /* JADX WARN: Type inference failed for: r0v2, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v5, types: [com.donglh.narutoninjasaga.d.q[]] */
    private void a(q qVar) {
        ?? r0 = 0;
        int i = 0;
        while (true) {
            try {
                if (i >= this.b.length) {
                    break;
                }
                q qVar2 = this.b[i];
                if (qVar2 != null) {
                    i++;
                    r0 = qVar2;
                } else {
                    this.b[i] = qVar;
                    break;
                }
            } catch (Exception e) {
                com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                return;
            }
        }
        r0 = d.a().W;
        r0[qVar.e] = 0;
    }

    @Override // com.donglh.narutoninjasaga.d.el
    public final void a(int i) {
        super.a(i);
        if (i == 1) {
            if (this.a != null) {
                e(this.a.e);
            }
            this.a = null;
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void b() {
        try {
            super.b();
            if (this.i.b == 0) {
                this.B = 0;
                this.h = 0;
                this.g = 0L;
                for (int i = 0; i < this.b.length; i++) {
                    if (this.b[i] != null) {
                        this.B++;
                        this.g += com.donglh.narutoninjasaga.e.f.c().av[this.b[i].c];
                    }
                }
                if (this.g > 0) {
                    this.h = com.donglh.narutoninjasaga.e.f.c().av.length - 1;
                    while (this.h >= 0 && this.g <= com.donglh.narutoninjasaga.e.f.c().av[this.h]) {
                        this.h--;
                    }
                }
                for (int i2 = 0; i2 < this.d.length; i2++) {
                    this.d[i2].a();
                }
            } else {
                a();
            }
            if (this.c != null) {
                this.c.b();
                if (this.c.h()) {
                    this.c = null;
                }
            }
            if (this.E > 0) {
                this.E--;
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) this);
        }
    }

    /* JADX WARN: Type inference failed for: r0v21, types: [com.donglh.narutoninjasaga.d.gt, java.lang.Exception] */
    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        ?? r0;
        try {
            if (q() <= 0) {
                a(lVar, (int) this.aY, (int) this.aZ, (int) this.aM, (int) this.aN, com.donglh.narutoninjasaga.c.a.o, (byte) 2, false);
            } else {
                super.a(lVar);
            }
            if (this.i.b == 0) {
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.aY, 14, a_() + 9, 0, -10831436, -16777216);
                b(lVar, this.d[0].aY, this.d[0].aZ, this.a, this.d[0].i >= 0, com.donglh.narutoninjasaga.c.a.G);
                com.donglh.narutoninjasaga.e.r.a(lVar, 95, 0, 158 - ((com.donglh.narutoninjasaga.e.f.c().i / 3) % 3), a_() + 25, 20);
                if (this.g > 0 && this.B > 1) {
                    if (this.h + 1 >= com.donglh.narutoninjasaga.e.f.c().av.length) {
                        if (this.E == 0 || this.E % 10 > 5) {
                            kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.bf, Input.Keys.NUMPAD_5, a_() + Input.Keys.BUTTON_START, 0, -30976, -16777216);
                        }
                    } else {
                        kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.bd + " " + (this.h + 2), Input.Keys.NUMPAD_5, a_() + Input.Keys.BUTTON_START, 0, -30976, -16777216);
                        kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.aM + " " + ((this.g * 100) / com.donglh.narutoninjasaga.e.f.c().av[this.h + 1]) + "%", Input.Keys.NUMPAD_5, a_() + 124, 0, -30976, -16777216);
                        kk.b(kk.c, lVar, (this.D.b == 0 ? com.donglh.narutoninjasaga.c.a.aN : com.donglh.narutoninjasaga.c.a.aO) + " " + com.donglh.narutoninjasaga.e.aw.c(com.donglh.narutoninjasaga.e.f.c().aq[this.h + 1]), Input.Keys.NUMPAD_5, a_() + 140, 0, -30976, -16777216);
                    }
                }
                a(lVar, this.d[1]);
                for (int i = 0; i < this.d[1].g; i++) {
                    for (int i2 = 0; i2 < this.d[1].o; i2++) {
                        if (i2 % 2 == 0) {
                            b(lVar, i2 * this.d[1].f, i * this.d[1].f, this.b[(i * this.d[1].o) + i2], (i * this.d[1].g) + i2 == this.d[1].i, "");
                        } else if (i2 % 2 == 1) {
                            b(lVar, i2 * this.d[1].f, i * this.d[1].f, this.b[(i * this.d[1].o) + i2], (i * this.d[1].g) + i2 == this.d[1].i, "");
                        } else {
                            b(lVar, i2 * this.d[1].f, i * this.d[1].f, this.b[(i * this.d[1].o) + i2], (i * this.d[1].g) + i2 == this.d[1].i);
                        }
                    }
                }
                b(lVar);
                if (this.c != null) {
                    r0 = this.c;
                    r0.b(lVar, this.d[0].aY + (this.d[0].f / 2), this.d[0].aZ + (this.d[0].f / 2));
                }
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
        }
    }

    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        Vector c = super.c();
        if (this.i.b == 0) {
            for (int i = 0; i < this.d.length; i++) {
                c.addElement(this.d[i].a(i + 1001, this));
            }
        }
        return c;
    }

    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.cn
    public final void a(int i, Object obj, co coVar) {
        if (i == 2000) {
            com.donglh.narutoninjasaga.e.ay.a().a(7, this.F.b.b);
        }
    }

    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        if (this.i.b == q() && guVar.b == 0) {
            d();
        }
        super.a(guVar, i, i2);
        if (this.i.b == 0) {
            switch (guVar.b) {
                case 0:
                    a(true);
                    return;
                case 1001:
                    a(guVar.j, guVar.j.i);
                    this.n = a(guVar, this, this.a);
                    if (this.a == null) {
                        a(1);
                        com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.mn, -1);
                        return;
                    }
                    return;
                case 1002:
                    a(guVar.j, guVar.j.i);
                    this.n = a(guVar, this, this.b[guVar.j.i]);
                    return;
                case 2001:
                    a(false);
                    return;
                case 2002:
                    e();
                    return;
                default:
                    return;
            }
        }
    }

    private void a(fw fwVar, int i) {
        for (int i2 = 0; i2 < this.d.length; i2++) {
            this.d[i2].i = -1;
        }
        fwVar.i = i;
    }

    @Override // com.donglh.narutoninjasaga.d.ej
    public final void d() {
        super.d();
        for (int i = 0; i < this.b.length; i++) {
            if (this.b[i] != null) {
                d.a().W[this.b[i].e] = this.b[i];
                this.b[i] = null;
            }
        }
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x011e: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:54:0x011d */
    private void a(boolean z) {
        Exception a;
        try {
            if (this.B < 2) {
                com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.bg, -65536);
            } else if (this.h + 1 >= com.donglh.narutoninjasaga.e.f.c().av.length) {
                this.E = 50;
            } else {
                boolean z2 = false;
                if (this.D.b == 0) {
                    int i = 0;
                    while (true) {
                        if (i >= this.b.length) {
                            break;
                        } else if (this.b[i] == null || !this.b[i].q) {
                            i++;
                        } else {
                            z2 = true;
                            break;
                        }
                    }
                } else {
                    int i2 = 0;
                    while (true) {
                        if (i2 >= this.b.length) {
                            break;
                        } else if (this.b[i2] == null || this.b[i2].q) {
                            i2++;
                        } else {
                            z2 = true;
                            break;
                        }
                    }
                }
                if (z && z2) {
                    com.donglh.narutoninjasaga.e.f.c().am.a(com.donglh.narutoninjasaga.c.a.bh, 2001, this);
                    return;
                }
                com.donglh.narutoninjasaga.e.ak akVar = new com.donglh.narutoninjasaga.e.ak((byte) 108);
                akVar.a(this.D.b != 0);
                int i3 = 0;
                for (int i4 = 0; i4 < this.b.length; i4++) {
                    if (this.b[i4] != null) {
                        i3++;
                    }
                }
                akVar.a(i3);
                for (int i5 = 0; i5 < this.b.length; i5++) {
                    if (this.b[i5] != null) {
                        akVar.b(this.b[i5].e);
                    }
                }
                akVar.l();
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a);
        }
    }
}
