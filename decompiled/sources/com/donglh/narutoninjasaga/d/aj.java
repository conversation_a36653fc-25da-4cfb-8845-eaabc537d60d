package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.net.HttpStatus;
import java.util.Vector;
/* compiled from: LangLa_ao.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/aj.class */
public final class aj extends he {
    public q[] a;
    public q[] b;
    private fx[] g;
    private dg h;
    public int c;
    public int d;
    public gt e;
    public boolean f;

    public aj(com.donglh.narutoninjasaga.e.ai aiVar) {
        super(aiVar, new String[]{com.donglh.narutoninjasaga.c.a.ot, com.donglh.narutoninjasaga.c.a.F});
        this.a = new q[2];
        this.b = new q[6];
        this.g = new fx[3];
        this.d = 0;
        this.g[0] = new fx((byte) 1, 124, a_() + 20, 30, 30, 30, 1, 1);
        this.g[1] = new fx((byte) 1, 14, a_() + 20, 64, 96, 32, 3, 2);
        this.g[2] = new fx((byte) 1, HttpStatus.SC_NO_CONTENT, a_() + 20, 30, 30, 30, 1, 1);
        this.h = a(this.aM - 70, this.aN - 33, com.donglh.narutoninjasaga.c.a.ba, this, 0, -8);
        a(this.h, 0);
    }

    @Override // com.donglh.narutoninjasaga.d.et
    public final void a() {
        this.A = new q[d.a().W.length];
        int i = 0;
        for (int i2 = 0; i2 < d.a().W.length; i2++) {
            if (d.a().W[i2] != null && d.a().W[i2].M()) {
                this.A[i] = d.a().W[i2];
                i++;
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void b() {
        super.b();
        if (this.i.b == 0) {
            if (!this.f) {
                if (this.a[0] == null) {
                    this.a[1] = null;
                } else {
                    this.a[1] = this.a[0].a();
                    byte b = this.a[1].p;
                    this.a[1].a(0);
                    this.a[1].q = true;
                    s[] N = this.a[1].N();
                    Vector vector = new Vector();
                    String[] strArr = null;
                    if (this.a[1].h().f == 0) {
                        strArr = new String[]{"256,4,-1", "173,20,-1", "253,2000,-1", "164,0,-1"};
                    } else if (this.a[1].h().f == 4) {
                        strArr = new String[]{"257,4,-1", "173,20,-1", "253,2000,-1", "164,0,-1"};
                    } else if (this.a[1].h().f == 3) {
                        strArr = new String[]{"258,25,-1", "173,20,-1", "253,2000,-1", "164,0,-1"};
                    }
                    boolean z = false;
                    boolean z2 = false;
                    for (int i = 0; i < N.length; i++) {
                        if (N[i].a[0] != 148) {
                            if (N[i].a().c == 2 && !z) {
                                vector.add(new s(strArr[0]));
                                vector.add(new s(strArr[1]));
                                z = true;
                            }
                            vector.add(N[i]);
                            if (!z2) {
                                if (this.a[1].h().i / 10 == 4 && N[i].a().c == 6) {
                                    vector.add(new s("42,20,-1"));
                                    z2 = true;
                                }
                                if (N[i].a().c == 7) {
                                    if (this.a[1].h().i / 10 >= 5) {
                                        vector.add(new s(strArr[2]));
                                    }
                                    if (this.a[1].h().i / 10 >= 6) {
                                        this.a[1].b(vector);
                                    }
                                    z2 = true;
                                }
                            }
                        }
                    }
                    vector.add(new s(strArr[3]));
                    this.a[1].h = q.a(vector);
                    this.a[1].a(b);
                }
            }
            for (int i2 = 0; i2 < this.g.length; i2++) {
                this.g[i2].a();
            }
        } else {
            a();
        }
        if (this.e != null) {
            this.e.b();
            if (this.e.h()) {
                this.e = null;
            }
        }
        if (this.d > 0) {
            this.d--;
        }
    }

    @Override // com.donglh.narutoninjasaga.d.el
    public final void a(int i) {
        super.a(i);
        if (i != 0 && this.f) {
            this.f = false;
        }
    }

    @Override // com.donglh.narutoninjasaga.d.he, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        if (q() <= 0) {
            a(lVar, (int) this.aY, (int) this.aZ, (int) this.aM, (int) this.aN, com.donglh.narutoninjasaga.c.a.o, (byte) 2, false);
        } else {
            super.a(lVar);
        }
        if (this.i.b == 0) {
            kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.kr + com.donglh.narutoninjasaga.e.f.c().U[567].b, 14, a_() + 9, 0, -10831436, -16777216);
            kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.ox, 89, a_() + 65, 0, -10831436, -16777216);
            if (this.a[0] != null) {
                int i = 25000000;
                int i2 = (this.a[0].h().i / 10) * 100;
                if (this.a[0].h().i / 10 == 5) {
                    i = 30000000;
                } else if (this.a[0].h().i / 10 == 6) {
                    i = 40000000;
                    i2 = 700;
                }
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.b(com.donglh.narutoninjasaga.c.a.kp, com.donglh.narutoninjasaga.e.aw.c(i2)) + com.donglh.narutoninjasaga.e.f.c().U[567].b, 89, a_() + 80, 0, -10831436, -16777216);
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.b(com.donglh.narutoninjasaga.c.a.om, com.donglh.narutoninjasaga.e.aw.c(i)), 89, a_() + 95, 0, -10831436, -16777216);
            }
            a(lVar, this.g[0].aY, this.g[0].aZ, this.a[0], this.g[0].i >= 0, com.donglh.narutoninjasaga.c.a.F);
            a(lVar, this.g[2].aY, this.g[0].aZ, this.a[1], this.g[2].i >= 0, com.donglh.narutoninjasaga.c.a.G);
            com.donglh.narutoninjasaga.e.r.a(lVar, 95, 0, 170 - ((com.donglh.narutoninjasaga.e.f.c().i / 3) % 3), a_() + 25, 20);
            if (this.d > 0 && this.d % 14 > 2) {
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.kJ + com.donglh.narutoninjasaga.e.f.c().V[164].b, 14, a_() + 125, 0, -2560, -16777216);
            }
            a(lVar, this.g[1]);
            int i3 = 0;
            for (int i4 = 0; i4 < this.g[1].g; i4++) {
                for (int i5 = 0; i5 < this.g[1].o; i5++) {
                    b(lVar, i5 * this.g[1].f, i4 * this.g[1].f, this.b[(i4 * this.g[1].o) + i5], i3 == this.g[1].i, com.donglh.narutoninjasaga.c.a.sY[i5]);
                    i3++;
                }
            }
            b(lVar);
            if (this.e != null) {
                this.e.b(lVar, this.g[2].aY + (this.g[2].f / 2), this.g[2].aZ + (this.g[2].f / 2));
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.he, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        Vector c = super.c();
        if (this.i.b == 0) {
            for (int i = 0; i < this.g.length; i++) {
                c.addElement(this.g[i].a(i + 1001, this));
            }
        }
        return c;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v40, types: [com.donglh.narutoninjasaga.d.q[]] */
    /* JADX WARN: Type inference failed for: r0v41 */
    @Override // com.donglh.narutoninjasaga.d.he, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        if (this.i.b == q() && guVar.b == 0) {
            d();
        }
        super.a(guVar, i, i2);
        if (this.i.b == 0) {
            switch (guVar.b) {
                case 0:
                    int i3 = 0;
                    Exception exc = null;
                    int i4 = 0;
                    while (i4 < this.b.length) {
                        try {
                            ?? r0 = this.b[i4];
                            if (r0 != 0) {
                                i3++;
                            }
                            i4++;
                            exc = r0;
                        } catch (Exception e) {
                            com.donglh.narutoninjasaga.e.aw.a(exc);
                            return;
                        }
                    }
                    if (this.a[0] != null && i3 != 0) {
                        com.donglh.narutoninjasaga.e.ak akVar = new com.donglh.narutoninjasaga.e.ak((byte) -35);
                        akVar.a(this.a[0].r);
                        akVar.b(this.a[0].e);
                        akVar.a(i3);
                        for (int i5 = 0; i5 < this.b.length; i5++) {
                            if (this.b[i5] != null) {
                                akVar.b(this.b[i5].e);
                            }
                        }
                        akVar.l();
                        return;
                    }
                    com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.kr + com.donglh.narutoninjasaga.e.f.c().U[567].b, -65536);
                    return;
                case 1001:
                    this.c = 1;
                    a(guVar.j, guVar.j.i);
                    this.n = a(guVar, this, this.a[0]);
                    return;
                case 1002:
                    this.c = 2;
                    a(guVar.j, guVar.j.i);
                    this.n = a(guVar, this, this.b[guVar.j.i]);
                    return;
                case 1003:
                    this.c = 3;
                    a(guVar.j, guVar.j.i);
                    this.n = a(guVar, this, this.a[1]);
                    return;
                default:
                    return;
            }
        }
    }

    private void a(fw fwVar, int i) {
        for (int i2 = 0; i2 < this.g.length; i2++) {
            this.g[i2].i = -1;
        }
        fwVar.i = i;
    }

    @Override // com.donglh.narutoninjasaga.d.ej
    public final void d() {
        super.d();
        if (this.a[0] != null) {
            d.a().g(this.a[0].r)[this.a[0].e] = this.a[0];
            this.a[0] = null;
        }
        for (int i = 0; i < this.b.length; i++) {
            if (this.b[i] != null) {
                d.a().W[this.b[i].e] = this.b[i];
                this.b[i] = null;
            }
        }
    }
}
