package com.donglh.narutoninjasaga.d;

import java.util.Hashtable;
import java.util.Vector;
/* compiled from: LangLa_mi.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/is.class */
public final class is extends cl {
    private ga a;
    private static com.donglh.narutoninjasaga.e.az b;
    private int c = -1;
    private int d;

    public is(int i, cn cnVar) {
        g(0, i);
        a_(84, 50);
        this.l = cnVar;
        this.d = com.donglh.narutoninjasaga.e.n.n().E << 1;
    }

    public final void a(int i) {
        this.c = i;
        this.a = new ga((byte) 2, 0, 0, this.aM, this.aN, com.donglh.narutoninjasaga.e.n.n().B / this.d, com.donglh.narutoninjasaga.e.n.n().C / this.d);
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        if (!com.donglh.narutoninjasaga.e.ay.a().a(18)) {
            if (this.c >= 0) {
                this.c = -1;
                com.donglh.narutoninjasaga.e.c.b(b);
                try {
                    String str = "mapscale_" + ((int) com.donglh.narutoninjasaga.e.n.n().A);
                    com.donglh.narutoninjasaga.e.az i = com.donglh.narutoninjasaga.e.c.i(str);
                    b = i;
                    if (i == null) {
                        ea eaVar = new ea();
                        eaVar.g = new Hashtable();
                        eaVar.a = com.donglh.narutoninjasaga.e.n.n().A;
                        eaVar.d = "mapscale/" + ((int) com.donglh.narutoninjasaga.e.n.n().A) + ".png";
                        eaVar.e = str;
                        cp.aS.add(eaVar);
                        cp.R();
                    }
                } catch (Exception unused) {
                }
                if (b != null) {
                    com.donglh.narutoninjasaga.e.c.a(b);
                }
            }
            a(lVar, this.aY + this.a.aY, this.aZ + this.a.aZ);
            int i2 = this.a.d;
            int i3 = this.a.n;
            if (i2 < 0) {
                i2 = 0;
            }
            if (i3 < 0) {
                i3 = 0;
            }
            int i4 = i2 + this.a.aM;
            int i5 = i3 + this.a.aN;
            if (i4 > com.donglh.narutoninjasaga.e.r.a(b)) {
                int a = com.donglh.narutoninjasaga.e.r.a(b);
                i4 = a;
                i2 = a - this.a.aM;
            }
            if (i5 > com.donglh.narutoninjasaga.e.r.b(b)) {
                int b2 = com.donglh.narutoninjasaga.e.r.b(b);
                i5 = b2;
                i3 = b2 - this.a.aN;
            }
            int i6 = (i4 - i2) * com.donglh.narutoninjasaga.e.f.c().u;
            int i7 = (i5 - i3) * com.donglh.narutoninjasaga.e.f.c().u;
            if (b != null) {
                if (this.a.aM * com.donglh.narutoninjasaga.e.f.c().u > b.c) {
                    i2 = 0;
                    i6 = b.c;
                }
                if (this.a.aN * com.donglh.narutoninjasaga.e.f.c().u > b.d) {
                    i3 = 0;
                    i7 = b.d;
                }
                lVar.a(b, i2, i3, i6, i7, 0, 0, 0, 0);
            }
            a(lVar, this.aY, this.aZ);
        }
        a(lVar, f(), g());
        a(lVar, this.a);
        if (com.donglh.narutoninjasaga.e.n.n().ay != null) {
            for (int i8 = 0; i8 < com.donglh.narutoninjasaga.e.n.n().ay.a.size(); i8++) {
                d b3 = com.donglh.narutoninjasaga.e.n.n().b(((dw) com.donglh.narutoninjasaga.e.n.n().ay.a.elementAt(i8)).a);
                if (b3 != null && !b3.N.equals(d.a().N)) {
                    int i9 = b3.aY / 8;
                    int i10 = b3.aZ / 8;
                    lVar.e(-16711681);
                    lVar.c(i9 - 2, i10 - 2, 4, 4);
                    kk.b(kk.a, lVar, b3.N, i9, i10 - 7, 2, -16711681, -13553359);
                }
            }
        }
        int i11 = d.a().aY / 8;
        int i12 = d.a().aZ / 8;
        if (com.donglh.narutoninjasaga.e.f.c().i % 30 < 10) {
            lVar.e(-1);
        } else {
            lVar.e(-16742145);
        }
        lVar.c(i11 - 2, i12 - 2, 4, 4);
        b(lVar);
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void b() {
        if (this.a != null) {
            this.a.h = (d.a().aY / this.d) - (this.a.aM / 2);
            this.a.o = (d.a().aZ / this.d) - (this.a.aN / 2);
            this.a.a();
        }
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        return null;
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
    }
}
