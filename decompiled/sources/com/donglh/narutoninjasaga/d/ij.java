package com.donglh.narutoninjasaga.d;

import java.util.Vector;
/* compiled from: LangLa_ly.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/ij.class */
public final class ij extends em {
    private es a;
    private byte b;

    public ij(com.donglh.narutoninjasaga.e.ai aiVar, byte b) {
        this.b = b;
        this.j = (byte) 2;
        this.s = aiVar;
        this.u = false;
        d(220, 125);
        this.a = new es(85, 40, 100, "", this, b);
        this.a.a = 12;
        a(this.a);
        a(com.donglh.narutoninjasaga.c.a.aE, 100);
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a(int i, Object obj, co coVar) {
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a_(int i) {
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        a(lVar, 0, 0, this.aM, this.aN, 80, 55, 56);
        switch (this.b) {
            case 0:
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.fm + com.donglh.narutoninjasaga.e.aw.c(d.a().B) + " " + com.donglh.narutoninjasaga.c.a.rk[2], 10, 28, 0, -1, -16777216);
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.b(com.donglh.narutoninjasaga.c.a.ph, com.donglh.narutoninjasaga.c.a.rk[2]), 10, 50, 0, -1, -16777216);
                return;
            case 1:
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.fm + com.donglh.narutoninjasaga.e.aw.c(d.a().C) + " " + com.donglh.narutoninjasaga.c.a.rk[3], 10, 28, 0, -1, -16777216);
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.b(com.donglh.narutoninjasaga.c.a.ph, com.donglh.narutoninjasaga.c.a.rk[3]), 10, 50, 0, -1, -16777216);
                return;
            case 2:
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.fm + com.donglh.narutoninjasaga.e.aw.c(d.a().E) + " " + com.donglh.narutoninjasaga.c.a.rk[0], 10, 28, 0, -1, -16777216);
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.b(com.donglh.narutoninjasaga.c.a.ph, com.donglh.narutoninjasaga.c.a.rk[0]), 10, 50, 0, -1, -16777216);
                return;
            case 3:
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.fm + com.donglh.narutoninjasaga.e.aw.c(d.a().D) + " " + com.donglh.narutoninjasaga.c.a.rk[1], 10, 28, 0, -1, -16777216);
                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.b(com.donglh.narutoninjasaga.c.a.ph, com.donglh.narutoninjasaga.c.a.rk[1]), 10, 50, 0, -1, -16777216);
                return;
            default:
                return;
        }
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void b() {
    }

    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        super.c();
        Vector vector = new Vector();
        vector.addElement(new gu(0, 0, 0, this.aM, this.aN, this.t, this));
        return vector;
    }

    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        try {
            switch (guVar.b) {
                case 100:
                    try {
                        com.donglh.narutoninjasaga.e.ak b = com.donglh.narutoninjasaga.e.ak.b((byte) -46);
                        b.a((int) this.b);
                        b.c(this.a.j());
                        b.l();
                        p();
                        return;
                    } catch (Exception unused) {
                        com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.fo, -1);
                        return;
                    }
                default:
                    return;
            }
        } catch (Exception unused2) {
        }
    }
}
