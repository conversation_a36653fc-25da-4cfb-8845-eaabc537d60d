package com.donglh.narutoninjasaga.d;
/* compiled from: LangLa_ir.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/gc.class */
public final class gc extends j {
    private boolean a;
    private j b;

    public gc(boolean z, int i, int i2, j jVar) {
        g(i, i2);
        this.a = z;
        this.b = jVar;
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final int d() {
        return 0;
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final int e() {
        return 0;
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final int f() {
        return 0;
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final int g() {
        return 0;
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final void a(l lVar) {
        if (!com.donglh.narutoninjasaga.e.aw.f()) {
            if (this.b instanceof d) {
                d dVar = (d) this.b;
                if ((!dVar.c() && dVar.k) || dVar.L()) {
                    return;
                }
            }
            int i = 642;
            int i2 = 100;
            int i3 = 0;
            if (this.b instanceof jq) {
                jq jqVar = (jq) this.b;
                if (jqVar.h().e != 255) {
                    if (jqVar.h().e == 256) {
                        i = 21;
                        i2 = 75;
                    } else if (jqVar.c != 8 && jqVar.a != 265 && jqVar.a != 266 && jqVar.a != 263 && jqVar.a != 276 && jqVar.a != 278) {
                        if (jqVar.a == 163) {
                            i = 21;
                            i2 = 62;
                            i3 = 0 - 5;
                        } else if (jqVar.a == 188) {
                            i = 21;
                            i3 = 0 - 2;
                        } else if (jqVar.a != 179 && jqVar.a != 173 && jqVar.a != 183 && jqVar.a != 273 && jqVar.a != 275 && jqVar.a != 277 && jqVar.a != 274) {
                            if (jqVar.a != 180 && jqVar.a != 120 && jqVar.a != 66 && jqVar.a != 176 && jqVar.a != 121 && jqVar.a != 182 && jqVar.a != 262) {
                                if (jqVar.a != 166 && jqVar.a != 178 && jqVar.a != 185 && jqVar.a != 97 && jqVar.a != 264) {
                                    if (jqVar.a == 189) {
                                        i = 21;
                                        i2 = 88;
                                    } else if (jqVar.a == 190) {
                                        i = 21;
                                        i2 = 130;
                                    } else if (jqVar.a == 192) {
                                        i = 21;
                                        i2 = 110;
                                    } else if (jqVar.a == 193) {
                                        i = 21;
                                        i2 = 115;
                                    } else if (jqVar.a >= 285 && jqVar.a <= 289) {
                                        i2 = 105;
                                    } else if (jqVar.a == 172 || jqVar.a == 178 || jqVar.a == 236 || jqVar.c == 9 || (jqVar.h().o == 3 && (!com.donglh.narutoninjasaga.e.n.n().M() || com.donglh.narutoninjasaga.e.n.n().s().d == 16))) {
                                        i = 21;
                                    }
                                } else {
                                    i = 21;
                                    i2 = 70;
                                }
                            } else {
                                i = 21;
                                i2 = 62;
                                i3 = 0 - 2;
                            }
                        } else {
                            i = 21;
                            i2 = 52;
                        }
                    }
                }
                i2 = 120;
            }
            ki b = d.b(this.aY, this.aZ);
            if (b != null) {
                this.aZ = b.aZ;
            }
            a.a(lVar, i, 0, (int) this.aY, this.aZ + i3, i2, (byte) 3);
        }
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final String A() {
        return null;
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final String B() {
        return null;
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final String C() {
        return null;
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final void b(l lVar) {
    }

    @Override // com.donglh.narutoninjasaga.d.j
    public final int N() {
        return 4;
    }
}
