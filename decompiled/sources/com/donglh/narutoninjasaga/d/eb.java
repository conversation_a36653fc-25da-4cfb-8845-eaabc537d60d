package com.donglh.narutoninjasaga.d;

import java.util.Vector;
/* compiled from: LangLa_fc.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/eb.class */
public final class eb extends em {
    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a(int i, Object obj, co coVar) {
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a_(int i) {
    }

    /* JADX WARN: Incorrect condition in loop: B:4:0x0034 */
    /* JADX WARN: Incorrect condition in loop: B:7:0x003e */
    @Override // com.donglh.narutoninjasaga.d.co
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void a(com.donglh.narutoninjasaga.d.l r11) {
        /*
            r10 = this;
            r0 = r10
            r1 = r11
            r2 = 0
            r3 = 0
            r4 = r10
            short r4 = r4.aM
            r5 = r10
            short r5 = r5.aN
            r6 = 80
            r7 = 55
            r8 = 56
            r0.a(r1, r2, r3, r4, r5, r6, r7, r8)
            com.donglh.narutoninjasaga.d.kk r0 = com.donglh.narutoninjasaga.d.kk.c
            r1 = r11
            java.lang.String r2 = com.donglh.narutoninjasaga.c.a.qq
            r3 = 10
            r4 = 25
            r5 = 0
            r6 = -1
            r7 = -16777216(0xffffffffff000000, float:-1.7014118E38)
            com.donglh.narutoninjasaga.d.kk.b(r0, r1, r2, r3, r4, r5, r6, r7)
            r0 = r10
            r1 = r11
            r2 = 0
            r0.a(r1, r2)
            r0 = 0
            r12 = r0
        L2f:
            r0 = r12
            r1 = 0
            int r1 = r1.o
            if (r0 >= r1) goto L96
            r0 = 0
            r13 = r0
        L39:
            r0 = r13
            r1 = 0
            int r1 = r1.g
            if (r0 >= r1) goto L90
            r0 = 0
            r1 = r13
            boolean r0 = r0.b(r1)
            if (r0 == 0) goto L8a
            r0 = r13
            r1 = 0
            int r1 = r1.f
            int r0 = r0 * r1
            r14 = r0
            r0 = r12
            r1 = 0
            int r1 = r1.f
            int r0 = r0 * r1
            r15 = r0
            r0 = r12
            r1 = 0
            int r1 = r1.g
            int r0 = r0 * r1
            r16 = r0
            r0 = r11
            r1 = r14
            r2 = r15
            com.donglh.narutoninjasaga.d.d r3 = com.donglh.narutoninjasaga.d.d.a()
            com.donglh.narutoninjasaga.d.q[] r3 = r3.aa
            r4 = r16
            r5 = r13
            int r4 = r4 + r5
            r3 = r3[r4]
            r4 = r12
            r5 = 0
            int r5 = r5.g
            int r4 = r4 * r5
            r5 = r13
            int r4 = r4 + r5
            r5 = 0
            int r5 = r5.i
            if (r4 != r5) goto L84
            r4 = 1
            goto L85
        L84:
            r4 = 0
        L85:
            r5 = 54
            a(r0, r1, r2, r3, r4, r5)
        L8a:
            int r13 = r13 + 1
            goto L39
        L90:
            int r12 = r12 + 1
            goto L2f
        L96:
            r0 = r10
            r1 = r11
            r0.b(r1)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: com.donglh.narutoninjasaga.d.eb.a(com.donglh.narutoninjasaga.d.l):void");
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void b() {
        fw fwVar = null;
        fwVar.a();
    }

    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        super.c();
        Vector vector = new Vector();
        fw fwVar = null;
        vector.addElement(fwVar.a(1001, this));
        vector.addElement(new gu(0, 0, 0, this.aM, this.aN, this.t, this));
        return vector;
    }

    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        switch (guVar.b) {
            case 1001:
                this.n = a(guVar, this, d.a().aa[guVar.j.i]);
                return;
            case 1002:
                p();
                return;
            default:
                return;
        }
    }
}
