package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.Input;
import com.badlogic.gdx.net.HttpStatus;
import java.util.Vector;
/* compiled from: LangLa_v.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/jk.class */
public final class jk extends et {
    public ew[] a;
    private fw h;
    private fw B;
    private fw C;
    private fw[] D;
    private fw[] E;
    private fw[] F;
    private dg[] G;
    private dg[] H;
    private dg[] I;
    private fw J;
    public int b;
    public hw c;
    public q d;
    private String[] K;
    private int[] L;
    private cd[] M;
    public es e;
    public int f;
    public int g;
    private gy N;
    private ew O;

    public jk(com.donglh.narutoninjasaga.e.ai aiVar) {
        super(aiVar, new String[]{com.donglh.narutoninjasaga.c.a.bn});
        this.b = -1;
        this.K = com.donglh.narutoninjasaga.c.a.rt;
        this.L = new int[]{10, 20, 30, 60, 100};
        this.M = new cd[2];
        d(this.aM, this.aN + 20);
        com.donglh.narutoninjasaga.e.n.n().au = new ew[0];
        com.donglh.narutoninjasaga.e.n.n().at = new ew[0];
        this.c = new hw(new gz(1001, this.K), 4, a_(), this.aM - 8, 84, this);
        cd[] cdVarArr = this.M;
        String[][] strArr = com.donglh.narutoninjasaga.e.f.c().az;
        com.donglh.narutoninjasaga.e.f.c();
        cdVarArr[0] = new cd(10, 73, 85, 7, new gz(1000, strArr[0]), this, 0, true);
        this.c.a(this.M[0], 0);
        this.N = new gy(1000, com.donglh.narutoninjasaga.e.n.n().ap);
        this.c.a(new ci(10, 111, com.donglh.narutoninjasaga.c.a.hF, this, this.N), 0);
        this.c.a(new ci(10, Input.Keys.CONTROL_RIGHT, com.donglh.narutoninjasaga.c.a.hG, this, this.N), 0);
        this.c.a(new ci(10, Input.Keys.NUMPAD_5, com.donglh.narutoninjasaga.c.a.hH, this, this.N), 0);
        this.c.a(new ci(10, 168, com.donglh.narutoninjasaga.c.a.hI, this, this.N), 0);
        this.c.a(new ci(10, 187, com.donglh.narutoninjasaga.c.a.hJ, this, this.N), 0);
        this.c.a(new ci(10, HttpStatus.SC_PARTIAL_CONTENT, com.donglh.narutoninjasaga.c.a.nQ, this, this.N), 0);
        dg dgVar = new dg(171, (this.aN - 33) + 14, "", this, 1012, 286);
        dgVar.e = -360;
        this.c.a(dgVar, 0);
        dg dgVar2 = new dg(Input.Keys.NUMPAD_2, (this.aN - 33) + 14, "", this, 1011, 287);
        dgVar2.e = -360;
        this.c.a(dgVar2, 0);
        this.c.a(new dg(218, (this.aN - 33) + 14, "", this, 1013, 286), 0);
        this.c.a(new dg(Input.Keys.COLON, (this.aN - 33) + 14, "", this, 1014, 287), 0);
        w();
        this.J = new fw((byte) 0, 11, a_() + 32, 28, 28, 28, 1);
        dg dgVar3 = new dg(45, 64, com.donglh.narutoninjasaga.c.a.aj, this, 1010, 15);
        dgVar3.a_(45, 22);
        this.c.a(dgVar3, 1);
        this.e = new es(10, Input.Keys.BUTTON_R2, 80, "", this, 0);
        this.c.a(this.e, 1);
        gz gzVar = new gz(HttpStatus.SC_OK);
        gzVar.c = com.donglh.narutoninjasaga.c.a.qR;
        gzVar.b = 0;
        this.M[1] = new cd(10, 165, 80, 5, gzVar, this, 1, true);
        this.c.a(this.M[1], 1);
        x();
        this.c.b(s());
        a(0);
        e();
        u();
    }

    @Override // com.donglh.narutoninjasaga.d.et
    public final void a() {
        this.A = new q[d.a().W.length];
        int i = 0;
        for (int i2 = 0; i2 < d.a().W.length; i2++) {
            if (d.a().W[i2] != null && !d.a().W[i2].q && d.a().W[i2].g == -1) {
                this.A[i] = d.a().W[i2];
                i++;
            }
        }
    }

    /* JADX WARN: Type inference failed for: r0v1, types: [com.donglh.narutoninjasaga.e.ak, java.lang.Exception] */
    public final void e() {
        ?? akVar;
        try {
            akVar = new com.donglh.narutoninjasaga.e.ak((byte) 101);
            akVar.a(this.M[0].b.b);
            akVar.a(com.donglh.narutoninjasaga.e.n.n().ap);
            akVar.b(this.f);
            akVar.l();
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) akVar);
        }
    }

    /* JADX WARN: Type inference failed for: r0v1, types: [com.donglh.narutoninjasaga.e.ak, java.lang.Exception] */
    private void b(int i) {
        ?? akVar;
        try {
            akVar = new com.donglh.narutoninjasaga.e.ak((byte) 101);
            akVar.a(this.M[0].b.b);
            akVar.a(this.N.b);
            akVar.b(i);
            akVar.l();
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) akVar);
        }
    }

    /* JADX WARN: Type inference failed for: r0v1, types: [com.donglh.narutoninjasaga.e.ak, java.lang.Exception] */
    public static void u() {
        ?? akVar;
        try {
            akVar = new com.donglh.narutoninjasaga.e.ak((byte) 100);
            akVar.l();
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) akVar);
        }
    }

    public final int v() {
        return this.c.b.b;
    }

    public final void w() {
        this.h = new fw((byte) 1, 100, 72, this.aM - Input.Keys.BUTTON_L2, 136, 34, com.donglh.narutoninjasaga.e.n.n().au.length);
        this.G = new dg[com.donglh.narutoninjasaga.e.n.n().au.length];
        this.D = new gb[com.donglh.narutoninjasaga.e.n.n().au.length];
        for (int i = 0; i < com.donglh.narutoninjasaga.e.n.n().au.length; i++) {
            this.G[i] = new dg(164, 6 + (i * this.h.f), "", this, 1000, -5);
            this.G[i].a_(26, 20);
            this.D[i] = new gb(0, 0, this.G[i].aM, this.G[i].aN, this.G[i].aN, this.h);
        }
        y();
    }

    public final void x() {
        this.B = new fw((byte) 1, 100, 72, this.aM - Input.Keys.BUTTON_L2, 136, 34, com.donglh.narutoninjasaga.e.n.n().at.length);
        this.H = new dg[com.donglh.narutoninjasaga.e.n.n().at.length];
        this.E = new gb[com.donglh.narutoninjasaga.e.n.n().at.length];
        for (int i = 0; i < com.donglh.narutoninjasaga.e.n.n().at.length; i++) {
            this.H[i] = new dg(164, 6 + (i * this.B.f), "", this, 1000, -5);
            this.H[i].a_(26, 20);
            this.E[i] = new gb(0, 0, this.H[i].aM, this.H[i].aN, this.H[i].aN, this.B);
        }
        y();
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x006d: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:16:0x006c */
    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void b() {
        Exception a;
        try {
            super.b();
            if (this.i.b != 0) {
                a();
                return;
            }
            this.c.b();
            this.C.a();
            for (int i = 0; i < this.I.length; i++) {
                this.I[i].b();
                this.F[i].a();
                if (this.F[i].j != -1) {
                    this.I[i].f = true;
                } else {
                    this.I[i].f = false;
                }
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a);
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v0, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v10, types: [com.donglh.narutoninjasaga.d.jk] */
    /* JADX WARN: Type inference failed for: r0v11, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v45, types: [com.donglh.narutoninjasaga.d.jk] */
    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        ?? r0;
        try {
            super.a(lVar);
            if (this.i.b == 0) {
                this.c.a(lVar);
                a(lVar, this.aY + 4, this.aZ + a_() + 22);
                a(lVar, -3, 0, 100, ((this.aN - 33) - a_()) + 10, 4, 55, 56);
                r0 = this;
                try {
                    r0.b(lVar, r0.C);
                    lVar.e(7049372);
                    if (r0.c.b.b == 0) {
                        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.hP, 5, -10, 0, -6488, -10275328);
                    } else {
                        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.hQ, 5, -10, 0, -6488, -10275328);
                    }
                    kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.hR, 114, -10, 0, -6488, -10275328);
                    kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.hS, 166, -10, 0, -6488, -10275328);
                    if (r0.c.b.b == 0) {
                        kk.b(kk.e, lVar, new StringBuilder().append(r0.f + 1).toString(), Input.Keys.BUTTON_R1, Input.Keys.NUMPAD_2, 2, -1, -16777216);
                    }
                    r0.a(lVar, r0.C);
                    int i = 0;
                    while (i < r0.C.g) {
                        if (r0.C.b(i)) {
                            r0.a(lVar, 2, i * r0.C.f, r0.C.aM - 4, r0.C.f - 1, -13, 84, 5, 1, 1);
                            b(lVar, 5, 2 + (i * r0.C.f), r0.a[i].e, r0.b == i);
                            kk.b(kk.c, lVar, r0.a[i].b, 38, 10 + (i * r0.C.f), 0, -1, -16777216);
                            kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.ah + " " + com.donglh.narutoninjasaga.e.aw.c(r0.a[i].c), 38, 23 + (i * r0.C.f), 0, -3089954, -16777216);
                            int a = (int) (r0.a[i].d - (com.donglh.narutoninjasaga.e.aw.a() / 1000));
                            if (a >= 60) {
                                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.bp, 137, 9 + (i * r0.C.f), 2, -6488, -10275328);
                                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.f(a), 137, 21 + (i * r0.C.f), 2, -6488, -10275328);
                            } else {
                                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.ru[0], 137, 9 + (i * r0.C.f), 2, -6488, -10275328);
                                kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.ru[1], 137, 21 + (i * r0.C.f), 2, -6488, -10275328);
                            }
                            int i2 = lVar.a;
                            int i3 = lVar.b;
                            r0.a(lVar, i2 + r0.I[i].aY, i3 + r0.I[i].aZ);
                            r0.I[i].a(lVar);
                            r0.a(lVar, i2, i3);
                        }
                        i++;
                    }
                    r0 = r0;
                    r0.b(lVar);
                } catch (Exception e) {
                    com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                }
                a(lVar, this.aY, this.aZ);
                switch (this.c.b.b) {
                    case 0:
                        a(lVar, this.aY + 4, this.aZ + a_());
                        kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.hK, 6, 35, 0, -6488, -10275328);
                        kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.hL, 6, 72, 0, -6488, -10275328);
                        return;
                    case 1:
                        a(lVar, (int) this.J.aY, (int) this.J.aZ, this.d, false);
                        a(lVar, this.aY + 4, this.aZ + a_());
                        kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.hM, 6, 67, 0, -6488, -10275328);
                        kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.hN, 6, 127, 0, -6488, -10275328);
                        kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.hO, 6, 167, 0, -6488, -10275328);
                        kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.nN, 6, 179, 0, -3089954, -16777216);
                        kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.nO, 6, 191, 0, -3089954, -16777216);
                        kk.b(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.c(this.L[this.M[1].b.b]), 26, 167, 0, -3089954, -16777216);
                        a(lVar, 26, 167, com.donglh.narutoninjasaga.e.aw.c(this.L[this.M[1].b.b]), (byte) 0);
                        return;
                    default:
                        return;
                }
            }
        } catch (Exception e2) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
        }
    }

    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        Vector c = super.c();
        if (this.i.b == 0) {
            for (int i = 0; i < this.I.length; i++) {
                int i2 = this.C.aY + this.I[i].aY;
                int i3 = (this.C.aZ + this.I[i].aZ) - this.C.d;
                if (com.donglh.narutoninjasaga.e.aw.a((int) this.C.aY, (int) this.C.aZ, this.C.aY + this.C.aM, this.C.aZ + this.C.aN, i2, i3) && com.donglh.narutoninjasaga.e.aw.a((int) this.C.aY, (int) this.C.aZ, this.C.aY + this.C.aM, this.C.aZ + this.C.aN, i2 + this.I[i].aM, i3 + this.I[i].aN)) {
                    this.F[i].aY = (short) i2;
                    this.F[i].aZ = (short) i3;
                    c.addElement(new gu(i + 7000, this.F[i].aY, this.F[i].aZ, this.F[i].aY + this.F[i].aM, this.F[i].aZ + this.F[i].aN, this.F[i], this));
                    c.addElement(new gu(i + 8000, i2 - 161, i3 - 4, (i2 - 161) + 28, (i3 - 4) + 28, this.C, this));
                }
            }
            c.addElement(this.c.c());
            c.addElement(this.C.a(1003, this));
            switch (this.c.b.b) {
                case 1:
                    c.addElement(this.J.a(1002, this));
                    break;
            }
        }
        return c;
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        if (this.i.b == q() && guVar.b == 0) {
            d();
        }
        super.a(guVar, i, i2);
        if (this.i.b == 0) {
            if (guVar.b == 2000) {
                a(false, this.a[this.b]);
            }
            this.b = -1;
            if (guVar.b >= 7000 && guVar.b < 8000) {
                this.g = 1;
                this.b = guVar.b - 7000;
                a(true, this.a[this.b]);
            } else if (guVar.b >= 8000 && guVar.b < 9000) {
                this.g = 2;
                this.b = guVar.b - 8000;
                this.n = a(this, ((this.C.aY + this.I[this.b].aY) - 161) + 30, ((this.C.aZ + this.I[this.b].aZ) - this.C.d) - 4, 28, this.a[this.b].e);
            }
            switch (guVar.b) {
                case 1001:
                    if (guVar.j.i >= 0) {
                        this.c.a(guVar.j.i);
                        y();
                        return;
                    }
                    return;
                case 1002:
                    this.g = 3;
                    if (this.d == null) {
                        a(1);
                        com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.bo, -1);
                        return;
                    }
                    this.n = a(this, guVar.j.aY + 32, guVar.j.aZ, 28, this.d);
                    return;
                case 1010:
                    try {
                        if (this.d == null) {
                            a(1);
                            com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.bo, -1);
                            return;
                        } else if (this.e.j() > 0) {
                            if (d.a().B < this.L[this.M[1].b.b]) {
                                com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.hU, -65536);
                                return;
                            }
                            com.donglh.narutoninjasaga.e.ak akVar = new com.donglh.narutoninjasaga.e.ak((byte) 99);
                            akVar.b(this.d.e);
                            akVar.a(this.M[1].b.b);
                            akVar.c(this.e.j());
                            akVar.l();
                            return;
                        } else {
                            com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.hT, -65536);
                            return;
                        }
                    } catch (Exception e) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) this);
                        return;
                    }
                case 1011:
                    this.f = 0;
                    b(0);
                    return;
                case 1012:
                    int i3 = this.f - 1;
                    int i4 = i3;
                    if (i3 < 0) {
                        i4 = 0;
                    }
                    b(i4);
                    return;
                case 1013:
                    b(this.f + 1);
                    return;
                case 1014:
                    b(32767);
                    return;
                case 2010:
                    b(false, this.O);
                    return;
                default:
                    return;
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.cn
    public final void a(int i, Object obj, co coVar) {
        super.a(i, obj, coVar);
        switch (i) {
            case 1000:
                this.f = 0;
                com.donglh.narutoninjasaga.e.n.n().ap = this.N.b;
                e();
                return;
            default:
                return;
        }
    }

    @Override // com.donglh.narutoninjasaga.d.el
    public final void a(int i) {
        super.a(i);
        if (this.c != null && this.i.b == 0) {
            this.c.a(this.c.b.b);
            y();
        }
    }

    public final void y() {
        if (this.c != null) {
            switch (this.c.b.b) {
                case 0:
                    this.a = com.donglh.narutoninjasaga.e.n.n().au;
                    this.C = this.h;
                    this.F = this.D;
                    this.I = this.G;
                    return;
                case 1:
                    this.a = com.donglh.narutoninjasaga.e.n.n().at;
                    this.C = this.B;
                    this.F = this.E;
                    this.I = this.H;
                    return;
                default:
                    return;
            }
        }
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x0035: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:8:0x0034 */
    private void b(boolean z, ew ewVar) {
        Exception a;
        try {
            this.O = ewVar;
            if (z) {
                com.donglh.narutoninjasaga.e.f.c().am.a(com.donglh.narutoninjasaga.c.a.hV, 2010, this);
                return;
            }
            com.donglh.narutoninjasaga.e.ak akVar = new com.donglh.narutoninjasaga.e.ak((byte) -19);
            akVar.a(ewVar.a);
            akVar.l();
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a);
        }
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x007b: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:11:0x007a */
    public final void a(boolean z, ew ewVar) {
        Exception a;
        try {
            if (d.a().B < ewVar.c) {
                com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.hW, -65536);
            } else if (z) {
                com.donglh.narutoninjasaga.e.f.c().am.a(com.donglh.narutoninjasaga.c.a.hX + ewVar.e.h().b + com.donglh.narutoninjasaga.c.a.hY + ewVar.c + com.donglh.narutoninjasaga.c.a.hZ, 2000, this);
            } else {
                com.donglh.narutoninjasaga.e.ak akVar = new com.donglh.narutoninjasaga.e.ak((byte) 98);
                akVar.a(ewVar.a);
                akVar.l();
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a);
        }
    }

    public final void a(boolean z) {
        b(z, this.a[this.b]);
    }

    @Override // com.donglh.narutoninjasaga.d.ej
    public final void d() {
        super.d();
        if (this.d != null) {
            d.a().W[this.d.e] = this.d;
            this.d = null;
        }
    }
}
