package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.Application;
import com.badlogic.gdx.Gdx;
import com.badlogic.gdx.net.HttpStatus;
import java.util.Vector;
/* compiled from: LangLa_q.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/jf.class */
public final class jf extends ej {
    public jg a;
    private boolean c;
    private long d;
    private int e;
    public static boolean b;

    public jf(com.donglh.narutoninjasaga.e.ai aiVar) {
        this.s = aiVar;
        a_((((com.donglh.narutoninjasaga.e.f.c().o - 240) / 14) * 14) + 192, 26);
        g((com.donglh.narutoninjasaga.e.f.c().o / 2) - (this.aM / 2), com.donglh.narutoninjasaga.e.f.c().p - 30);
        this.a = new jg(aiVar);
        this.a.a_(this.aM, this.aN);
        this.a.b = HttpStatus.SC_OK;
        this.e = this.a.q();
        this.d = com.donglh.narutoninjasaga.e.aw.a();
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        if (!com.donglh.narutoninjasaga.e.f.an || Gdx.app.getType() != Application.ApplicationType.iOS) {
            this.a.b(lVar, this.aY, this.aZ);
        }
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void b() {
        this.a.b();
        if (this.c) {
            com.donglh.narutoninjasaga.e.f.ao = true;
            com.donglh.narutoninjasaga.e.ao n = jp.n();
            com.donglh.narutoninjasaga.e.f.c().a(n);
            com.donglh.narutoninjasaga.e.ay.a().a(17, true);
            if (!com.donglh.narutoninjasaga.e.ay.a().a(8)) {
                n.a(new az(n));
            }
            if (b) {
                b = false;
                return;
            }
            com.donglh.narutoninjasaga.e.a.f();
            com.donglh.narutoninjasaga.e.a.f();
            return;
        }
        if (this.a.a >= this.a.b) {
            this.c = true;
        }
        if (this.a.q() != this.e) {
            this.e = this.a.q();
            this.d = com.donglh.narutoninjasaga.e.aw.a();
        }
    }

    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        return new Vector();
    }

    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a(int i, Object obj, co coVar) {
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a_(int i) {
    }
}
