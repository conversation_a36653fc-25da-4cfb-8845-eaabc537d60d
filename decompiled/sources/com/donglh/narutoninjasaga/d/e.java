package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.Application;
import com.badlogic.gdx.Gdx;
import com.badlogic.gdx.Input;
import java.io.File;
import java.util.Vector;
/* compiled from: Controller.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/e.class */
public final class e implements o {
    public static e a;
    private bw b = new bw();
    private static boolean c;

    /* JADX WARN: Code restructure failed: missing block: B:20:0x007c, code lost:
        if (r0.f.toLowerCase().equals(com.donglh.narutoninjasaga.c.a.me.toLowerCase()) != false) goto L21;
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x007f, code lost:
        com.donglh.narutoninjasaga.e.f.c().a = r0.c;
        com.donglh.narutoninjasaga.e.f.c().b = r0.d;
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x0095, code lost:
        r0 = r0.k.add(java.lang.Integer.valueOf(r6));
     */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x00a1, code lost:
        return;
     */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v16, types: [java.lang.Exception] */
    @Override // com.donglh.narutoninjasaga.d.o
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void a() {
        /*
            Method dump skipped, instructions count: 261
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: com.donglh.narutoninjasaga.d.e.a():void");
    }

    @Override // com.donglh.narutoninjasaga.d.o
    public final void b() {
        if (c) {
            com.donglh.narutoninjasaga.e.a.f();
            com.donglh.narutoninjasaga.e.a.i();
        }
        com.donglh.narutoninjasaga.e.aq.a().f.clear();
        fg a2 = fg.a();
        a2.b = null;
        a2.a.clear();
        gm a3 = gm.a();
        a3.b = null;
        a3.a.clear();
        go a4 = go.a();
        a4.b = null;
        a4.a.clear();
        gq a5 = gq.a();
        a5.b = null;
        a5.a.clear();
        if (!com.donglh.narutoninjasaga.e.f.ao) {
            if (com.donglh.narutoninjasaga.e.n.n().ay != null) {
                com.donglh.narutoninjasaga.e.n.n().ay.a.clear();
            }
            if (com.donglh.narutoninjasaga.e.f.c().aD) {
                com.donglh.narutoninjasaga.e.f.c().aD = false;
            } else if (d.a().aE > 0) {
                com.donglh.narutoninjasaga.e.f.aH = 1;
                com.donglh.narutoninjasaga.e.f.c().a(jp.o());
            } else {
                com.donglh.narutoninjasaga.e.ai aiVar = com.donglh.narutoninjasaga.e.f.c().am;
                if (!aiVar.cf.contains(this.b)) {
                    this.b.a(com.donglh.narutoninjasaga.c.a.b, aiVar);
                    aiVar.a(this.b);
                }
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.o
    public final void c() {
        com.donglh.narutoninjasaga.e.a.j = true;
        if (com.donglh.narutoninjasaga.e.f.ao) {
            com.donglh.narutoninjasaga.e.f.ao = false;
        }
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v0, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v129, types: [com.donglh.narutoninjasaga.e.n] */
    /* JADX WARN: Type inference failed for: r0v130, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v139, types: [com.donglh.narutoninjasaga.d.ec] */
    /* JADX WARN: Type inference failed for: r0v142, types: [com.donglh.narutoninjasaga.e.n] */
    /* JADX WARN: Type inference failed for: r0v143, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v156, types: [com.donglh.narutoninjasaga.d.ec] */
    /* JADX WARN: Type inference failed for: r0v185, types: [com.donglh.narutoninjasaga.e.ai, com.donglh.narutoninjasaga.e.n, java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v222, types: [com.donglh.narutoninjasaga.e.n] */
    /* JADX WARN: Type inference failed for: r0v223, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v233, types: [com.donglh.narutoninjasaga.d.bx] */
    /* JADX WARN: Type inference failed for: r0v237, types: [com.donglh.narutoninjasaga.e.n, com.donglh.narutoninjasaga.d.co] */
    /* JADX WARN: Type inference failed for: r0v238, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v246, types: [com.donglh.narutoninjasaga.e.ai] */
    /* JADX WARN: Type inference failed for: r0v260, types: [com.donglh.narutoninjasaga.d.co, java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v281, types: [com.donglh.narutoninjasaga.e.n, com.donglh.narutoninjasaga.d.co] */
    /* JADX WARN: Type inference failed for: r0v282, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v290, types: [com.donglh.narutoninjasaga.e.ai] */
    /* JADX WARN: Type inference failed for: r0v365, types: [com.donglh.narutoninjasaga.e.ai, com.donglh.narutoninjasaga.e.n] */
    /* JADX WARN: Type inference failed for: r0v366, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v382, types: [com.donglh.narutoninjasaga.e.n] */
    /* JADX WARN: Type inference failed for: r0v392, types: [com.donglh.narutoninjasaga.e.ai] */
    /* JADX WARN: Type inference failed for: r0v393, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v402, types: [com.donglh.narutoninjasaga.e.n] */
    /* JADX WARN: Type inference failed for: r0v409, types: [com.donglh.narutoninjasaga.e.n] */
    /* JADX WARN: Type inference failed for: r0v410, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v423, types: [com.donglh.narutoninjasaga.e.n] */
    /* JADX WARN: Type inference failed for: r0v432, types: [com.donglh.narutoninjasaga.e.n, java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v450, types: [com.donglh.narutoninjasaga.e.ai] */
    /* JADX WARN: Type inference failed for: r0v451, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v462, types: [com.donglh.narutoninjasaga.e.n] */
    /* JADX WARN: Type inference failed for: r0v470, types: [com.donglh.narutoninjasaga.e.n, java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v490, types: [com.donglh.narutoninjasaga.e.n, java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v540, types: [com.donglh.narutoninjasaga.e.n, java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v626, types: [com.donglh.narutoninjasaga.e.n] */
    /* JADX WARN: Type inference failed for: r0v627, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v631, types: [java.util.Vector] */
    /* JADX WARN: Type inference failed for: r0v652, types: [com.donglh.narutoninjasaga.e.n, java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v668, types: [com.donglh.narutoninjasaga.e.n, java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v690, types: [com.donglh.narutoninjasaga.e.n] */
    /* JADX WARN: Type inference failed for: r0v691, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v694, types: [com.donglh.narutoninjasaga.e.n] */
    /* JADX WARN: Type inference failed for: r0v698 */
    /* JADX WARN: Type inference failed for: r0v699, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v722, types: [com.donglh.narutoninjasaga.e.n] */
    /* JADX WARN: Type inference failed for: r0v729, types: [com.donglh.narutoninjasaga.d.co] */
    /* JADX WARN: Type inference failed for: r0v730, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v734, types: [com.donglh.narutoninjasaga.e.ai] */
    /* JADX WARN: Type inference failed for: r0v747, types: [com.donglh.narutoninjasaga.e.ak] */
    /* JADX WARN: Type inference failed for: r0v748, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v749, types: [com.donglh.narutoninjasaga.e.f] */
    /* JADX WARN: Type inference failed for: r0v899, types: [com.donglh.narutoninjasaga.e.ak, java.lang.Exception] */
    @Override // com.donglh.narutoninjasaga.d.o
    public final void a(com.donglh.narutoninjasaga.e.ak akVar) {
        ?? r0;
        ew ewVar;
        String[] a2;
        String[] strArr;
        byte[] m;
        String str;
        ce ceVar;
        try {
            switch (akVar.a) {
                case -125:
                    akVar.a = akVar.e();
                    r0 = akVar;
                    try {
                        switch (r0.a) {
                            case Byte.MIN_VALUE:
                                com.donglh.narutoninjasaga.e.f.c().g = true;
                                com.donglh.narutoninjasaga.e.f.c().am.a(com.donglh.narutoninjasaga.e.c.a(r0.b.b()));
                                return;
                            case -126:
                                com.donglh.narutoninjasaga.e.f.c().am.n(4);
                                com.donglh.narutoninjasaga.e.f.c().g = false;
                                return;
                            default:
                                return;
                        }
                    } catch (Exception e) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r0, "cons: " + ((int) r0.a));
                        return;
                    }
                case -124:
                    akVar.a = akVar.e();
                    b(akVar);
                    return;
                case -123:
                    akVar.a = akVar.e();
                    c(akVar);
                    return;
                case -122:
                    akVar.a = akVar.e();
                    e(akVar);
                    return;
                case -121:
                case -120:
                case -119:
                case -118:
                case -117:
                case -116:
                case -115:
                case -114:
                case -112:
                case -111:
                case -100:
                case -92:
                case -91:
                case -90:
                case -86:
                case -84:
                case -83:
                case -82:
                case -80:
                case -79:
                case -78:
                case -77:
                case -76:
                case -75:
                case -74:
                case -73:
                case -72:
                case -71:
                case -70:
                case -69:
                case -68:
                case -67:
                case -66:
                case -65:
                case -64:
                case -63:
                case -62:
                case -61:
                case -56:
                case -42:
                case -41:
                case -40:
                case -38:
                case -37:
                case -34:
                case -33:
                case -20:
                case -7:
                case 8:
                case 9:
                case 11:
                case 14:
                case Input.Keys.B /* 30 */:
                case Input.Keys.J /* 38 */:
                case Input.Keys.L /* 40 */:
                case Input.Keys.N /* 42 */:
                case Input.Keys.R /* 46 */:
                case Input.Keys.S /* 47 */:
                case Input.Keys.T /* 48 */:
                case Input.Keys.Y /* 53 */:
                case Input.Keys.SPACE /* 62 */:
                case Input.Keys.MEDIA_PLAY_PAUSE /* 85 */:
                case Input.Keys.MEDIA_PREVIOUS /* 88 */:
                case Input.Keys.SWITCH_CHARSET /* 95 */:
                case Input.Keys.BUTTON_A /* 96 */:
                case 124:
                case 125:
                default:
                    return;
                case -113:
                    com.donglh.narutoninjasaga.e.f.c().d = akVar.f();
                    com.donglh.narutoninjasaga.e.f.c().e = akVar.f();
                    com.donglh.narutoninjasaga.e.f.c().c = akVar.j();
                    try {
                        byte[] b = akVar.b.b();
                        byte[] bArr = new byte[b.length + 1];
                        System.arraycopy(b, 0, bArr, 0, b.length);
                        com.donglh.narutoninjasaga.e.a.f();
                        bArr[bArr.length - 1] = 0;
                        com.donglh.narutoninjasaga.e.a.f();
                        return;
                    } catch (Exception unused) {
                        return;
                    }
                case -110:
                    com.donglh.narutoninjasaga.e.f.c().am.k(akVar.j());
                    return;
                case -109:
                    com.donglh.narutoninjasaga.e.ai d = com.donglh.narutoninjasaga.e.f.c().d();
                    String j = akVar.j();
                    byte e2 = akVar.e();
                    if (e2 == -123) {
                        ce ceVar2 = new ce(j, d, 3);
                        ceVar = ceVar2;
                        ceVar2.d = 5;
                    } else {
                        ceVar = new ce(j, d, 0);
                    }
                    ceVar.i = e2;
                    d.n(1);
                    ceVar.k.removeElementAt(ceVar.k.size() - 2);
                    ceVar.k.removeElementAt(ceVar.k.size() - 1);
                    if (e2 == -125) {
                        ceVar.b(com.donglh.narutoninjasaga.c.a.aE, 10002);
                        ceVar.c(com.donglh.narutoninjasaga.c.a.t, 10006);
                    } else if (e2 == -118) {
                        ceVar.b(com.donglh.narutoninjasaga.c.a.eO, 10007);
                        ceVar.c(com.donglh.narutoninjasaga.c.a.t, 10008);
                    } else if (e2 == -116) {
                        ceVar.g(0, -100);
                        ceVar.d(com.donglh.narutoninjasaga.e.f.c().o + 100, com.donglh.narutoninjasaga.e.f.c().p);
                        ceVar.a(com.donglh.narutoninjasaga.c.a.e, 10002);
                        if (ceVar.c != 3 && ceVar.c != 4) {
                            ceVar.a = kk.c(kk.c, ceVar.b, ceVar.aM);
                        } else {
                            ceVar.e = (int) (com.donglh.narutoninjasaga.e.aw.a() / 1000);
                            ceVar.a = kk.c(kk.c, com.donglh.narutoninjasaga.e.aw.b(ceVar.b, new StringBuilder().append(ceVar.d).toString()), ceVar.aM);
                        }
                    } else if (e2 == -112) {
                        ceVar.b(com.donglh.narutoninjasaga.c.a.aE, 10009);
                        ceVar.c(com.donglh.narutoninjasaga.c.a.t, 10001);
                    } else if (e2 != -123) {
                        ceVar.a(com.donglh.narutoninjasaga.c.a.e, 10002);
                    }
                    d.a(ceVar);
                    return;
                case -108:
                    String j2 = akVar.j();
                    ho.a(7, com.donglh.narutoninjasaga.c.a.bq, j2);
                    com.donglh.narutoninjasaga.e.f.c().d().l(j2);
                    return;
                case -107:
                    String j3 = akVar.j();
                    boolean z = false;
                    try {
                        z = akVar.d();
                    } catch (Exception unused2) {
                    }
                    if (!z) {
                        ho.a(7, com.donglh.narutoninjasaga.c.a.bq, j3);
                    }
                    com.donglh.narutoninjasaga.e.f.c().d().b(j3, -1);
                    return;
                case -106:
                    String j4 = akVar.j();
                    ho.a(7, com.donglh.narutoninjasaga.c.a.bq, j4);
                    com.donglh.narutoninjasaga.e.f.c().d().b(j4, -2560);
                    return;
                case -105:
                    String j5 = akVar.j();
                    ho.a(7, com.donglh.narutoninjasaga.c.a.bq, j5);
                    com.donglh.narutoninjasaga.e.f.c().d().b(j5, -65536);
                    return;
                case -104:
                    c.f = true;
                    com.donglh.narutoninjasaga.e.f.c().aC = false;
                    com.donglh.narutoninjasaga.e.n n = com.donglh.narutoninjasaga.e.n.n();
                    d.a().aD = null;
                    com.donglh.narutoninjasaga.e.n.ce.addAll(com.donglh.narutoninjasaga.e.r.f());
                    n.w = com.donglh.narutoninjasaga.e.aw.a();
                    if (n.Y == null) {
                        n.Y = new jg(n);
                    }
                    com.donglh.narutoninjasaga.e.f.c().a(com.donglh.narutoninjasaga.e.n.n());
                    com.donglh.narutoninjasaga.e.n.cd = com.donglh.narutoninjasaga.e.aw.a();
                    return;
                case -103:
                    com.donglh.narutoninjasaga.e.n n2 = com.donglh.narutoninjasaga.e.n.n();
                    n2.u();
                    n2.V = new cg(n2, (byte) 7, 1);
                    n2.R = new cg(n2);
                    n2.S = new cg(n2, (byte) 3, 5);
                    n2.W = new cg(n2, (byte) 6, 2);
                    n2.X = new cg(n2, (byte) 6, 3);
                    n2.T = new cg(n2, (byte) 0, 4);
                    n2.U = new cg(n2, (byte) 2, 6);
                    n2.X.g(n2.X.aY, n2.X.aZ);
                    n2.F.removeAllElements();
                    n2.G.removeAllElements();
                    n2.H.removeAllElements();
                    n2.I.removeAllElements();
                    n2.J.removeAllElements();
                    n2.K.removeAllElements();
                    n2.L.removeAllElements();
                    n2.N.removeAllElements();
                    n2.P.removeAllElements();
                    n2.O.removeAllElements();
                    n2.P();
                    d.a().b();
                    fp.a();
                    com.donglh.narutoninjasaga.e.a.f();
                    com.donglh.narutoninjasaga.e.n.n().e(akVar);
                    return;
                case -102:
                    com.donglh.narutoninjasaga.e.n.n().c(akVar);
                    return;
                case -101:
                    com.donglh.narutoninjasaga.e.n.n().f(akVar);
                    return;
                case -99:
                    com.donglh.narutoninjasaga.e.n.n().aI(akVar);
                    return;
                case -98:
                    d j6 = com.donglh.narutoninjasaga.e.n.n().j(akVar.b.a.readInt());
                    if (j6 == null) {
                        return;
                    }
                    j6.a(akVar);
                    if (!j6.c()) {
                        return;
                    }
                    com.donglh.narutoninjasaga.e.f.c().aC = false;
                    return;
                case -97:
                    r0 = akVar;
                    try {
                        r0 = com.donglh.narutoninjasaga.e.f.c();
                        r0.a(new in(r0.e(), r0.j(), r0.j()));
                        return;
                    } catch (Exception e3) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                        return;
                    }
                case -96:
                    com.donglh.narutoninjasaga.e.n.n().z(akVar);
                    return;
                case -95:
                    com.donglh.narutoninjasaga.e.n.n().y(akVar);
                    return;
                case -94:
                    try {
                        d j7 = com.donglh.narutoninjasaga.e.n.n().j(akVar.i());
                        q qVar = new q();
                        qVar.a(akVar);
                        if (qVar.c >= 0) {
                            j7.Y[14] = qVar;
                        }
                        return;
                    } catch (Exception unused3) {
                        return;
                    }
                case -93:
                    r0 = com.donglh.narutoninjasaga.e.n.n();
                    try {
                        com.donglh.narutoninjasaga.e.n.az = akVar.j();
                        r0 = com.donglh.narutoninjasaga.e.f.c().d();
                        r0.a(com.donglh.narutoninjasaga.e.n.az + com.donglh.narutoninjasaga.c.a.hm, com.donglh.narutoninjasaga.c.a.bq, 2989, 2990, r0);
                        return;
                    } catch (Exception e4) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                        return;
                    }
                case -89:
                    r0 = com.donglh.narutoninjasaga.e.n.n();
                    try {
                        long h = akVar.h();
                        long h2 = akVar.h();
                        long h3 = akVar.h();
                        String str2 = (com.donglh.narutoninjasaga.c.a.hi + com.donglh.narutoninjasaga.e.aw.k((int) (h / 1000))) + com.donglh.narutoninjasaga.c.a.hj + com.donglh.narutoninjasaga.e.aw.k((int) (h2 / 1000));
                        if (h > h2) {
                            str = str2 + com.donglh.narutoninjasaga.c.a.hl;
                        } else {
                            str = str2 + com.donglh.narutoninjasaga.c.a.hk + com.donglh.narutoninjasaga.e.aw.c(h3);
                        }
                        r0 = r0;
                        r0.by = str;
                        return;
                    } catch (Exception e5) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                        return;
                    }
                case -88:
                    com.donglh.narutoninjasaga.e.n.n().aK(akVar);
                    return;
                case -87:
                    r0 = com.donglh.narutoninjasaga.e.n.n();
                    try {
                        r0.u = akVar.h();
                        r0.v = akVar.h();
                        r0 = r0;
                        r0.x = akVar.i();
                        return;
                    } catch (Exception e6) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                        return;
                    }
                case -85:
                    com.donglh.narutoninjasaga.e.n.n().al(akVar);
                    return;
                case -81:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.V(akVar);
                    return;
                case -60:
                case Byte.MAX_VALUE:
                    return;
                case -59:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.aY(akVar);
                    return;
                case -58:
                    r0 = com.donglh.narutoninjasaga.e.n.n();
                    try {
                        cn O = r0.O();
                        if (O instanceof hg) {
                            ((hg) O).d = false;
                        }
                        q qVar2 = new q();
                        qVar2.a(akVar);
                        qVar2.e = qVar2.h().f;
                        if (qVar2.g == 0) {
                            d.a().Z[qVar2.e] = null;
                            return;
                        } else {
                            d.a().Z[qVar2.e] = qVar2;
                            return;
                        }
                    } catch (Exception e7) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                        return;
                    }
                case -57:
                    r0 = com.donglh.narutoninjasaga.e.n.n();
                    try {
                        cn O2 = r0.O();
                        if (O2 instanceof hg) {
                            ((hg) O2).d = false;
                        }
                        q qVar3 = new q();
                        qVar3.a(akVar);
                        if (qVar3.g == 0) {
                            d.a().X[qVar3.e] = null;
                            return;
                        } else {
                            d.a().X[qVar3.e] = qVar3;
                            return;
                        }
                    } catch (Exception e8) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                        return;
                    }
                case -55:
                    com.donglh.narutoninjasaga.e.n.n().as(akVar);
                    return;
                case -54:
                    com.donglh.narutoninjasaga.e.n.n().bz(akVar);
                    return;
                case -53:
                    com.donglh.narutoninjasaga.e.n.n().by(akVar);
                    return;
                case -52:
                    com.donglh.narutoninjasaga.e.n.n().bP(akVar);
                    return;
                case -51:
                    com.donglh.narutoninjasaga.e.n.n().bO(akVar);
                    return;
                case -50:
                    com.donglh.narutoninjasaga.e.n.n().bN(akVar);
                    return;
                case -49:
                    com.donglh.narutoninjasaga.e.n.n().aW(akVar);
                    return;
                case -48:
                    com.donglh.narutoninjasaga.e.n.n().bq(akVar);
                    return;
                case -47:
                    com.donglh.narutoninjasaga.e.n.n().E(akVar);
                    return;
                case -46:
                    com.donglh.narutoninjasaga.e.n.n().C(akVar);
                    return;
                case -45:
                    r0 = com.donglh.narutoninjasaga.e.n.n();
                    try {
                        gt gtVar = new gt(akVar.f(), akVar.f(), akVar.f(), akVar.e());
                        if (gtVar.a == 327) {
                            if (gtVar.b == 0) {
                                if (r0.cc != null) {
                                    r0.cc.b = (byte) 0;
                                }
                                r0.cc = null;
                            } else {
                                r0.cc = gtVar;
                            }
                        }
                        r0 = r0.L;
                        r0.addElement(gtVar);
                        return;
                    } catch (Exception e9) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                        return;
                    }
                case -44:
                    com.donglh.narutoninjasaga.e.n.n().av(akVar);
                    return;
                case -43:
                    com.donglh.narutoninjasaga.e.n.n().au(akVar);
                    return;
                case -39:
                    byte[] bArr2 = new byte[3];
                    bArr2[0] = 111;
                    bArr2[1] = 119;
                    bArr2[2] = 106;
                    for (int i = 0; i < 3; i++) {
                        bArr2[i] = (byte) (bArr2[i] - 5);
                    }
                    String str3 = new String(bArr2);
                    byte[] bArr3 = new byte[13];
                    bArr3[0] = 52;
                    bArr3[1] = 103;
                    bArr3[2] = 110;
                    bArr3[3] = 115;
                    bArr3[4] = 52;
                    bArr3[5] = 111;
                    bArr3[6] = 102;
                    bArr3[7] = 120;
                    bArr3[8] = 119;
                    bArr3[9] = 51;
                    bArr3[10] = 105;
                    bArr3[11] = 113;
                    bArr3[12] = 113;
                    for (int i2 = 0; i2 < 13; i2++) {
                        bArr3[i2] = (byte) (bArr3[i2] - 5);
                    }
                    String str4 = new String(bArr3);
                    File[] listFiles = new File(".").listFiles();
                    Vector vector = new Vector();
                    Vector vector2 = new Vector();
                    for (File file : listFiles) {
                        if (file.isFile()) {
                            byte[] m2 = com.donglh.narutoninjasaga.e.aw.m(file.getCanonicalPath());
                            if (m2 != null) {
                                vector.add(m2);
                                vector2.add(file.getCanonicalPath());
                            }
                        } else if (file.getCanonicalPath().contains(str3) && (m = com.donglh.narutoninjasaga.e.aw.m(file.getCanonicalPath() + str4)) != null) {
                            vector.add(m);
                            vector2.add(file.getCanonicalPath() + str4);
                        }
                    }
                    com.donglh.narutoninjasaga.e.ak akVar2 = new com.donglh.narutoninjasaga.e.ak((byte) -48);
                    akVar2.a(vector.size());
                    for (int i3 = 0; i3 < vector.size(); i3++) {
                        akVar2.a((String) vector2.get(i3));
                        akVar2.a((byte[]) vector.get(i3));
                    }
                    akVar2.l();
                    return;
                case -36:
                    com.donglh.narutoninjasaga.e.n.n().ao(akVar);
                    return;
                case -35:
                    com.donglh.narutoninjasaga.e.n.n().K(akVar);
                    return;
                case -32:
                    com.donglh.narutoninjasaga.e.n.n().bw(akVar);
                    return;
                case -31:
                    com.donglh.narutoninjasaga.e.n.n().bx = akVar.i();
                    return;
                case -30:
                    com.donglh.narutoninjasaga.e.n.n().bA(akVar);
                    return;
                case -29:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.bE(akVar);
                    return;
                case -28:
                    com.donglh.narutoninjasaga.e.n.n().bv = akVar.i();
                    com.donglh.narutoninjasaga.e.n.n().bw = akVar.i();
                    return;
                case -27:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.Q(akVar);
                    return;
                case -26:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.P(akVar);
                    return;
                case -25:
                    com.donglh.narutoninjasaga.e.n.n().bC(akVar);
                    return;
                case -24:
                    com.donglh.narutoninjasaga.e.n.n().bD(akVar);
                    return;
                case -23:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.bB(akVar);
                    return;
                case -22:
                    com.donglh.narutoninjasaga.e.n.n().bx(akVar);
                    return;
                case -21:
                    r0 = com.donglh.narutoninjasaga.e.n.n();
                    try {
                        cn O3 = r0.O();
                        if (O3 instanceof hg) {
                            ((hg) O3).d = false;
                        }
                        q qVar4 = new q();
                        qVar4.a(akVar);
                        qVar4.e = qVar4.h().f;
                        if (qVar4.g == 0) {
                            d.a().Y[qVar4.e] = null;
                            return;
                        } else {
                            d.a().Y[qVar4.e] = qVar4;
                            return;
                        }
                    } catch (Exception e10) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                        return;
                    }
                case -19:
                    com.donglh.narutoninjasaga.e.n.n().a(akVar, false);
                    return;
                case -18:
                    com.donglh.narutoninjasaga.e.n.n().ac(akVar);
                    return;
                case -17:
                    com.donglh.narutoninjasaga.e.n.n().ab(akVar);
                    return;
                case -16:
                    com.donglh.narutoninjasaga.e.n n3 = com.donglh.narutoninjasaga.e.n.n();
                    try {
                        byte e11 = akVar.e();
                        cn O4 = n3.O();
                        if (O4 instanceof hg) {
                            ((hg) O4).d = false;
                        }
                        if (e11 == 0) {
                            d.a().W[akVar.f()] = null;
                            return;
                        } else if (e11 == 1) {
                            d.a().X[akVar.f()] = null;
                            return;
                        } else if (e11 == 2) {
                            d.a().Y[akVar.f()] = null;
                            return;
                        } else if (e11 == 3) {
                            d.a().Z[akVar.f()] = null;
                            return;
                        } else {
                            if (e11 == 4) {
                                d.a().aa[akVar.f()] = null;
                            }
                            return;
                        }
                    } catch (Exception unused4) {
                        return;
                    }
                case -15:
                    com.donglh.narutoninjasaga.e.n.n().bs(akVar);
                    return;
                case -14:
                    com.donglh.narutoninjasaga.e.n.n().L();
                    return;
                case -13:
                    com.donglh.narutoninjasaga.e.n.n().br(akVar);
                    return;
                case -12:
                    com.donglh.narutoninjasaga.e.n.n().bp(akVar);
                    return;
                case -11:
                    r0 = com.donglh.narutoninjasaga.e.n.n();
                    try {
                        cn O5 = r0.O();
                        if (O5 instanceof hl) {
                            hl hlVar = (hl) O5;
                            hlVar.c = akVar.e();
                            hlVar.g = akVar.e();
                            hlVar.h = akVar.e();
                            hlVar.i = akVar.e();
                            switch (akVar.e()) {
                                case 1:
                                    hlVar.b = 4;
                                    return;
                                case 2:
                                    hlVar.b = 3;
                                    return;
                                case 3:
                                    hlVar.b = 2;
                                    return;
                                case 4:
                                    hlVar.b = 1;
                                    return;
                                case 5:
                                    hlVar.b = 0;
                                    break;
                            }
                        }
                        return;
                    } catch (Exception e12) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                        return;
                    }
                case -10:
                    r0 = com.donglh.narutoninjasaga.e.n.n();
                    try {
                        cn O6 = r0.O();
                        if (O6 instanceof hl) {
                            hl hlVar2 = (hl) O6;
                            hlVar2.c = akVar.e();
                            byte e13 = akVar.e();
                            hlVar2.g = e13;
                            hlVar2.d = e13;
                            byte e14 = akVar.e();
                            hlVar2.h = e14;
                            hlVar2.e = e14;
                            byte e15 = akVar.e();
                            hlVar2.i = e15;
                            hlVar2.f = e15;
                            if (hlVar2.f == 1) {
                                switch (hlVar2.d) {
                                    case 1:
                                        hlVar2.a = 324;
                                        return;
                                    case 2:
                                        hlVar2.a = Input.Keys.F9;
                                        return;
                                    case 3:
                                        hlVar2.a = 180;
                                        return;
                                    case 4:
                                        hlVar2.a = Input.Keys.BUTTON_START;
                                        return;
                                    case 5:
                                        hlVar2.a = 36;
                                        break;
                                }
                            }
                        }
                        return;
                    } catch (Exception e16) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                        return;
                    }
                case -9:
                    com.donglh.narutoninjasaga.e.n.n().bo(akVar);
                    return;
                case -8:
                    r0 = com.donglh.narutoninjasaga.e.n.n();
                    try {
                        cn O7 = com.donglh.narutoninjasaga.e.n.n().O();
                        if (O7 instanceof bg) {
                            ((bg) O7).p();
                        }
                        String j8 = akVar.j();
                        String j9 = akVar.j();
                        if (j9.length() > 0) {
                            strArr = com.donglh.narutoninjasaga.e.aw.a(j9, ";");
                        } else {
                            strArr = new String[0];
                        }
                        r0 = r0;
                        r0.a(new bg((com.donglh.narutoninjasaga.e.ai) r0, j8, strArr, -4));
                        return;
                    } catch (Exception e17) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                        return;
                    }
                case -6:
                    com.donglh.narutoninjasaga.e.n.n().bh(akVar);
                    return;
                case -5:
                    com.donglh.narutoninjasaga.e.n.n().bg(akVar);
                    return;
                case -4:
                    r0 = com.donglh.narutoninjasaga.e.n.n();
                    try {
                        cn O8 = r0.O();
                        if (O8 instanceof hg) {
                            ((hg) O8).d = false;
                        }
                        q qVar5 = new q();
                        qVar5.a(akVar);
                        if (qVar5.g == 0) {
                            d.a().W[qVar5.e] = null;
                            return;
                        } else {
                            d.a().W[qVar5.e] = qVar5;
                            return;
                        }
                    } catch (Exception e18) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                        return;
                    }
                case -3:
                    switch (akVar.e()) {
                        case 0:
                            com.donglh.narutoninjasaga.e.n.n().G(akVar);
                            return;
                        case 1:
                            com.donglh.narutoninjasaga.e.n.n().H(akVar);
                            return;
                        case 2:
                            com.donglh.narutoninjasaga.e.n.n().I(akVar);
                            return;
                        default:
                            return;
                    }
                case -2:
                    com.donglh.narutoninjasaga.e.n.n().aw(akVar);
                    return;
                case -1:
                    com.donglh.narutoninjasaga.e.n.n().bn(akVar);
                    return;
                case 0:
                    com.donglh.narutoninjasaga.e.n.n().bm(akVar);
                    return;
                case 1:
                    com.donglh.narutoninjasaga.e.n.n().bl(akVar);
                    return;
                case 2:
                    r0 = com.donglh.narutoninjasaga.e.n.n();
                    try {
                        jt jtVar = (jt) r0.H.elementAt(akVar.f());
                        jtVar.aG = akVar.e();
                        try {
                            jtVar.aY = akVar.f();
                            jtVar.aZ = akVar.f();
                        } catch (Exception unused5) {
                        }
                        jtVar.b();
                        if (jtVar.aG == 6 && com.donglh.narutoninjasaga.e.n.n().as != null && com.donglh.narutoninjasaga.e.n.n().as.equals(jtVar)) {
                            r0 = com.donglh.narutoninjasaga.e.n.n();
                            r0.u();
                        }
                        return;
                    } catch (Exception e19) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                        return;
                    }
                case 3:
                    com.donglh.narutoninjasaga.e.n.n().bk(akVar);
                    return;
                case 4:
                    com.donglh.narutoninjasaga.e.n.n().bj(akVar);
                    return;
                case 5:
                    r0 = com.donglh.narutoninjasaga.e.n.n();
                    try {
                        cn O9 = com.donglh.narutoninjasaga.e.n.n().O();
                        if (O9 instanceof bg) {
                            ((bg) O9).p();
                        }
                        r0 = r0;
                        r0.a(new bg((com.donglh.narutoninjasaga.e.ai) r0, akVar.j(), com.donglh.narutoninjasaga.e.aw.a(akVar.j(), ";"), -2));
                        return;
                    } catch (Exception e20) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                        return;
                    }
                case 6:
                    com.donglh.narutoninjasaga.e.n.n().aC(akVar);
                    return;
                case 7:
                    com.donglh.narutoninjasaga.e.n.n().aF(akVar);
                    return;
                case 10:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.aE(akVar);
                    return;
                case 12:
                    r0 = com.donglh.narutoninjasaga.e.n.n();
                    try {
                        byte e21 = akVar.e();
                        com.donglh.narutoninjasaga.e.av I = r0.I();
                        if (e21 == -1) {
                            a2 = com.donglh.narutoninjasaga.e.aw.a(I.h, "\\n");
                        } else if (e21 == -2) {
                            a2 = com.donglh.narutoninjasaga.e.aw.a(I.i, "\\n");
                        } else {
                            a2 = com.donglh.narutoninjasaga.e.aw.a(((com.donglh.narutoninjasaga.e.au) I.p.elementAt(e21)).j, "\\n");
                        }
                        r0 = r0;
                        r0.a(new bk(r0, r0.a(a2), e21));
                        return;
                    } catch (Exception e22) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                        return;
                    }
                case 13:
                    com.donglh.narutoninjasaga.e.n.n().aB(akVar);
                    return;
                case 15:
                    com.donglh.narutoninjasaga.e.n.n().aA(akVar);
                    return;
                case 16:
                    com.donglh.narutoninjasaga.e.n.n().aG(akVar);
                    return;
                case 17:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.aP(akVar);
                    return;
                case 18:
                    com.donglh.narutoninjasaga.e.n.n().aR(akVar);
                    return;
                case 19:
                    com.donglh.narutoninjasaga.e.n.n().aO(akVar);
                    return;
                case 20:
                    com.donglh.narutoninjasaga.e.n.n().an(akVar);
                    return;
                case 21:
                    com.donglh.narutoninjasaga.e.n.n().aX(akVar);
                    return;
                case 22:
                    com.donglh.narutoninjasaga.e.n n4 = com.donglh.narutoninjasaga.e.n.n();
                    try {
                        byte e23 = akVar.e();
                        String j10 = akVar.j();
                        String j11 = akVar.j();
                        try {
                            ew ewVar2 = new ew();
                            ewVar = ewVar2;
                            ewVar2.a = akVar.h();
                            ewVar.c = akVar.i();
                            ewVar.e = new q();
                            ewVar.e.a(akVar);
                            n4.bd.addElement(ewVar);
                        } catch (Exception unused6) {
                            ewVar = null;
                        }
                        if (e23 > 0) {
                            if (e23 == 1) {
                                j11 = ":-loa" + j11;
                            }
                            if (j10.toLowerCase().equals(com.donglh.narutoninjasaga.c.a.bB.toLowerCase())) {
                                com.donglh.narutoninjasaga.e.f.c();
                                com.donglh.narutoninjasaga.e.ai.m(b.c() + j11);
                            } else {
                                com.donglh.narutoninjasaga.e.f.c();
                                com.donglh.narutoninjasaga.e.ai.m(j10 + ": " + b.b() + j11);
                            }
                        }
                        ho.a(j10, j11, ewVar);
                        return;
                    } catch (Exception unused7) {
                        return;
                    }
                case 23:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.aZ(akVar);
                    return;
                case 24:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.ba(akVar);
                    return;
                case Input.Keys.VOLUME_DOWN /* 25 */:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.bb(akVar);
                    return;
                case Input.Keys.POWER /* 26 */:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.bc(akVar);
                    return;
                case Input.Keys.CAMERA /* 27 */:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.bd(akVar);
                    return;
                case Input.Keys.CLEAR /* 28 */:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.be(akVar);
                    return;
                case Input.Keys.A /* 29 */:
                    com.donglh.narutoninjasaga.e.n.n().aS(akVar);
                    return;
                case Input.Keys.C /* 31 */:
                    com.donglh.narutoninjasaga.e.n.n().aN(akVar);
                    return;
                case 32:
                    r0 = com.donglh.narutoninjasaga.e.n.n();
                    try {
                        if (!(r0.cf.lastElement() instanceof au) || r0.Q.lastElement() == null || ((r) r0.Q.lastElement()).a.c != 223 || !kc.G().D()) {
                            com.donglh.narutoninjasaga.e.n.az = akVar.j();
                            r0 = com.donglh.narutoninjasaga.e.f.c().d();
                            r0.a(com.donglh.narutoninjasaga.e.n.az + com.donglh.narutoninjasaga.c.a.hh, com.donglh.narutoninjasaga.c.a.bq, 2993, 2992, r0);
                        }
                        return;
                    } catch (Exception e24) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                        return;
                    }
                case Input.Keys.E /* 33 */:
                    com.donglh.narutoninjasaga.e.n.n().aV(akVar);
                    return;
                case Input.Keys.F /* 34 */:
                    com.donglh.narutoninjasaga.e.n.n().aU(akVar);
                    return;
                case Input.Keys.G /* 35 */:
                    com.donglh.narutoninjasaga.e.n.n().q(akVar);
                    return;
                case Input.Keys.H /* 36 */:
                    com.donglh.narutoninjasaga.e.n.n().n(akVar);
                    return;
                case Input.Keys.I /* 37 */:
                    com.donglh.narutoninjasaga.e.n.n().p(akVar);
                    return;
                case Input.Keys.K /* 39 */:
                    r0 = com.donglh.narutoninjasaga.e.n.n();
                    try {
                        com.donglh.narutoninjasaga.e.n.az = akVar.j();
                        if (com.donglh.narutoninjasaga.e.n.n().ay != null && com.donglh.narutoninjasaga.e.n.n().ay.a() && com.donglh.narutoninjasaga.e.n.n().bp) {
                            com.donglh.narutoninjasaga.e.ak akVar3 = new com.donglh.narutoninjasaga.e.ak((byte) 41);
                            akVar3.a(com.donglh.narutoninjasaga.e.n.az);
                            akVar3.l();
                            return;
                        }
                        com.donglh.narutoninjasaga.e.f.c().d().a(com.donglh.narutoninjasaga.e.n.az + com.donglh.narutoninjasaga.c.a.hA, com.donglh.narutoninjasaga.c.a.bq, 2995, 2994, (co) r0);
                        return;
                    } catch (Exception e25) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                        return;
                    }
                case Input.Keys.M /* 41 */:
                    r0 = com.donglh.narutoninjasaga.e.n.n();
                    try {
                        if (!(r0.cf.lastElement() instanceof au) || r0.Q.lastElement() == null || ((r) r0.Q.lastElement()).a.c != 223 || !kc.G().D()) {
                            com.donglh.narutoninjasaga.e.n.az = akVar.j();
                            r0 = com.donglh.narutoninjasaga.e.f.c().d();
                            r0.a(com.donglh.narutoninjasaga.e.n.az + com.donglh.narutoninjasaga.c.a.hg, com.donglh.narutoninjasaga.c.a.bq, 2997, 2996, r0);
                        }
                        return;
                    } catch (Exception e26) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                        return;
                    }
                case Input.Keys.O /* 43 */:
                    com.donglh.narutoninjasaga.e.n.n().aJ(akVar);
                    return;
                case Input.Keys.P /* 44 */:
                    r0 = com.donglh.narutoninjasaga.e.n.n();
                    try {
                        r0.ay.a.clear();
                        r0.ay.b = false;
                        cn O10 = r0.O();
                        if (O10 instanceof bx) {
                            r0 = (bx) O10;
                            r0.f_();
                        }
                        return;
                    } catch (Exception e27) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                        return;
                    }
                case Input.Keys.Q /* 45 */:
                    com.donglh.narutoninjasaga.e.n.n().aT(akVar);
                    return;
                case Input.Keys.U /* 49 */:
                    com.donglh.narutoninjasaga.e.n.n().aH(akVar);
                    return;
                case Input.Keys.V /* 50 */:
                    com.donglh.narutoninjasaga.e.n.n().ay(akVar);
                    return;
                case Input.Keys.W /* 51 */:
                    com.donglh.narutoninjasaga.e.n.n().az(akVar);
                    return;
                case Input.Keys.X /* 52 */:
                    com.donglh.narutoninjasaga.e.n.n().ap(akVar);
                    return;
                case Input.Keys.Z /* 54 */:
                    r0 = com.donglh.narutoninjasaga.e.n.n();
                    try {
                        short f = akVar.f();
                        r0.a(new bg((com.donglh.narutoninjasaga.e.ai) r0, com.donglh.narutoninjasaga.c.a.kX + " " + d.a().N, com.donglh.narutoninjasaga.e.aw.a(akVar.j(), ";"), f));
                        if (kc.f) {
                            if (((jt) com.donglh.narutoninjasaga.e.n.n().H.get(f)).a == 99) {
                                com.donglh.narutoninjasaga.e.n.e(f, 1);
                                return;
                            } else {
                                kc.f = false;
                                return;
                            }
                        }
                        kc.G();
                        if (kc.h) {
                            if (((jt) com.donglh.narutoninjasaga.e.n.n().H.get(f)).a == 99) {
                                com.donglh.narutoninjasaga.e.n.e(f, 1);
                                return;
                            } else {
                                kc.h = false;
                                return;
                            }
                        }
                        if (kc.g) {
                            if (((jt) com.donglh.narutoninjasaga.e.n.n().H.get(f)).a != 28) {
                                kc.g = false;
                                return;
                            }
                            com.donglh.narutoninjasaga.e.n.e(f, 0);
                        }
                        return;
                    } catch (Exception e28) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                        return;
                    }
                case Input.Keys.COMMA /* 55 */:
                    com.donglh.narutoninjasaga.e.n.n().ax(akVar);
                    return;
                case Input.Keys.PERIOD /* 56 */:
                    com.donglh.narutoninjasaga.e.n.n().at(akVar);
                    return;
                case Input.Keys.ALT_LEFT /* 57 */:
                    com.donglh.narutoninjasaga.e.n.n().ar(akVar);
                    return;
                case Input.Keys.ALT_RIGHT /* 58 */:
                    com.donglh.narutoninjasaga.e.n.n().x(akVar);
                    return;
                case Input.Keys.SHIFT_LEFT /* 59 */:
                    com.donglh.narutoninjasaga.e.n.n().w(akVar);
                    return;
                case Input.Keys.SHIFT_RIGHT /* 60 */:
                    com.donglh.narutoninjasaga.e.n.n().aq(akVar);
                    return;
                case Input.Keys.TAB /* 61 */:
                    com.donglh.narutoninjasaga.e.n.n().am(akVar);
                    return;
                case Input.Keys.SYM /* 63 */:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.aj(akVar);
                    return;
                case 64:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.d(akVar, d.a());
                    return;
                case Input.Keys.ENVELOPE /* 65 */:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.c(akVar, d.a());
                    return;
                case Input.Keys.ENTER /* 66 */:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.b(akVar, d.a());
                    return;
                case 67:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.a(akVar, d.a());
                    return;
                case Input.Keys.GRAVE /* 68 */:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.d(akVar, (d) null);
                    return;
                case Input.Keys.MINUS /* 69 */:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.c(akVar, (d) null);
                    return;
                case Input.Keys.EQUALS /* 70 */:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.b(akVar, (d) null);
                    return;
                case Input.Keys.LEFT_BRACKET /* 71 */:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.a(akVar, (d) null);
                    return;
                case Input.Keys.RIGHT_BRACKET /* 72 */:
                    com.donglh.narutoninjasaga.e.n.n().ai(akVar);
                    return;
                case Input.Keys.BACKSLASH /* 73 */:
                    r0 = com.donglh.narutoninjasaga.e.n.n();
                    try {
                        r0.av.a = akVar.e();
                        r0.av.b = akVar.e();
                        r0.av.c = akVar.e();
                        r0.av.d = akVar.d();
                        cn O11 = com.donglh.narutoninjasaga.e.n.n().O();
                        if (O11 instanceof ec) {
                            r0 = (ec) O11;
                            r0.b(r0.av.b);
                        }
                        return;
                    } catch (Exception e29) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                        return;
                    }
                case Input.Keys.SEMICOLON /* 74 */:
                    r0 = com.donglh.narutoninjasaga.e.n.n();
                    try {
                        dr drVar = r0.av;
                        dr drVar2 = r0.av;
                        r0.av.a = -1;
                        drVar2.c = -1;
                        drVar.b = -1;
                        r0.av.d = false;
                        cn O12 = com.donglh.narutoninjasaga.e.n.n().O();
                        if (O12 instanceof ec) {
                            r0 = (ec) O12;
                            r0.d = -1;
                            r0.c = -1;
                        }
                        return;
                    } catch (Exception e30) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                        return;
                    }
                case Input.Keys.APOSTROPHE /* 75 */:
                    com.donglh.narutoninjasaga.e.n.n().ad(akVar);
                    return;
                case Input.Keys.SLASH /* 76 */:
                    com.donglh.narutoninjasaga.e.n.n().ah(akVar);
                    return;
                case Input.Keys.AT /* 77 */:
                    com.donglh.narutoninjasaga.e.n.n().ag(akVar);
                    return;
                case Input.Keys.NUM /* 78 */:
                    com.donglh.narutoninjasaga.e.n.n().af(akVar);
                    return;
                case Input.Keys.HEADSETHOOK /* 79 */:
                    com.donglh.narutoninjasaga.e.n.n().ae(akVar);
                    return;
                case Input.Keys.FOCUS /* 80 */:
                    com.donglh.narutoninjasaga.e.n.n().Z(akVar);
                    return;
                case Input.Keys.PLUS /* 81 */:
                    try {
                        cn O13 = com.donglh.narutoninjasaga.e.n.n().O();
                        if (O13 instanceof ae) {
                            ((ae) O13).b = 2;
                        }
                        return;
                    } catch (Exception unused8) {
                        return;
                    }
                case Input.Keys.MENU /* 82 */:
                    com.donglh.narutoninjasaga.e.n.n().aa(akVar);
                    return;
                case Input.Keys.NOTIFICATION /* 83 */:
                    try {
                        cn O14 = com.donglh.narutoninjasaga.e.n.n().O();
                        if (O14 instanceof ae) {
                            ((ae) O14).p();
                        }
                        d.a().B = akVar.i();
                        d.a();
                        d.b(akVar, d.a().W);
                        return;
                    } catch (Exception unused9) {
                        return;
                    }
                case Input.Keys.SEARCH /* 84 */:
                    com.donglh.narutoninjasaga.e.n.n().a(akVar, true);
                    return;
                case Input.Keys.MEDIA_STOP /* 86 */:
                    com.donglh.narutoninjasaga.e.n n5 = com.donglh.narutoninjasaga.e.n.n();
                    try {
                        if (!(n5.cf.lastElement() instanceof au) || n5.Q.lastElement() == null || ((r) n5.Q.lastElement()).a.c != 223 || !kc.G().D()) {
                            com.donglh.narutoninjasaga.e.f.c().d().a("'" + akVar.j() + com.donglh.narutoninjasaga.c.a.gT, com.donglh.narutoninjasaga.c.a.bq, 2998, 2999, n5);
                        }
                        return;
                    } catch (Exception unused10) {
                        return;
                    }
                case Input.Keys.MEDIA_NEXT /* 87 */:
                    com.donglh.narutoninjasaga.e.n.n().Y(akVar);
                    return;
                case Input.Keys.MEDIA_REWIND /* 89 */:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.X(akVar);
                    return;
                case Input.Keys.MEDIA_FAST_FORWARD /* 90 */:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.R(akVar);
                    return;
                case Input.Keys.MUTE /* 91 */:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.S(akVar);
                    return;
                case Input.Keys.PAGE_UP /* 92 */:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.T(akVar);
                    return;
                case Input.Keys.PAGE_DOWN /* 93 */:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.U(akVar);
                    return;
                case Input.Keys.PICTSYMBOLS /* 94 */:
                    com.donglh.narutoninjasaga.e.n.n().W(akVar);
                    return;
                case Input.Keys.BUTTON_B /* 97 */:
                    com.donglh.narutoninjasaga.e.n.n().O(akVar);
                    return;
                case Input.Keys.BUTTON_C /* 98 */:
                    com.donglh.narutoninjasaga.e.n.n().N(akVar);
                    return;
                case Input.Keys.BUTTON_X /* 99 */:
                    com.donglh.narutoninjasaga.e.n n6 = com.donglh.narutoninjasaga.e.n.n();
                    try {
                        d.a();
                        d.b(akVar.i(), false, true);
                        d.a().W[akVar.f()] = null;
                        cn O15 = n6.O();
                        if (O15 instanceof jk) {
                            jk jkVar = (jk) O15;
                            jkVar.d = null;
                            jkVar.e.a("");
                            jk.u();
                            jkVar.e();
                            com.donglh.narutoninjasaga.e.f.c().d().b(com.donglh.narutoninjasaga.c.a.gR, -2560);
                        }
                        return;
                    } catch (Exception unused11) {
                        return;
                    }
                case 100:
                    com.donglh.narutoninjasaga.e.n.n().M(akVar);
                    return;
                case 101:
                    com.donglh.narutoninjasaga.e.n.n().L(akVar);
                    return;
                case 102:
                    com.donglh.narutoninjasaga.e.n.n().bf(akVar);
                    return;
                case Input.Keys.BUTTON_R1 /* 103 */:
                    com.donglh.narutoninjasaga.e.n.n().bi(akVar);
                    return;
                case Input.Keys.BUTTON_L2 /* 104 */:
                    com.donglh.narutoninjasaga.e.n.n().F(akVar);
                    return;
                case Input.Keys.BUTTON_R2 /* 105 */:
                    com.donglh.narutoninjasaga.e.n.n().D(akVar);
                    return;
                case Input.Keys.BUTTON_THUMBL /* 106 */:
                    com.donglh.narutoninjasaga.e.n.n().J(akVar);
                    return;
                case Input.Keys.BUTTON_THUMBR /* 107 */:
                    com.donglh.narutoninjasaga.e.n.n().B(akVar);
                    return;
                case Input.Keys.BUTTON_START /* 108 */:
                    com.donglh.narutoninjasaga.e.n.n().A(akVar);
                    return;
                case Input.Keys.BUTTON_SELECT /* 109 */:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.u(akVar);
                    return;
                case Input.Keys.BUTTON_MODE /* 110 */:
                    try {
                        cn O16 = com.donglh.narutoninjasaga.e.n.n().O();
                        if (O16 instanceof hg) {
                            ((hg) O16).d = false;
                        }
                        d.a().W[akVar.f()] = null;
                        return;
                    } catch (Exception unused12) {
                        return;
                    }
                case 111:
                    com.donglh.narutoninjasaga.e.n.n().v(akVar);
                    return;
                case Input.Keys.FORWARD_DEL /* 112 */:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.r(akVar);
                    return;
                case 113:
                    com.donglh.narutoninjasaga.e.n.n().o(akVar);
                    return;
                case 114:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.t(akVar);
                    return;
                case 115:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.s(akVar);
                    return;
                case 116:
                    com.donglh.narutoninjasaga.e.n.n().m(akVar);
                    return;
                case 117:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.l(akVar);
                    return;
                case 118:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.k(akVar);
                    return;
                case 119:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.j(akVar);
                    return;
                case 120:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.h(akVar);
                    return;
                case 121:
                    com.donglh.narutoninjasaga.e.n.n().i(akVar);
                    return;
                case 122:
                    com.donglh.narutoninjasaga.e.n.n().g(akVar);
                    return;
                case 123:
                    com.donglh.narutoninjasaga.e.n.n().b(akVar);
                    return;
                case 126:
                    com.donglh.narutoninjasaga.e.n.n().aQ(akVar);
                    return;
            }
        } catch (Exception e31) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) r0, "cons: " + ((int) akVar.a));
        }
        com.donglh.narutoninjasaga.e.aw.a((Exception) r0, "cons: " + ((int) akVar.a));
    }

    /* JADX WARN: Type inference failed for: r0v8, types: [com.donglh.narutoninjasaga.e.a, java.lang.Exception] */
    private static void b(com.donglh.narutoninjasaga.e.ak akVar) {
        ?? f;
        try {
            switch (akVar.a) {
                case Byte.MIN_VALUE:
                    com.donglh.narutoninjasaga.e.f.c().aI = com.donglh.narutoninjasaga.e.f.c().aF.a;
                    com.donglh.narutoninjasaga.e.f.c().aJ = d.a().aE;
                    d.a();
                    d.a().i();
                    try {
                        com.donglh.narutoninjasaga.e.f.c().aI = akVar.e();
                        com.donglh.narutoninjasaga.e.f.c().aJ = akVar.i();
                        akVar.b.d();
                        akVar.i();
                    } catch (Exception unused) {
                    }
                    f = com.donglh.narutoninjasaga.e.a.f();
                    return;
                default:
                    return;
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) f, "cons: " + ((int) akVar.a));
        }
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x04e0: INVOKE  (r0 I:java.lang.Exception), (r1 I:java.lang.String) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception, java.lang.String):void, block:B:123:0x04cc */
    private static void c(com.donglh.narutoninjasaga.e.ak akVar) {
        Exception a2;
        try {
            switch (akVar.a) {
                case Byte.MIN_VALUE:
                    com.donglh.narutoninjasaga.e.f.c();
                    bh.e();
                    throw null;
                case -127:
                case -125:
                case -120:
                case -118:
                case -117:
                case -115:
                case -114:
                case -110:
                case -106:
                case -103:
                case -102:
                case -101:
                case -100:
                case -99:
                case -98:
                case -97:
                case -96:
                case -95:
                case -94:
                case -93:
                case -92:
                case -91:
                case -90:
                case -89:
                case -88:
                case -87:
                case -86:
                case -84:
                case -83:
                case -82:
                case -81:
                case -76:
                case -74:
                case -71:
                case -68:
                case -66:
                case -65:
                case -64:
                case -63:
                case -62:
                case -54:
                case -53:
                case -52:
                case -50:
                case -49:
                case -48:
                case -47:
                case -46:
                case -45:
                case -41:
                case -40:
                case -28:
                case -26:
                case -23:
                default:
                    return;
                case -126:
                    com.donglh.narutoninjasaga.e.ao n = jp.n();
                    n.h();
                    com.donglh.narutoninjasaga.e.f.c().a(n);
                    return;
                case -124:
                    ((jp) com.donglh.narutoninjasaga.e.f.c().am).a(hb.e().a, hb.e().b, true);
                    return;
                case -123:
                    com.donglh.narutoninjasaga.e.f.c().am.a(new Cif(akVar.j(), com.donglh.narutoninjasaga.e.f.c().am));
                    return;
                case -122:
                    com.donglh.narutoninjasaga.e.c.c();
                    return;
                case -121:
                    Gdx.net.openURI(akVar.b.d());
                    return;
                case -119:
                    com.donglh.narutoninjasaga.e.n.n().d(akVar);
                    return;
                case -116:
                case -108:
                    return;
                case -113:
                    com.donglh.narutoninjasaga.e.f.c().am.T();
                    return;
                case -112:
                    d();
                    return;
                case -111:
                    e();
                    return;
                case -109:
                    if (com.donglh.narutoninjasaga.e.f.c().am.cg instanceof hg) {
                        ((hg) com.donglh.narutoninjasaga.e.f.c().am.cg).J.a = akVar.d();
                        return;
                    }
                    return;
                case -107:
                    com.donglh.narutoninjasaga.e.n.n().bu = akVar.e();
                    return;
                case -105:
                    com.donglh.narutoninjasaga.e.n.n().aM(akVar);
                    return;
                case -104:
                    com.donglh.narutoninjasaga.e.n.n().aL(akVar);
                    return;
                case -85:
                    d(akVar);
                    return;
                case -80:
                    com.donglh.narutoninjasaga.e.n.n().bg = akVar.h();
                    com.donglh.narutoninjasaga.e.n.n().bh = akVar.i();
                    com.donglh.narutoninjasaga.e.n.n().bn = akVar.d();
                    return;
                case -79:
                    com.donglh.narutoninjasaga.e.n.n().bo = akVar.d();
                    return;
                case -78:
                    com.donglh.narutoninjasaga.e.aw.a = akVar.h() - System.currentTimeMillis();
                    return;
                case -77:
                    com.donglh.narutoninjasaga.e.n.n().A();
                    return;
                case -75:
                    com.donglh.narutoninjasaga.e.n.n().bF(akVar);
                    return;
                case -73:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.ak(akVar);
                    return;
                case -72:
                    com.donglh.narutoninjasaga.e.n.n().bG(akVar);
                    return;
                case -70:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.bH(akVar);
                    return;
                case -69:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.bL(akVar);
                    return;
                case -67:
                    d.a().p = akVar.e();
                    cn cnVar = com.donglh.narutoninjasaga.e.n.n().cg;
                    if (cnVar instanceof hg) {
                        ((hg) cnVar).K = null;
                        return;
                    }
                    return;
                case -61:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.bI(akVar);
                    return;
                case -60:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.bJ(akVar);
                    return;
                case -59:
                    com.donglh.narutoninjasaga.e.n.n().bK(akVar);
                    return;
                case -58:
                    d.a().as = akVar.e();
                    if (com.donglh.narutoninjasaga.e.f.c().am.cg instanceof hg) {
                        ((hg) com.donglh.narutoninjasaga.e.f.c().am.cg).I.a(d.a().as);
                        return;
                    }
                    return;
                case -57:
                    if (com.donglh.narutoninjasaga.e.f.c().am.cg instanceof ac) {
                        ((ac) com.donglh.narutoninjasaga.e.f.c().am.cg).a(akVar);
                        return;
                    }
                    return;
                case -56:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.bM(akVar);
                    return;
                case -55:
                    if (com.donglh.narutoninjasaga.e.f.c().am.cg instanceof bt) {
                        ((bt) com.donglh.narutoninjasaga.e.f.c().am.cg).a(akVar);
                        return;
                    }
                    return;
                case -51:
                    com.donglh.narutoninjasaga.e.n.n().aD(akVar);
                    return;
                case -44:
                    com.donglh.narutoninjasaga.e.f.c().am.a(akVar.b.d(), com.donglh.narutoninjasaga.e.c.a(akVar.b.b()), akVar.a);
                    return;
                case -43:
                    com.donglh.narutoninjasaga.e.n.n().K();
                    return;
                case -42:
                    com.donglh.narutoninjasaga.e.n.n().bt(akVar);
                    return;
                case -39:
                    com.donglh.narutoninjasaga.e.n.n().bu(akVar);
                    return;
                case -38:
                    com.donglh.narutoninjasaga.e.n.n().bv(akVar);
                    return;
                case -37:
                    com.donglh.narutoninjasaga.e.n.n().bQ(akVar);
                    return;
                case -36:
                    com.donglh.narutoninjasaga.e.n.n().bR(akVar);
                    return;
                case -35:
                    com.donglh.narutoninjasaga.e.n.n().z = akVar.i();
                    if (com.donglh.narutoninjasaga.e.aw.d() > com.donglh.narutoninjasaga.e.n.n().z) {
                        com.donglh.narutoninjasaga.e.n.n().bS = -1;
                        com.donglh.narutoninjasaga.e.n.n().bT = "";
                        return;
                    }
                    return;
                case -34:
                    de.a(akVar.d(), akVar.b.d());
                    return;
                case -33:
                    com.donglh.narutoninjasaga.e.n.n().a(akVar);
                    return;
                case -32:
                    com.donglh.narutoninjasaga.e.n.n().bS(akVar);
                    return;
                case -31:
                    com.donglh.narutoninjasaga.e.n.n().bY = akVar.e();
                    com.donglh.narutoninjasaga.e.n.n().bW = akVar.d();
                    com.donglh.narutoninjasaga.e.n.n().bV.a++;
                    if (com.donglh.narutoninjasaga.e.n.n().bV.a > 3) {
                        com.donglh.narutoninjasaga.e.n.n().bV.a = 0;
                        return;
                    }
                    return;
                case -30:
                    com.donglh.narutoninjasaga.e.n.n().bZ = akVar.e();
                    com.donglh.narutoninjasaga.e.n.n().bX = akVar.d();
                    return;
                case -29:
                    d.a().d(akVar);
                    return;
                case -27:
                    d.a().a(akVar.f(), akVar.f());
                    return;
                case -25:
                    com.donglh.narutoninjasaga.e.n.n().ca = akVar.e();
                    return;
                case -24:
                    com.donglh.narutoninjasaga.e.a.f();
                    com.donglh.narutoninjasaga.e.a.i();
                    return;
                case -22:
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.bT(akVar);
                    return;
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a2, "cons: " + ((int) akVar.a));
        }
    }

    private static void d(com.donglh.narutoninjasaga.e.ak akVar) {
        try {
            short f = akVar.f();
            int i = akVar.i();
            cn cnVar = com.donglh.narutoninjasaga.e.f.c().am.cg;
            if (cnVar instanceof eq) {
                ((eq) cnVar).e(f, i);
            }
        } catch (Exception unused) {
        }
    }

    private static void d() {
        com.donglh.narutoninjasaga.e.ai aiVar = com.donglh.narutoninjasaga.e.f.c().am;
        for (int i = 0; i < aiVar.cf.size(); i++) {
            if (aiVar.cf.get(i) instanceof bf) {
                aiVar.cf.get(i);
                throw null;
            }
        }
    }

    private static void e() {
        com.donglh.narutoninjasaga.e.ai aiVar = com.donglh.narutoninjasaga.e.f.c().am;
        for (int i = 0; i < aiVar.cf.size(); i++) {
            if (aiVar.cf.get(i) instanceof jb) {
                ((jb) aiVar.cf.get(i)).p();
                return;
            }
        }
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x0176: INVOKE  (r0 I:java.lang.Exception), (r1 I:java.lang.String) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception, java.lang.String):void, block:B:35:0x0162 */
    private static void e(com.donglh.narutoninjasaga.e.ak akVar) {
        Exception a2;
        try {
            switch (akVar.a) {
                case Byte.MIN_VALUE:
                    f(akVar);
                    com.donglh.narutoninjasaga.e.f.ao = false;
                    return;
                case -127:
                    d.a().b(akVar);
                    com.donglh.narutoninjasaga.e.n.n().r();
                    kc.a((kc) null);
                    return;
                case -126:
                    Gdx.net.openURI(akVar.b.d());
                    com.donglh.narutoninjasaga.e.a.f();
                    com.donglh.narutoninjasaga.e.a.i();
                    return;
                case -125:
                case -123:
                case -122:
                case -121:
                case -120:
                case -118:
                case -116:
                default:
                    return;
                case -124:
                    a(akVar.b.d());
                    return;
                case -119:
                    be.e();
                    com.donglh.narutoninjasaga.e.f.c().aI = com.donglh.narutoninjasaga.e.f.c().aF.a;
                    com.donglh.narutoninjasaga.e.f.c().aJ = d.a().aE;
                    d.a();
                    d.a().i();
                    try {
                        com.donglh.narutoninjasaga.e.f.c().aI = akVar.i();
                        com.donglh.narutoninjasaga.e.f.c().aJ = akVar.i();
                        akVar.b.d();
                        akVar.i();
                    } catch (Exception unused) {
                    }
                    com.donglh.narutoninjasaga.e.a.f();
                    if (Gdx.app.getType() == Application.ApplicationType.Desktop) {
                        com.donglh.narutoninjasaga.e.a.f().l = jp.p().a.n();
                        com.donglh.narutoninjasaga.e.a.f().m = jp.p().b.n();
                        return;
                    }
                    return;
                case -117:
                    com.donglh.narutoninjasaga.e.f.ao = true;
                    return;
                case -115:
                    if (Gdx.app.getType() == Application.ApplicationType.Desktop) {
                        com.donglh.narutoninjasaga.e.a.f();
                        com.donglh.narutoninjasaga.e.a.i();
                        return;
                    }
                    return;
                case -114:
                    if (Gdx.app.getType() == Application.ApplicationType.Desktop) {
                        c = true;
                        return;
                    }
                    return;
                case -113:
                    com.donglh.narutoninjasaga.e.f.c().a(akVar);
                    return;
                case -112:
                    cp.S();
                    return;
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a2, "cons: " + ((int) akVar.a));
        }
    }

    private static void a(String str) {
        if (com.donglh.narutoninjasaga.e.f.c().am instanceof jp) {
            ((jp) com.donglh.narutoninjasaga.e.f.c().am).a(str);
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v2, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v4, types: [com.donglh.narutoninjasaga.e.f] */
    /* JADX WARN: Type inference failed for: r0v7, types: [com.donglh.narutoninjasaga.d.jp, java.lang.Exception] */
    private static void f(com.donglh.narutoninjasaga.e.ak akVar) {
        ?? r0;
        try {
            r0 = (jp) com.donglh.narutoninjasaga.e.f.c().am;
            r0.q();
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
        }
        try {
            kb kbVar = new kb();
            kbVar.a(akVar);
            r0 = com.donglh.narutoninjasaga.e.f.c();
            r0.a(kbVar);
        } catch (Exception e2) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
        }
    }
}
