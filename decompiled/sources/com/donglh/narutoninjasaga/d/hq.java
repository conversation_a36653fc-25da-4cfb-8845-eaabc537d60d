package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.Input;
import com.badlogic.gdx.net.HttpStatus;
import java.util.Vector;
/* compiled from: LangLa_ky.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/hq.class */
public final class hq extends hj {
    private fc y;
    public fw[] a;
    private fx z;
    private int A;
    private String B;
    private String C;
    public int f;
    private int D;
    private int E;
    public int g;
    private int F;
    private long G;
    private String[] H;
    private int I;
    public int h;
    private Vector J;
    private Vector K;
    private Vector L;
    public Vector x;
    private Vector M;
    private cb N;
    private cb O;
    private int P;
    private int Q;
    private int R;
    private String S;
    private int T;
    private int U;
    private String V;

    private int e() {
        return HttpStatus.SC_INTERNAL_SERVER_ERROR + (this.f * 100);
    }

    public hq(com.donglh.narutoninjasaga.e.ai aiVar, com.donglh.narutoninjasaga.e.ak akVar) {
        super(aiVar, 4, new String[]{com.donglh.narutoninjasaga.c.a.dD});
        this.B = com.donglh.narutoninjasaga.c.a.kM;
        this.C = "";
        this.H = new String[0];
        this.J = new Vector();
        this.K = new Vector();
        this.L = new Vector();
        this.x = new Vector();
        this.M = new Vector();
        this.s = aiVar;
        this.u = false;
        d(HttpStatus.SC_MULTIPLE_CHOICES, 220);
        gz gzVar = new gz(1001);
        gzVar.c = com.donglh.narutoninjasaga.c.a.rB;
        this.y = new fc(gzVar, this.aM, a_() - 3, (this.aN - 33) + 6, this);
        this.N = a(this.y.c() + 7, (a_() + (this.aN - 33)) - 25, com.donglh.narutoninjasaga.c.a.gb, 100);
        this.N.a = com.donglh.narutoninjasaga.e.c.g("clan" + d.a().aE);
        this.y.a(this.N, 1);
        a(this.N, 0);
        this.O = a(this.y.c() + 87, (a_() + (this.aN - 33)) - 25, com.donglh.narutoninjasaga.c.a.qh, 101);
        this.O.a = com.donglh.narutoninjasaga.e.ay.a().a(24);
        this.y.a(this.O, 1);
        com.donglh.narutoninjasaga.e.f.c();
        this.a = new fw[gzVar.c.length];
        a(akVar);
        this.y.a(com.donglh.narutoninjasaga.e.n.n().bG);
        a(com.donglh.narutoninjasaga.e.n.n().bE);
    }

    public final void a(com.donglh.narutoninjasaga.e.ak akVar) {
        try {
            this.J.clear();
            this.L.clear();
            this.x.clear();
            this.M.clear();
            this.B = akVar.b.d();
            this.C = akVar.b.d();
            this.G = akVar.b.a.readLong();
            this.f = akVar.b.a.readShort();
            this.D = akVar.b.a.readInt();
            this.E = akVar.b.a.readInt();
            this.F = akVar.b.a.readInt();
            this.g = akVar.b.a.readInt();
            this.H = kk.c(kk.c, akVar.b.d(), 190);
            this.I = akVar.b.a.readByte();
            int readShort = akVar.b.a.readShort();
            for (int i = 0; i < readShort; i++) {
                dw dwVar = new dw();
                dwVar.i = akVar.b.a.readByte();
                dwVar.b = akVar.b.a.readByte();
                dwVar.c = akVar.b.a.readByte();
                dwVar.d = akVar.b.a.readShort();
                dwVar.a = akVar.b.d();
                dwVar.j = akVar.b.a.readInt();
                dwVar.k = akVar.b.a.readInt();
                dwVar.l = akVar.b.a.readBoolean();
                dwVar.m = akVar.b.a.readBoolean();
                this.J.addElement(dwVar);
                if (dwVar.a.toLowerCase().equals(d.a().N.toLowerCase())) {
                    this.h = dwVar.i;
                }
            }
            int readShort2 = akVar.b.a.readShort();
            com.donglh.narutoninjasaga.e.f.c();
            int i2 = (this.aM - 8) - Input.Keys.BUTTON_MODE;
            for (int i3 = 0; i3 < readShort2; i3++) {
                this.L.addAll(kk.b(kk.c, "- " + akVar.j(), i2));
            }
            int readShort3 = akVar.b.a.readShort();
            for (int i4 = 0; i4 < readShort3; i4++) {
                q qVar = new q();
                qVar.a(akVar);
                this.x.add(qVar);
            }
            int a = akVar.b.a();
            for (int i5 = 0; i5 < a; i5++) {
                this.M.add(Integer.valueOf(akVar.b.a.readByte()));
            }
            akVar.b.a.readLong();
            this.R = akVar.b.a.readShort();
        } catch (Exception unused) {
        }
        com.donglh.narutoninjasaga.e.f.c();
        this.T = this.y.aZ + 7;
        this.A = 15;
        fw[] fwVarArr = this.a;
        int c = this.y.c() + 7;
        int i6 = this.T + 10;
        int i7 = (this.aM - 8) - 100;
        int i8 = this.A;
        com.donglh.narutoninjasaga.e.f.c();
        fwVarArr[0] = new fw((byte) 1, c, i6, i7, i8 * 11, this.A, 9 + this.H.length);
        v();
        this.A = 26;
        fw[] fwVarArr2 = this.a;
        com.donglh.narutoninjasaga.e.f.c();
        fwVarArr2[2] = new fw((byte) 1, this.y.c() + 8, this.T + 6, 190, (this.A * 6) + 1, this.A, com.donglh.narutoninjasaga.e.f.c().X.length);
        this.A = 15;
        fw[] fwVarArr3 = this.a;
        int c2 = this.y.c() + 7;
        int i9 = this.T + 10;
        com.donglh.narutoninjasaga.e.f.c();
        int i10 = (this.aM - 8) - 100;
        int i11 = this.A;
        com.donglh.narutoninjasaga.e.f.c();
        fwVarArr3[3] = new fw((byte) 1, c2, i9, i10, i11 * 11, this.A, this.L.size());
        com.donglh.narutoninjasaga.e.f.c();
        int c3 = this.y.c();
        com.donglh.narutoninjasaga.e.f.c();
        int i12 = c3 + 9;
        com.donglh.narutoninjasaga.e.f.c();
        com.donglh.narutoninjasaga.e.f.c();
        this.z = new fx((byte) 1, i12, this.T + 5, 192, 160, 32, com.donglh.narutoninjasaga.e.aw.c(this.x.size() < 30 ? 30 : this.x.size(), 6), 6);
        this.S = com.donglh.narutoninjasaga.c.a.di;
        this.P = -7812062;
        if (this.h == 5) {
            this.S = com.donglh.narutoninjasaga.c.a.dj;
            this.P = -2560;
        } else if (this.h == 4) {
            this.S = com.donglh.narutoninjasaga.c.a.dk;
            this.P = -3407617;
        } else if (this.h == 4) {
            this.S = com.donglh.narutoninjasaga.c.a.dl;
            this.P = -48128;
        }
        this.Q = kk.b(kk.c, this.S);
    }

    private void v() {
        this.K.clear();
        if (this.N.a) {
            for (int i = 0; i < this.J.size(); i++) {
                dw dwVar = (dw) this.J.elementAt(i);
                if (dwVar.l) {
                    this.K.add(dwVar);
                }
            }
        } else {
            this.K.addAll(this.J);
        }
        com.donglh.narutoninjasaga.e.c.a("clan" + d.a().aE, this.N.a);
        com.donglh.narutoninjasaga.e.f.c();
        int i2 = (this.aM - 8) - 100;
        com.donglh.narutoninjasaga.e.f.c();
        int i3 = this.T;
        com.donglh.narutoninjasaga.e.f.c();
        if (this.O.a) {
            this.A = 25;
            this.a[1] = new fw((byte) 1, this.y.c() + 7, i3 + 6, i2, this.A * 6, this.A, this.K.size());
            return;
        }
        this.A = 50;
        this.a[1] = new fw((byte) 1, this.y.c() + 7, i3 + 6, i2, this.A * 3, this.A, this.K.size());
    }

    @Override // com.donglh.narutoninjasaga.d.hj, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        super.a(guVar, i, i2);
        if (this.i.b == 0) {
            switch (guVar.b) {
                case 1001:
                    if (guVar.j.i >= 0) {
                        com.donglh.narutoninjasaga.e.n.n().bG = (byte) guVar.j.i;
                        this.y.a(guVar.j.i);
                        return;
                    }
                    return;
                case 1004:
                    int i3 = i2 - 72;
                    Vector vector = new Vector();
                    if (this.h == 5) {
                        vector.addElement(new hz(60, com.donglh.narutoninjasaga.c.a.dP));
                        vector.addElement(new hz(61, com.donglh.narutoninjasaga.c.a.dQ));
                        vector.addElement(new hz(62, com.donglh.narutoninjasaga.c.a.dR));
                    } else if (this.h == 4) {
                        vector.addElement(new hz(60, com.donglh.narutoninjasaga.c.a.dP));
                        vector.addElement(new hz(62, com.donglh.narutoninjasaga.c.a.dR));
                        vector.addElement(new hz(63, com.donglh.narutoninjasaga.c.a.dS));
                    } else {
                        vector.addElement(new hz(62, com.donglh.narutoninjasaga.c.a.dR));
                        vector.addElement(new hz(63, com.donglh.narutoninjasaga.c.a.dS));
                    }
                    if (vector.size() > 0) {
                        String[] strArr = new String[vector.size()];
                        short[] sArr = new short[vector.size()];
                        for (int i4 = 0; i4 < vector.size(); i4++) {
                            hz hzVar = (hz) vector.elementAt(i4);
                            strArr[i4] = hzVar.b;
                            sArr[i4] = (short) hzVar.a;
                        }
                        if (vector.size() == 2) {
                            i3 += 15;
                        }
                        this.n = a(this, i + 5, i3, new gz(0, sArr, strArr));
                        return;
                    }
                    return;
                case 1010:
                    if (guVar.j.i >= 0) {
                        a(guVar.j.i, i, i2);
                        return;
                    }
                    return;
                case 1011:
                    if (guVar.j.i >= 0) {
                        boolean contains = this.M.contains(Integer.valueOf(com.donglh.narutoninjasaga.e.f.c().X[guVar.j.i].a));
                        if (this.h != 5) {
                            contains = true;
                        }
                        String a = a(guVar.j.i, contains);
                        int i5 = guVar.j.aY + 27;
                        ix ixVar = new ix(i5, ((guVar.j.aZ + (guVar.j.i * guVar.j.f)) - guVar.j.d) + 1, 220, this, a, contains);
                        if (ixVar.aY > (com.donglh.narutoninjasaga.e.f.c().o - ixVar.aM) - this.aY) {
                            ixVar.aY = (short) ((com.donglh.narutoninjasaga.e.f.c().o - ixVar.aM) - this.aY);
                        }
                        if (ixVar.aY < i5) {
                            ixVar.aY = (short) (i5 - (ixVar.aM + 3));
                        }
                        if (ixVar.aY < (-this.aY)) {
                            ixVar.aY = (short) (-this.aY);
                        }
                        if (ixVar.aZ > (com.donglh.narutoninjasaga.e.f.c().p - ixVar.aN) - this.aZ) {
                            ixVar.aZ = (short) ((com.donglh.narutoninjasaga.e.f.c().p - ixVar.aN) - this.aZ);
                        }
                        a(ixVar);
                        this.n = ixVar;
                        return;
                    }
                    return;
                case 2001:
                    try {
                        if (this.V != null) {
                            com.donglh.narutoninjasaga.e.ak b = com.donglh.narutoninjasaga.e.ak.b((byte) -97);
                            b.a(this.V);
                            b.l();
                            return;
                        }
                        return;
                    } catch (Exception unused) {
                        return;
                    }
                case 2002:
                    a(false);
                    return;
                case 2003:
                    try {
                        com.donglh.narutoninjasaga.e.as asVar = com.donglh.narutoninjasaga.e.f.c().X[this.a[2].i];
                        com.donglh.narutoninjasaga.e.ak b2 = com.donglh.narutoninjasaga.e.ak.b((byte) -68);
                        b2.a(asVar.a);
                        b2.l();
                        return;
                    } catch (Exception unused2) {
                        return;
                    }
                case 2004:
                    try {
                        com.donglh.narutoninjasaga.e.ak b3 = com.donglh.narutoninjasaga.e.ak.b((byte) -99);
                        b3.a(this.V);
                        b3.a(5);
                        b3.l();
                        return;
                    } catch (Exception unused3) {
                        return;
                    }
                case 5001:
                    int i6 = guVar.j.i;
                    fw fwVar = guVar.j;
                    this.z.i = -1;
                    fwVar.i = i6;
                    if (guVar.j.i >= 0) {
                        this.n = a(guVar, this, (q) this.x.get(guVar.j.i));
                        return;
                    }
                    return;
                default:
                    return;
            }
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v47, types: [com.donglh.narutoninjasaga.d.hq] */
    /* JADX WARN: Type inference failed for: r0v48, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v53, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r0v56, types: [com.donglh.narutoninjasaga.d.hj, com.donglh.narutoninjasaga.d.hq] */
    /* JADX WARN: Type inference failed for: r0v57, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v62, types: [com.donglh.narutoninjasaga.e.n] */
    /* JADX WARN: Type inference failed for: r0v65, types: [com.donglh.narutoninjasaga.d.hq] */
    /* JADX WARN: Type inference failed for: r0v66, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v71, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r0v74, types: [com.donglh.narutoninjasaga.d.hq] */
    /* JADX WARN: Type inference failed for: r0v75, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v80, types: [java.lang.String] */
    @Override // com.donglh.narutoninjasaga.d.hj, com.donglh.narutoninjasaga.d.cn
    public final void a(int i, Object obj, co coVar) {
        super.a(i, obj, coVar);
        if (this.i.b == 0) {
            switch (i) {
                case Input.Keys.W /* 51 */:
                    ?? r0 = this;
                    try {
                        dw b = r0.b(((gz) obj).a);
                        com.donglh.narutoninjasaga.e.n.n();
                        r0 = b.a;
                        com.donglh.narutoninjasaga.e.n.e((String) r0);
                        return;
                    } catch (Exception e) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                        return;
                    }
                case Input.Keys.X /* 52 */:
                    ?? r02 = this;
                    try {
                        dw b2 = r02.b(((gz) obj).a);
                        com.donglh.narutoninjasaga.e.n.n();
                        r02 = b2.a;
                        com.donglh.narutoninjasaga.e.n.d((String) r02);
                        return;
                    } catch (Exception e2) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r02);
                        return;
                    }
                case Input.Keys.Y /* 53 */:
                    gz gzVar = (gz) obj;
                    ?? r03 = this;
                    try {
                        r03.u();
                        r03.a(r03.q());
                        dw b3 = r03.b(gzVar.a);
                        r03 = com.donglh.narutoninjasaga.e.n.n();
                        r03.a(b3.a, r03);
                        return;
                    } catch (Exception e3) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r03);
                        return;
                    }
                case Input.Keys.Z /* 54 */:
                    ?? r04 = this;
                    try {
                        dw b4 = r04.b(((gz) obj).a);
                        com.donglh.narutoninjasaga.e.n.n();
                        r04 = b4.a;
                        com.donglh.narutoninjasaga.e.n.h((String) r04);
                        return;
                    } catch (Exception e4) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r04);
                        return;
                    }
                case Input.Keys.COMMA /* 55 */:
                    a((gz) obj, 0);
                    return;
                case Input.Keys.PERIOD /* 56 */:
                    a((gz) obj, 4);
                    return;
                case Input.Keys.ALT_LEFT /* 57 */:
                    a((gz) obj, 3);
                    return;
                case Input.Keys.ALT_RIGHT /* 58 */:
                    try {
                        this.V = b(((gz) obj).a).a;
                        com.donglh.narutoninjasaga.e.f.c().am.a(com.donglh.narutoninjasaga.c.a.dF + this.V + com.donglh.narutoninjasaga.c.a.dG, 2001, this);
                        return;
                    } catch (Exception unused) {
                        return;
                    }
                case Input.Keys.SHIFT_LEFT /* 59 */:
                    try {
                        com.donglh.narutoninjasaga.e.n.n().a((cn) new bb(this.s, b(((gz) obj).a).a));
                        return;
                    } catch (Exception unused2) {
                        return;
                    }
                case Input.Keys.SHIFT_RIGHT /* 60 */:
                    com.donglh.narutoninjasaga.e.n.n().a((cn) new aa(this.s));
                    return;
                case Input.Keys.TAB /* 61 */:
                    com.donglh.narutoninjasaga.e.n.n().a((cn) new bj(this.s, this.g));
                    return;
                case Input.Keys.SPACE /* 62 */:
                    com.donglh.narutoninjasaga.e.n.n().a((cn) new gg(this.s));
                    return;
                case Input.Keys.SYM /* 63 */:
                    a(true);
                    return;
                case 64:
                    try {
                        dw b5 = b(((gz) obj).a);
                        com.donglh.narutoninjasaga.e.ak b6 = com.donglh.narutoninjasaga.e.ak.b((byte) -91);
                        b6.a(b5.a);
                        b6.l();
                        return;
                    } catch (Exception unused3) {
                        return;
                    }
                case Input.Keys.ENVELOPE /* 65 */:
                    try {
                        this.V = b(((gz) obj).a).a;
                        com.donglh.narutoninjasaga.e.f.c().am.a(com.donglh.narutoninjasaga.e.aw.b(com.donglh.narutoninjasaga.c.a.oA, this.V), 2004, this);
                        return;
                    } catch (Exception unused4) {
                        return;
                    }
                case Input.Keys.ENTER /* 66 */:
                case 67:
                case Input.Keys.GRAVE /* 68 */:
                case Input.Keys.MINUS /* 69 */:
                case Input.Keys.EQUALS /* 70 */:
                case Input.Keys.LEFT_BRACKET /* 71 */:
                case Input.Keys.RIGHT_BRACKET /* 72 */:
                case Input.Keys.BACKSLASH /* 73 */:
                case Input.Keys.SEMICOLON /* 74 */:
                case Input.Keys.APOSTROPHE /* 75 */:
                case Input.Keys.SLASH /* 76 */:
                case Input.Keys.AT /* 77 */:
                case Input.Keys.NUM /* 78 */:
                case Input.Keys.HEADSETHOOK /* 79 */:
                case Input.Keys.FOCUS /* 80 */:
                case Input.Keys.PLUS /* 81 */:
                case Input.Keys.MENU /* 82 */:
                case Input.Keys.NOTIFICATION /* 83 */:
                case Input.Keys.SEARCH /* 84 */:
                case Input.Keys.MEDIA_PLAY_PAUSE /* 85 */:
                case Input.Keys.MEDIA_STOP /* 86 */:
                case Input.Keys.MEDIA_NEXT /* 87 */:
                case Input.Keys.MEDIA_PREVIOUS /* 88 */:
                case Input.Keys.MEDIA_REWIND /* 89 */:
                case Input.Keys.MEDIA_FAST_FORWARD /* 90 */:
                case Input.Keys.MUTE /* 91 */:
                case Input.Keys.PAGE_UP /* 92 */:
                case Input.Keys.PAGE_DOWN /* 93 */:
                case Input.Keys.PICTSYMBOLS /* 94 */:
                case Input.Keys.SWITCH_CHARSET /* 95 */:
                case Input.Keys.BUTTON_A /* 96 */:
                case Input.Keys.BUTTON_B /* 97 */:
                case Input.Keys.BUTTON_C /* 98 */:
                case Input.Keys.BUTTON_X /* 99 */:
                default:
                    return;
                case 100:
                    v();
                    return;
                case 101:
                    com.donglh.narutoninjasaga.e.ay.a().a(24, this.O.a);
                    v();
                    return;
            }
        }
    }

    private void a(boolean z) {
        try {
            if (z) {
                com.donglh.narutoninjasaga.e.f.c().am.a(com.donglh.narutoninjasaga.c.a.dE, 2002, this);
            } else {
                com.donglh.narutoninjasaga.e.ak.b((byte) -96).l();
            }
        } catch (Exception unused) {
        }
    }

    private void a(gz gzVar, int i) {
        try {
            dw b = b(gzVar.a);
            com.donglh.narutoninjasaga.e.ak b2 = com.donglh.narutoninjasaga.e.ak.b((byte) -99);
            b2.a(b.a);
            b2.a(i);
            b2.l();
        } catch (Exception unused) {
        }
    }

    private void a(int i, int i2, int i3) {
        Vector vector = new Vector();
        dw b = b(i);
        if (!b.a.equals(d.a().N)) {
            if (this.h == 5) {
                if (b.i > 0) {
                    vector.addElement(new hz(55, com.donglh.narutoninjasaga.c.a.dH));
                } else {
                    vector.addElement(new hz(65, com.donglh.narutoninjasaga.c.a.oz));
                    vector.addElement(new hz(56, com.donglh.narutoninjasaga.c.a.dI));
                    vector.addElement(new hz(57, com.donglh.narutoninjasaga.c.a.dJ));
                }
                vector.addElement(new hz(58, com.donglh.narutoninjasaga.c.a.dK));
                vector.addElement(new hz(59, com.donglh.narutoninjasaga.c.a.dL));
            } else if (this.h == 4 && b.i <= 3) {
                if (b.i > 0) {
                    vector.addElement(new hz(55, com.donglh.narutoninjasaga.c.a.dH));
                } else {
                    vector.addElement(new hz(57, com.donglh.narutoninjasaga.c.a.dJ));
                }
                vector.addElement(new hz(58, com.donglh.narutoninjasaga.c.a.dK));
            }
            if (this.h > 0 && this.h > b.i) {
                if (b.m) {
                    vector.addElement(new hz(64, com.donglh.narutoninjasaga.c.a.dM));
                } else {
                    vector.addElement(new hz(64, com.donglh.narutoninjasaga.c.a.dN));
                }
            }
            if (!com.donglh.narutoninjasaga.e.n.n().j(b.a)) {
                vector.addElement(new hz(51, com.donglh.narutoninjasaga.c.a.cb));
            }
            vector.addElement(new hz(52, com.donglh.narutoninjasaga.c.a.dO));
            vector.addElement(new hz(53, com.donglh.narutoninjasaga.c.a.cc));
            vector.addElement(new hz(54, com.donglh.narutoninjasaga.c.a.cS));
        }
        if (vector.size() > 0) {
            String[] strArr = new String[vector.size()];
            short[] sArr = new short[vector.size()];
            for (int i4 = 0; i4 < vector.size(); i4++) {
                hz hzVar = (hz) vector.elementAt(i4);
                strArr[i4] = hzVar.b;
                sArr[i4] = (short) hzVar.a;
            }
            this.n = a(this, i2 + 25, i3, new gz(i, sArr, strArr));
        }
    }

    @Override // com.donglh.narutoninjasaga.d.hj, com.donglh.narutoninjasaga.d.cn
    public final void a_(int i) {
    }

    @Override // com.donglh.narutoninjasaga.d.hj, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        Vector c = super.c();
        if (this.i.b == 0) {
            c.addElement(this.y.b());
            switch (this.y.a.b) {
                case 0:
                    c.addElement(this.a[this.y.a.b].a(1003, this));
                    break;
                case 1:
                    c.addElement(this.a[this.y.a.b].a(1010, this));
                    break;
                case 2:
                    c.addElement(this.z.a(5001, this));
                    break;
                case 3:
                    c.addElement(this.a[2].a(1011, this));
                    break;
                case 4:
                    c.addElement(this.a[3].a(1003, this));
                    break;
            }
            if (d.a().am != null) {
                com.donglh.narutoninjasaga.e.f.c();
                c.addElement(new gu(1004, this.U - 2, (a_() + (this.aN - 33)) - 14, this.U + this.Q + 2, a_() + (this.aN - 33), null, this));
            }
        }
        return c;
    }

    @Override // com.donglh.narutoninjasaga.d.hj, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        super.a(lVar);
        if (this.i.b == 0) {
            switch (this.y.a.b) {
                case 0:
                    this.y.a(lVar, 61);
                    a(lVar, this.aY, this.aZ);
                    a(lVar, this.a[0]);
                    kk.a(kk.c, lVar, com.donglh.narutoninjasaga.c.a.dV + this.B, 6, 7 + (0 * this.a[0].f), 0, -1, 0);
                    int i = this.E;
                    int i2 = i;
                    if (i == 0) {
                        i2 = 1;
                    }
                    kk.a(kk.c, lVar, com.donglh.narutoninjasaga.c.a.dW + this.f + " + " + ((this.D * 100) / i2) + "%", 6, 7 + (1 * this.a[0].f), 0, -1, 0);
                    kk.a(kk.c, lVar, com.donglh.narutoninjasaga.c.a.dX + com.donglh.narutoninjasaga.e.aw.c(this.D) + "/" + com.donglh.narutoninjasaga.e.aw.c(this.E), 6, 7 + (2 * this.a[0].f), 0, -1, 0);
                    String str = com.donglh.narutoninjasaga.c.a.dT + com.donglh.narutoninjasaga.e.aw.c(this.F) + "/" + com.donglh.narutoninjasaga.e.aw.c(e());
                    kk.a(kk.c, lVar, str, 6, 7 + (3 * this.a[0].f), 0, -1, 0);
                    if (d.a().am != null) {
                        if (this.F >= e()) {
                            kk.a(kk.c, lVar, "(" + com.donglh.narutoninjasaga.c.a.dZ + ")", kk.b(kk.c, str) + 10, 6 + (3 * this.a[0].f), 0, -16742145, 0);
                        } else {
                            kk.a(kk.c, lVar, "(" + com.donglh.narutoninjasaga.c.a.dY + ")", kk.b(kk.c, str) + 10, 6 + (3 * this.a[0].f), 0, -3089954, 0);
                        }
                    }
                    kk.a(kk.c, lVar, com.donglh.narutoninjasaga.c.a.ea + this.J.size() + "/" + (15 + (this.f * 5)) + com.donglh.narutoninjasaga.e.aw.b(com.donglh.narutoninjasaga.c.a.qj, new StringBuilder().append(this.R).toString()), 6, 7 + (4 * this.a[0].f), 0, -1, 0);
                    kk.a(kk.c, lVar, com.donglh.narutoninjasaga.c.a.eb + com.donglh.narutoninjasaga.e.aw.c(this.g) + com.donglh.narutoninjasaga.c.a.ee, 6, 7 + (5 * this.a[0].f), 0, -1, 0);
                    kk.a(kk.c, lVar, com.donglh.narutoninjasaga.c.a.ec + this.I, 6, 7 + (6 * this.a[0].f), 0, -1, 0);
                    if (d.a().am != null) {
                        kk.a(kk.c, lVar, com.donglh.narutoninjasaga.c.a.ed + com.donglh.narutoninjasaga.e.aw.e(this.G), 6, 7 + (7 * this.a[0].f), 0, -1, 0);
                    } else {
                        kk.a(kk.c, lVar, com.donglh.narutoninjasaga.c.a.ed, 6, 7 + (7 * this.a[0].f), 0, -1, 0);
                    }
                    for (int i3 = 0; i3 < this.H.length; i3++) {
                        kk.a(kk.c, lVar, this.H[i3], 6, 7 + ((i3 + 8) * this.a[0].f), 0, -2560, 0);
                    }
                    b(lVar);
                    break;
                case 1:
                    this.y.a(lVar, 61);
                    d(lVar);
                    break;
                case 2:
                    this.y.a(lVar, 61);
                    e(lVar);
                    break;
                case 3:
                    this.y.a(lVar, -11);
                    f(lVar);
                    break;
                case 4:
                    this.y.a(lVar, -11);
                    g(lVar);
                    break;
            }
            if (d.a().am != null) {
                com.donglh.narutoninjasaga.e.f.c();
                this.U = 18;
                com.donglh.narutoninjasaga.e.f.c();
                kk.a(kk.c, lVar, this.S, this.U, (a_() + (this.aN - 33)) - 10, 0, this.P, -10275328);
                lVar.e(this.P);
                lVar.a(this.U, (a_() + (this.aN - 33)) - 5, this.U + this.Q, (a_() + (this.aN - 33)) - 9);
            }
        }
    }

    private void d(l lVar) {
        a(lVar, this.a[1]);
        for (int i = 0; i < this.K.size(); i++) {
            dw dwVar = (dw) this.K.get(i);
            if (i == this.a[this.y.a.b].i) {
                a(lVar, 0, i * this.a[this.y.a.b].f, this.a[this.y.a.b].aM, this.a[this.y.a.b].f - 2, 4, 84, 5, 1, 1);
            } else {
                a(lVar, 0, i * this.a[this.y.a.b].f, this.a[this.y.a.b].aM, this.a[this.y.a.b].f - 2, -17, 84, 5, 1, 1);
            }
            if (!this.O.a) {
                a.a(lVar, 92, 0, 22, (i * this.a[this.y.a.b].f) + 24, 86, (byte) 3);
                a.a(lVar, dwVar.a(), 0, 22, (i * this.a[this.y.a.b].f) + 24, 83, (byte) 3);
                if (dwVar.l) {
                    com.donglh.narutoninjasaga.e.r.a(lVar, HttpStatus.SC_MOVED_TEMPORARILY, 0, 5, (i * this.a[this.y.a.b].f) + 32, 20);
                } else {
                    com.donglh.narutoninjasaga.e.r.a(lVar, HttpStatus.SC_NOT_MODIFIED, 0, 5, (i * this.a[this.y.a.b].f) + 32, 20);
                }
                if (dwVar.m) {
                    com.donglh.narutoninjasaga.e.r.a(lVar, 584, 0, this.a[this.y.a.b].aM - 10, (i * this.a[this.y.a.b].f) + 36, 3);
                }
                int i2 = 46;
                int i3 = (i * this.a[this.y.a.b].f) + 8;
                if (i == this.a[this.y.a.b].j && !dwVar.a.equals(d.a().N)) {
                    i2 = 46 + 1;
                    i3++;
                }
                String str = "";
                if (dwVar.i == 5) {
                    str = " (" + com.donglh.narutoninjasaga.c.a.dj + ")";
                } else if (dwVar.i == 4) {
                    str = " (" + com.donglh.narutoninjasaga.c.a.dk + ")";
                } else if (dwVar.i == 3) {
                    str = " (" + com.donglh.narutoninjasaga.c.a.dl + ")";
                }
                kk.a(kk.c, lVar, com.donglh.narutoninjasaga.c.a.ry[3] + " " + dwVar.a + str, i2, i3, 0, -1);
                kk.a(kk.c, lVar, com.donglh.narutoninjasaga.c.a.ry[4] + " " + com.donglh.narutoninjasaga.e.f.c().R[dwVar.b].b + ", " + com.donglh.narutoninjasaga.c.a.ry[5] + " " + ((int) dwVar.d), i2, i3 + 10, 0, -1);
                kk.a(kk.c, lVar, com.donglh.narutoninjasaga.c.a.dT + " " + com.donglh.narutoninjasaga.e.aw.c(dwVar.k), i2, i3 + 20, 0, -1);
                kk.a(kk.c, lVar, com.donglh.narutoninjasaga.c.a.dU + " " + com.donglh.narutoninjasaga.e.aw.c(dwVar.j), i2, i3 + 30, 0, -1);
            } else {
                a.a(lVar, 92, 0, 11, (i * this.a[this.y.a.b].f) + 11, 42, (byte) 3);
                a.a(lVar, dwVar.a(), 0, 11, (i * this.a[this.y.a.b].f) + 11, 40, (byte) 3);
                if (dwVar.l) {
                    a.a(lVar, (int) HttpStatus.SC_MOVED_TEMPORARILY, 0, 3, (i * this.a[this.y.a.b].f) + 14, 60, (byte) 20);
                } else {
                    a.a(lVar, (int) HttpStatus.SC_NOT_MODIFIED, 0, 3, (i * this.a[this.y.a.b].f) + 14, 60, (byte) 20);
                }
                if (dwVar.m) {
                    a.a(lVar, 584, 0, this.a[this.y.a.b].aM - 6, (i * this.a[this.y.a.b].f) + 15, 60, (byte) 3);
                }
                int i4 = 24;
                int i5 = (i * this.a[this.y.a.b].f) + 7;
                if (i == this.a[this.y.a.b].j && !dwVar.a.equals(d.a().N)) {
                    i4 = 24 + 1;
                    i5++;
                }
                String str2 = "";
                if (dwVar.i == 5) {
                    str2 = " (" + com.donglh.narutoninjasaga.c.a.dj + ")";
                } else if (dwVar.i == 4) {
                    str2 = " (" + com.donglh.narutoninjasaga.c.a.dk + ")";
                } else if (dwVar.i == 3) {
                    str2 = " (" + com.donglh.narutoninjasaga.c.a.dl + ")";
                }
                kk.a(kk.a, lVar, com.donglh.narutoninjasaga.c.a.ry[3] + " " + dwVar.a + str2 + ", " + com.donglh.narutoninjasaga.c.a.ry[4] + " " + com.donglh.narutoninjasaga.e.f.c().R[dwVar.b].b + ", " + com.donglh.narutoninjasaga.c.a.ry[5] + " " + ((int) dwVar.d), i4, i5, 0, -1);
                kk.a(kk.a, lVar, com.donglh.narutoninjasaga.c.a.dT + " " + com.donglh.narutoninjasaga.e.aw.c(dwVar.k) + ", " + com.donglh.narutoninjasaga.c.a.dU + " " + com.donglh.narutoninjasaga.e.aw.c(dwVar.j), i4, i5 + 10, 0, -1);
            }
        }
        b(lVar);
    }

    private void e(l lVar) {
        a(lVar, this.z);
        for (int i = 0; i < this.z.g; i++) {
            for (int i2 = 0; i2 < this.z.o; i2++) {
                if (this.z.b(i)) {
                    if ((i * this.z.o) + i2 < this.x.size()) {
                        b(lVar, i2 * this.z.f, i * this.z.f, (q) this.x.get((i * this.z.o) + i2), (i * this.z.o) + i2 == this.z.i);
                    } else {
                        b(lVar, i2 * this.z.f, i * this.z.f, (q) null, (i * this.z.o) + i2 == this.z.i);
                    }
                }
            }
        }
        b(lVar);
    }

    private void f(l lVar) {
        if (d.a().am != null) {
            a(lVar, this.a[2]);
            for (int i = 0; i < com.donglh.narutoninjasaga.e.f.c().X.length; i++) {
                int i2 = i * this.a[2].f;
                if (this.a[2].c(i2)) {
                    if (this.a[2].i == i) {
                        a(lVar, 28, i2 + 1, this.a[2].aM - 29, 25, -15, 84, 5, 1, 1);
                    } else {
                        a(lVar, 28, i2 + 1, this.a[2].aM - 29, 25, -14, 84, 5, 1, 1);
                    }
                    kk.a(kk.d, lVar, com.donglh.narutoninjasaga.e.f.c().X[i].b, 35, i2 + 8, 0, -7812062, 0);
                    if (this.M.contains(Integer.valueOf(com.donglh.narutoninjasaga.e.f.c().X[i].a))) {
                        kk.a(kk.c, lVar, com.donglh.narutoninjasaga.c.a.hd, 35, i2 + 18, 0, -2560, 0);
                    } else {
                        kk.a(kk.c, lVar, com.donglh.narutoninjasaga.c.a.ah + " " + com.donglh.narutoninjasaga.e.aw.c(com.donglh.narutoninjasaga.e.f.c().X[i].g) + " " + com.donglh.narutoninjasaga.c.a.aP, 35, i2 + 18, 0, -1, 0);
                    }
                    com.donglh.narutoninjasaga.e.r.a(lVar, com.donglh.narutoninjasaga.e.f.c().X[i].f, 0, 1, i2 + 1, 20);
                }
            }
            if (this.a[2].i >= 0) {
                lVar.e(-1);
                lVar.b(0, this.a[2].i * this.a[2].f, 26, 26);
            }
            b(lVar);
            com.donglh.narutoninjasaga.e.f.c();
        }
    }

    private void g(l lVar) {
        a(lVar, this.a[3]);
        for (int i = 0; i < this.L.size(); i++) {
            kk.a(lVar, (String) this.L.get(i), 6, 5 + (i * this.a[3].f), 0, -6488);
        }
        b(lVar);
        this.a[3].d(lVar, -10, -16);
    }

    @Override // com.donglh.narutoninjasaga.d.hj, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void b() {
        super.b();
        if (this.i.b == 0) {
            this.y.a();
            this.z.a();
            for (int i = 0; i < this.a.length; i++) {
                if (this.a[i] != null) {
                    this.a[i].a();
                }
            }
        }
    }

    private dw b(int i) {
        dw dwVar = null;
        if (i >= 0) {
            if (this.y.a.b == 1) {
                dwVar = (dw) this.K.elementAt(i);
            } else {
                dwVar = (dw) this.J.elementAt(i);
            }
        }
        return dwVar;
    }

    @Override // com.donglh.narutoninjasaga.d.hj, com.donglh.narutoninjasaga.d.el
    public final void a(int i) {
        super.a(i);
        if (this.c != null && this.i.b == q()) {
            this.c.a(2);
            t();
        }
        if (this.y != null) {
            this.y.d();
            if (i == 0) {
                this.y.a(this.y.a.b);
            }
        }
    }

    private String a(int i, boolean z) {
        com.donglh.narutoninjasaga.e.as asVar = com.donglh.narutoninjasaga.e.f.c().X[i];
        Vector vector = new Vector();
        vector.addElement("c#moc" + asVar.b);
        if (asVar.c != null && asVar.c.length() > 0) {
            vector.addElement(asVar.c);
        }
        if (!z) {
            vector.addElement("c#yellow" + com.donglh.narutoninjasaga.c.a.ah + " " + com.donglh.narutoninjasaga.e.aw.c(com.donglh.narutoninjasaga.e.f.c().X[i].g) + " " + com.donglh.narutoninjasaga.c.a.aP);
        }
        vector.addElement("c#green" + com.donglh.narutoninjasaga.c.a.cM);
        a(vector, asVar);
        Vector vector2 = new Vector();
        for (int i2 = 0; i2 < vector.size(); i2++) {
            String valueOf = String.valueOf(vector.elementAt(i2));
            if (i2 == 0) {
                Vector a = kk.a(kk.d, valueOf, 180);
                for (int i3 = 0; i3 < a.size(); i3++) {
                    vector2.add(new hz(0, (String) a.elementAt(i3)));
                }
            } else {
                Vector a2 = kk.a(kk.c, valueOf, 180);
                for (int i4 = 0; i4 < a2.size(); i4++) {
                    vector2.add(new hz(1, (String) a2.elementAt(i4)));
                }
            }
        }
        String str = "";
        hz[] hzVarArr = new hz[vector2.size()];
        for (int i5 = 0; i5 < vector2.size(); i5++) {
            hzVarArr[i5] = (hz) vector2.elementAt(i5);
            str = str + hzVarArr[i5].b;
            if (i5 < hzVarArr.length - 1) {
                str = str + "\r\n";
            }
        }
        return str;
    }

    private void a(Vector vector, com.donglh.narutoninjasaga.e.as asVar) {
        if (this.f >= asVar.d) {
            vector.addElement("c#white" + com.donglh.narutoninjasaga.c.a.rz[3] + asVar.d);
        } else {
            vector.addElement("c#red" + com.donglh.narutoninjasaga.c.a.rz[3] + asVar.d);
        }
        for (s sVar : asVar.b()) {
            String a = sVar.a(0);
            if (a != null && a.length() > 0) {
                vector.addElement(a);
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.hj, com.donglh.narutoninjasaga.d.ej
    public final void d() {
        try {
            if (com.donglh.narutoninjasaga.e.n.n().J() != null && com.donglh.narutoninjasaga.e.n.n().J().a == 44) {
                com.donglh.narutoninjasaga.e.n.n();
                com.donglh.narutoninjasaga.e.n.l(93);
            }
        } catch (Exception unused) {
        }
        super.d();
    }
}
