package com.donglh.narutoninjasaga.d;

import java.util.Vector;
/* compiled from: LangLa_kx.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/hp.class */
public final class hp extends em {
    private dg[] a;
    private ib[] b;
    private fw c;
    private fw[] d;

    public hp(com.donglh.narutoninjasaga.e.ai aiVar, com.donglh.narutoninjasaga.e.ak akVar) {
        this.b = new ib[0];
        try {
            this.s = aiVar;
            d(315, 218);
            this.u = false;
            this.b = new ib[akVar.b.a.readShort()];
            for (int i = 0; i < this.b.length; i++) {
                this.b[i] = new ib();
                this.b[i].a = i + 1;
                this.b[i].b = akVar.b.d();
                this.b[i].c = akVar.b.a.readInt();
                this.b[i].g = akVar.b.a.readInt() + "%";
                this.b[i].d = akVar.b.a.readInt();
                this.b[i].e = akVar.b.a.readInt();
                this.b[i].f = akVar.j();
            }
            e();
        } catch (Exception unused) {
        }
    }

    private void e() {
        this.c = new fw((byte) 1, 4, a_() + 28, this.aM - 8, 154, 22, this.b.length);
        this.a = new dg[this.b.length];
        this.d = new fw[this.b.length];
        for (int i = 0; i < this.a.length; i++) {
            this.a[i] = new dg(261, i * this.c.f, com.donglh.narutoninjasaga.c.a.ma, this, i + 3000, 15);
            this.a[i].a_(37, 18);
            this.d[i] = new gb(this.a[i].aY, this.a[i].aZ, this.a[i].aM, this.a[i].aN, this.a[i].aN, this.c);
        }
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void b() {
        this.c.a();
        for (int i = 0; i < this.a.length; i++) {
            this.a[i].b();
            this.a[i].b();
            if (this.d[i].j != -1) {
                this.a[i].f = true;
            } else {
                this.a[i].f = false;
            }
        }
        if (com.donglh.narutoninjasaga.e.n.n().aC == 14 && com.donglh.narutoninjasaga.e.n.n().aD == 0) {
            com.donglh.narutoninjasaga.e.f.c().aE.a((this.aY + this.aM) - 5, this.aZ + 5);
            com.donglh.narutoninjasaga.e.f.c().aE.a = true;
        }
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a(int i, Object obj, co coVar) {
        switch (i) {
            case 2002:
                gz gzVar = (gz) obj;
                try {
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.h(this.b[gzVar.a].b);
                    return;
                } catch (Exception unused) {
                    return;
                }
            case 2003:
                gz gzVar2 = (gz) obj;
                try {
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.e(this.b[gzVar2.a].b);
                    return;
                } catch (Exception unused2) {
                    return;
                }
            case 2004:
                gz gzVar3 = (gz) obj;
                try {
                    ho.c = this.b[gzVar3.a].b;
                    com.donglh.narutoninjasaga.e.n.n().a(this.b[gzVar3.a].b, new hk(com.donglh.narutoninjasaga.e.n.n(), 6));
                    return;
                } catch (Exception unused3) {
                    return;
                }
            default:
                return;
        }
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a_(int i) {
    }

    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        Vector c = super.c();
        for (int i = 0; i < this.a.length; i++) {
            int i2 = this.c.aY + this.a[i].aY;
            int i3 = (this.c.aZ + this.a[i].aZ) - this.c.d;
            if (com.donglh.narutoninjasaga.e.aw.a((int) this.c.aY, (int) this.c.aZ, this.c.aY + this.c.aM, this.c.aZ + this.c.aN, i2, i3) && com.donglh.narutoninjasaga.e.aw.a((int) this.c.aY, (int) this.c.aZ, this.c.aY + this.c.aM, this.c.aZ + this.c.aN, i2 + this.a[i].aM, i3 + this.a[i].aN)) {
                this.d[i].aY = (short) i2;
                this.d[i].aZ = (short) i3;
                c.addElement(new gu(i + 3000, this.d[i].aY, this.d[i].aZ, this.d[i].aY + this.d[i].aM, this.d[i].aZ + this.d[i].aN, this.d[i], this));
            }
        }
        c.addElement(this.c.a(1003, this));
        return c;
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        a(lVar, (int) this.aY, (int) this.aZ, (int) this.aM, (int) this.aN, com.donglh.narutoninjasaga.c.a.gY, (byte) 2, false);
        b(lVar, this.c);
        a(lVar, 0, -28, this.c.aM, 28, -11, 55, 56);
        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.bV, 40, -15, 2, -6488, -10275328);
        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.jt, 90, -15, 2, -6488, -10275328);
        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.di, 158, -15, 2, -6488, -10275328);
        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.dj, 224, -15, 2, -6488, -10275328);
        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.lZ, 279, -15, 2, -6488, -10275328);
        a(lVar, this.c);
        int i = lVar.a;
        int i2 = lVar.b;
        for (int i3 = 0; i3 < this.c.g; i3++) {
            if (this.c.b(i3)) {
                if (i3 == this.c.i) {
                    lVar.e(13136426);
                    lVar.c(0, i3 * this.c.f, this.c.aM - 50, this.c.f);
                }
                ib ibVar = this.b[i3];
                kk.b(kk.c, lVar, ibVar.f, 40, 8 + (i3 * this.c.f), 2, -3604601, -16777216);
                kk.b(kk.c, lVar, ibVar.c + "+" + ibVar.g, 90, 8 + (i3 * this.c.f), 2, -3604601, -16777216);
                kk.b(kk.c, lVar, ibVar.d + "/" + ibVar.e, 158, 8 + (i3 * this.c.f), 2, -3604601, -16777216);
                kk.b(kk.c, lVar, ibVar.b, 224, 8 + (i3 * this.c.f), 2, -3604601, -16777216);
                a(lVar, i + this.a[i3].aY, i2 + this.a[i3].aZ);
                this.a[i3].a(lVar);
                a(lVar, i, i2);
            }
        }
        b(lVar);
        this.c.d(lVar, -15, -10);
    }

    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        super.a(guVar, i, i2);
        if ((guVar.b >= 3000) & (guVar.b < 4000)) {
            try {
                db.a().h();
                com.donglh.narutoninjasaga.e.ak b = com.donglh.narutoninjasaga.e.ak.b((byte) -92);
                b.a(this.b[guVar.b - 3000].f);
                b.l();
                p();
                return;
            } catch (Exception unused) {
            }
        }
        switch (guVar.b) {
            case 1001:
                return;
            case 1003:
                if (guVar.j.i >= 0) {
                    int i3 = guVar.j.i;
                    Vector vector = new Vector();
                    vector.addElement(new hz(2002, com.donglh.narutoninjasaga.c.a.ca));
                    vector.addElement(new hz(2003, com.donglh.narutoninjasaga.c.a.cb));
                    vector.addElement(new hz(2004, com.donglh.narutoninjasaga.c.a.cc));
                    if (vector.size() > 0) {
                        String[] strArr = new String[vector.size()];
                        short[] sArr = new short[vector.size()];
                        for (int i4 = 0; i4 < vector.size(); i4++) {
                            hz hzVar = (hz) vector.elementAt(i4);
                            strArr[i4] = hzVar.b;
                            sArr[i4] = (short) hzVar.a;
                        }
                        this.n = a(this, i + 25, i2, new gz(i3, sArr, strArr));
                        return;
                    }
                    return;
                }
                return;
            default:
                return;
        }
    }

    @Override // com.donglh.narutoninjasaga.d.ej
    public final void d() {
        try {
            if (com.donglh.narutoninjasaga.e.n.n().J() != null && com.donglh.narutoninjasaga.e.n.n().J().a == 44) {
                com.donglh.narutoninjasaga.e.n.n();
                com.donglh.narutoninjasaga.e.n.l(93);
            }
        } catch (Exception unused) {
        }
        super.d();
    }
}
