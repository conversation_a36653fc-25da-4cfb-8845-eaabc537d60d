package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.net.HttpStatus;
import java.util.Vector;
/* compiled from: LangLa_kj.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/he.class */
public class he extends et {
    private int a;
    private fx[][] b;
    private fc c;
    private int d;

    /* JADX WARN: Type inference failed for: r1v11, types: [com.donglh.narutoninjasaga.d.fx[], com.donglh.narutoninjasaga.d.fx[][]] */
    public he(com.donglh.narutoninjasaga.e.ai aiVar, String[] strArr) {
        super(aiVar, strArr);
        this.a = 32;
        this.d = 32;
        gz gzVar = new gz(HttpStatus.SC_INTERNAL_SERVER_ERROR);
        gzVar.c = com.donglh.narutoninjasaga.c.a.st;
        this.c = new fc(gzVar, this.aM, a_() - 3, (this.aN - 33) + 6, this);
        int c = this.c.c() + 8;
        com.donglh.narutoninjasaga.e.f.c();
        int a_ = a_() + 11;
        com.donglh.narutoninjasaga.e.f.c();
        this.b = new fx[com.donglh.narutoninjasaga.c.a.qV.length];
        this.b[0] = new fx[14];
        this.b[0][0] = new fx((byte) 1, c, a_, this.a, this.a * 5, this.a, 5, 1);
        this.b[0][1] = new fx((byte) 1, c + 160, a_, this.a, this.a * 5, this.a, 5, 1);
        this.b[0][2] = new fx((byte) 1, c + this.a, a_ + (this.a << 2), this.a << 2, this.a, this.a, 1, 4);
        this.b[0][3] = new fx((byte) 1, c + this.a, a_ + (this.a * 3), this.a, this.a, this.a, 1, 1);
        this.b[0][4] = new fx((byte) 1, c + (this.a << 2), a_ + (this.a * 3), this.a, this.a, this.a, 1, 1);
        this.b[0][5] = new fx((byte) 1, this.aY + this.aM + 8 + (this.d << 2), 40 + (this.d << 1), this.d, this.d, this.d, 1, 1);
        this.b[0][6] = new fx((byte) 1, this.aY + this.aM + 8 + (this.d << 2), 40 + this.d, this.d, this.d, this.d, 1, 1);
        this.b[0][7] = new fx((byte) 1, this.aY + this.aM + 8, HttpStatus.SC_OK, this.d, this.d, this.d, 1, 1);
        this.b[0][8] = new fx((byte) 1, c, HttpStatus.SC_OK, this.d, this.d, this.d, 1, 1);
        this.b[0][9] = new fx((byte) 1, c + this.d, HttpStatus.SC_OK, this.d, this.d, this.d, 1, 1);
        this.b[0][10] = new fx((byte) 1, c + (this.d << 1), HttpStatus.SC_OK, this.d, this.d, this.d, 1, 1);
        this.b[0][11] = new fx((byte) 1, c + (this.d * 3), HttpStatus.SC_OK, this.d, this.d, this.d, 1, 1);
        this.b[0][12] = new fx((byte) 1, c + (this.d << 2), HttpStatus.SC_OK, this.d, this.d, this.d, 1, 1);
        this.b[0][13] = new fx((byte) 1, c + (this.d * 5), HttpStatus.SC_OK, this.d, this.d, this.d, 1, 1);
        this.b[1] = new fx[14];
        this.b[1][0] = new fx((byte) 1, c, a_, this.a, this.a * 5, this.a, 5, 1);
        this.b[1][1] = new fx((byte) 1, c + 160, a_, this.a, this.a * 5, this.a, 5, 1);
        this.b[1][2] = new fx((byte) 1, c + this.a, a_ + (this.a << 2), this.a << 2, this.a, this.a, 1, 4);
        this.b[1][3] = new fx((byte) 1, c + this.a, a_ + (this.a * 3), this.a, this.a, this.a, 1, 1);
        this.b[1][4] = new fx((byte) 1, c + (this.a << 2), a_ + (this.a * 3), this.a, this.a, this.a, 1, 1);
        this.b[1][5] = new fx((byte) 1, this.aY + this.aM + 8 + (this.d << 2), 40 + (this.d << 1), this.d, this.d, this.d, 1, 1);
        this.b[1][6] = new fx((byte) 1, this.aY + this.aM + 8 + (this.d << 2), 40 + this.d, this.d, this.d, this.d, 1, 1);
        this.b[1][7] = new fx((byte) 1, this.aY + this.aM + 8, HttpStatus.SC_OK, this.d, this.d, this.d, 1, 1);
        this.b[1][8] = new fx((byte) 1, c, HttpStatus.SC_OK, this.d, this.d, this.d, 1, 1);
        this.b[1][9] = new fx((byte) 1, c + this.d, HttpStatus.SC_OK, this.d, this.d, this.d, 1, 1);
        this.b[1][10] = new fx((byte) 1, c + (this.d << 1), HttpStatus.SC_OK, this.d, this.d, this.d, 1, 1);
        this.b[1][11] = new fx((byte) 1, c + (this.d * 3), HttpStatus.SC_OK, this.d, this.d, this.d, 1, 1);
        this.b[1][12] = new fx((byte) 1, c + (this.d << 2), HttpStatus.SC_OK, this.d, this.d, this.d, 1, 1);
        this.b[1][13] = new fx((byte) 1, c + (this.d * 5), HttpStatus.SC_OK, this.d, this.d, this.d, 1, 1);
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x006a: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:11:0x0069 */
    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public void a(l lVar) {
        Exception a;
        try {
            super.a(lVar);
            if (this.i.b == 1) {
                this.c.a(lVar, 61);
                this.c.a(lVar, this.aY, this.aZ);
                switch (this.c.a.b) {
                    case 0:
                        a(lVar, d.a().Y);
                        return;
                    case 1:
                        a(lVar, d.a().Z);
                        return;
                    default:
                        return;
                }
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a);
        }
    }

    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public Vector c() {
        Vector c = super.c();
        if (this.i.b == 1) {
            c.addElement(this.c.b());
            switch (this.c.a.b) {
                case 0:
                case 1:
                    for (int i = 0; i < this.b[this.c.a.b].length; i++) {
                        c.addElement(this.b[this.c.a.b][i].a(i + 1010, this));
                    }
                    break;
            }
        }
        return c;
    }

    /* JADX WARN: Not initialized variable reg: 0, insn: 0x0327: INVOKE  (r0 I:java.lang.Exception) type: STATIC call: com.donglh.narutoninjasaga.e.aw.a(java.lang.Exception):void, block:B:70:0x0326 */
    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public void a(gu guVar, int i, int i2) {
        Exception a;
        try {
            super.a(guVar, i, i2);
            if (this.i.b == 1) {
                q[] qVarArr = d.a().Y;
                if (this.c.a.b == 1) {
                    qVarArr = d.a().Z;
                }
                switch (guVar.b) {
                    case HttpStatus.SC_INTERNAL_SERVER_ERROR /* 500 */:
                        if (guVar.j.i >= 0) {
                            this.c.a(guVar.j.i);
                            return;
                        }
                        return;
                    case 1010:
                        a(guVar.j, guVar.j.i);
                        switch (guVar.j.i) {
                            case 0:
                                this.n = a(guVar, this, qVarArr[0]);
                                return;
                            case 1:
                                this.n = a(guVar, this, qVarArr[2]);
                                return;
                            case 2:
                                this.n = a(guVar, this, qVarArr[4]);
                                return;
                            case 3:
                                this.n = a(guVar, this, qVarArr[6]);
                                return;
                            case 4:
                                this.n = a(guVar, this, qVarArr[8]);
                                return;
                            default:
                                return;
                        }
                    case 1011:
                        a(guVar.j, guVar.j.i);
                        switch (guVar.j.i) {
                            case 0:
                                this.n = a(guVar, this, qVarArr[1]);
                                return;
                            case 1:
                                this.n = a(guVar, this, qVarArr[3]);
                                return;
                            case 2:
                                this.n = a(guVar, this, qVarArr[5]);
                                return;
                            case 3:
                                this.n = a(guVar, this, qVarArr[7]);
                                return;
                            case 4:
                                this.n = a(guVar, this, qVarArr[9]);
                                return;
                            default:
                                return;
                        }
                    case 1012:
                        a(guVar.j, guVar.j.i);
                        switch (guVar.j.i) {
                            case 0:
                                this.n = a(guVar, this, qVarArr[10]);
                                return;
                            case 1:
                                this.n = a(guVar, this, qVarArr[11]);
                                return;
                            case 2:
                                this.n = a(guVar, this, qVarArr[12]);
                                return;
                            case 3:
                                this.n = a(guVar, this, qVarArr[13]);
                                return;
                            default:
                                return;
                        }
                    case 1013:
                        a(guVar.j, guVar.j.i);
                        this.n = a(guVar, this, qVarArr[14]);
                        return;
                    case 1014:
                        a(guVar.j, guVar.j.i);
                        this.n = a(guVar, this, qVarArr[15]);
                        return;
                    case 1015:
                        return;
                    case 1016:
                        return;
                    case 1017:
                        return;
                    case 1018:
                        a(guVar.j, guVar.j.i);
                        this.n = a(guVar, this, qVarArr[16]);
                        return;
                    case 1019:
                        a(guVar.j, guVar.j.i);
                        this.n = a(guVar, this, qVarArr[17]);
                        return;
                    case 1020:
                        a(guVar.j, guVar.j.i);
                        this.n = a(guVar, this, qVarArr[18]);
                        return;
                    case 1021:
                        a(guVar.j, guVar.j.i);
                        this.n = a(guVar, this, qVarArr[19]);
                        return;
                    case 1022:
                        a(guVar.j, guVar.j.i);
                        this.n = a(guVar, this, qVarArr[20]);
                        return;
                    case 1023:
                        a(guVar.j, guVar.j.i);
                        this.n = a(guVar, this, qVarArr[21]);
                        return;
                    default:
                        return;
                }
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a(a);
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v1 */
    /* JADX WARN: Type inference failed for: r0v2 */
    /* JADX WARN: Type inference failed for: r0v25, types: [int] */
    /* JADX WARN: Type inference failed for: r0v3, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v60 */
    private void a(l lVar, q[] qVarArr) {
        q qVar = null;
        ?? r0 = 0;
        int i = 0;
        while (i < this.b[this.c.a.b][0].g) {
            try {
                switch (i) {
                    case 0:
                        qVar = qVarArr[0];
                        break;
                    case 1:
                        qVar = qVarArr[2];
                        break;
                    case 2:
                        qVar = qVarArr[4];
                        break;
                    case 3:
                        qVar = qVarArr[6];
                        break;
                    case 4:
                        qVar = qVarArr[8];
                        break;
                }
                l lVar2 = lVar;
                a(lVar2, this.b[this.c.a.b][0].aY, this.b[this.c.a.b][0].aZ + (i * this.b[this.c.a.b][0].f), qVar, i == this.b[this.c.a.b][0].i, com.donglh.narutoninjasaga.c.a.qN[i]);
                i++;
                r0 = lVar2;
            } catch (Exception e) {
                com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                return;
            }
        }
        int i2 = 0;
        while (i2 < this.b[this.c.a.b][1].g) {
            switch (i2) {
                case 0:
                    qVar = qVarArr[1];
                    break;
                case 1:
                    qVar = qVarArr[3];
                    break;
                case 2:
                    qVar = qVarArr[5];
                    break;
                case 3:
                    qVar = qVarArr[7];
                    break;
                case 4:
                    qVar = qVarArr[9];
                    break;
            }
            a(lVar, this.b[this.c.a.b][1].aY, this.b[this.c.a.b][1].aZ + (i2 * this.b[this.c.a.b][1].f), qVar, i2 == this.b[this.c.a.b][1].i, com.donglh.narutoninjasaga.c.a.qO[i2]);
            i2++;
        }
        int i3 = 0;
        while (i3 < this.b[this.c.a.b][2].o) {
            switch (i3) {
                case 0:
                    qVar = qVarArr[10];
                    break;
                case 1:
                    qVar = qVarArr[11];
                    break;
                case 2:
                    qVar = qVarArr[12];
                    break;
                case 3:
                    qVar = qVarArr[13];
                    break;
            }
            a(lVar, this.b[this.c.a.b][2].aY + (i3 * this.a), this.b[this.c.a.b][2].aZ, qVar, i3 == this.b[this.c.a.b][2].i, com.donglh.narutoninjasaga.c.a.qP[i3]);
            i3++;
        }
        a(lVar, this.b[this.c.a.b][3].aY, this.b[this.c.a.b][3].aZ, qVarArr[14], this.b[this.c.a.b][3].i == 0, com.donglh.narutoninjasaga.c.a.rK[0]);
        a(lVar, this.b[this.c.a.b][4].aY, this.b[this.c.a.b][4].aZ, qVarArr[15], this.b[this.c.a.b][4].i == 0, com.donglh.narutoninjasaga.c.a.rK[1]);
        a(lVar, this.b[this.c.a.b][8].aY, this.b[this.c.a.b][8].aZ, qVarArr[16], this.b[this.c.a.b][8].i == 0, com.donglh.narutoninjasaga.c.a.tx[0]);
        a(lVar, this.b[this.c.a.b][9].aY, this.b[this.c.a.b][9].aZ, qVarArr[17], this.b[this.c.a.b][9].i == 0, com.donglh.narutoninjasaga.c.a.tx[1]);
        a(lVar, this.b[this.c.a.b][10].aY, this.b[this.c.a.b][10].aZ, qVarArr[18], this.b[this.c.a.b][10].i == 0, com.donglh.narutoninjasaga.c.a.tx[2]);
        a(lVar, this.b[this.c.a.b][11].aY, this.b[this.c.a.b][11].aZ, qVarArr[19], this.b[this.c.a.b][11].i == 0, com.donglh.narutoninjasaga.c.a.tx[3]);
        a(lVar, this.b[this.c.a.b][12].aY, this.b[this.c.a.b][12].aZ, qVarArr[20], this.b[this.c.a.b][12].i == 0, com.donglh.narutoninjasaga.c.a.tx[4]);
        a(lVar, this.b[this.c.a.b][13].aY, this.b[this.c.a.b][13].aZ, qVarArr[21], this.b[this.c.a.b][13].i == 0, com.donglh.narutoninjasaga.c.a.tx[5]);
        d a = d.a();
        com.donglh.narutoninjasaga.e.f.c();
        com.donglh.narutoninjasaga.e.f.c();
        a.a(lVar, 190, 120, d.a().a(qVarArr, false), (com.donglh.narutoninjasaga.e.f.c().i / 8) % 2, (int) d.a().d, false);
        d a2 = d.a();
        com.donglh.narutoninjasaga.e.f.c();
        com.donglh.narutoninjasaga.e.f.c();
        r0 = a2.b(lVar, 190, 68);
    }

    private void a(fw fwVar, int i) {
        for (int i2 = 0; i2 < this.b.length; i2++) {
            if (this.b[i2] != null) {
                for (int i3 = 0; i3 < this.b[i2].length; i3++) {
                    if (this.b[i2][i3] != null) {
                        this.b[i2][i3].i = -1;
                    }
                }
            }
        }
        fwVar.i = i;
    }

    public final q[] b(q qVar) {
        q[] qVarArr = d.a().W;
        if (this.i.b == 1) {
            if (this.c.a.b == 0) {
                qVarArr = d.a().Y;
                qVar.r = 2;
            } else {
                qVarArr = d.a().Z;
                qVar.r = 3;
            }
            for (int i = 0; i < qVarArr.length; i++) {
                if (qVarArr[i] != null) {
                    qVarArr[i].e = qVar.h().f;
                }
            }
        } else if (this.i.b == q()) {
            qVar.r = 0;
        }
        return qVarArr;
    }

    @Override // com.donglh.narutoninjasaga.d.et
    public final int t() {
        if (this.i.b == 1) {
            return 0;
        }
        return super.t();
    }
}
