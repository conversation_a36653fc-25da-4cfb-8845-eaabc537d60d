package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.Input;
import java.util.Vector;
/* compiled from: LangLa_kk.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/hf.class */
public final class hf extends ek {
    private int a = 0;
    private ke b;
    private dg[] c;
    private fw[] d;
    private fw e;
    private fw f;
    private String[] g;

    public hf(int i, int i2, int i3, cn cnVar, ke keVar) {
        this.l = cnVar;
        this.b = keVar;
        g(i, i2);
        if (keVar.a()) {
            int i4 = keVar.f > 0 ? 0 + 15 : 0;
            i4 = keVar.g > 0 ? i4 + 15 : i4;
            i4 = keVar.h > 0 ? i4 + 15 : i4;
            i4 = keVar.i > 0 ? i4 + 15 : i4;
            i4 = keVar.j > 0 ? i4 + 15 : i4;
            a_(230, (i4 < 15 ? 15 : i4) + 160);
        } else {
            a_(230, 165);
        }
        this.c = new dg[4];
        this.c[0] = new dg(this.aM - 53, this.aN - 26, com.donglh.narutoninjasaga.c.a.w, cnVar, 1000, 7);
        this.c[0].a_(49, 22);
        this.c[1] = new dg(this.aM - 17, 3, "", cnVar, 1001, 52);
        this.c[2] = new dg(this.aM - Input.Keys.BUTTON_L2, this.aN - 26, com.donglh.narutoninjasaga.c.a.x, cnVar, 1002, 7);
        this.c[2].a_(49, 22);
        if (keVar.a()) {
            this.c[3] = new dg(8, Input.Keys.NUMPAD_4, com.donglh.narutoninjasaga.c.a.bC, cnVar, 1003, 7);
            this.c[3].a_(41, 18);
        }
        this.d = a(this.c);
        this.g = kk.c(kk.c, keVar.d, this.aM - 18);
        this.e = new fw((byte) 1, 2, 40, this.aM - 4, 84, 14, this.g.length);
        this.f = new fw((byte) 1, 54, 137, 28, 28, 28, 1);
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        int i = lVar.a;
        int i2 = lVar.b;
        a(lVar, 0, 0, this.aM, this.aN, -11, 55, 56);
        kk.a(kk.c, lVar, com.donglh.narutoninjasaga.c.a.gi, 50, 12, 1, -6488, -10275328);
        kk.a(kk.c, lVar, this.b.c, 55, 12, 0, -1, -10275328);
        kk.a(kk.c, lVar, com.donglh.narutoninjasaga.c.a.gj, 50, 26, 1, -6488, -10275328);
        kk.a(kk.c, lVar, this.b.b, 55, 26, 0, -22360, -10275328);
        if (this.b.a()) {
            kk.a(kk.c, lVar, com.donglh.narutoninjasaga.c.a.gk, 8, 140, 0, -10831436, 0);
            int i3 = 125;
            if (this.b.f > 0) {
                i3 = 125 + 15;
                kk.a(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.c(this.b.f), 90, 140, 0, -1, 0);
                a(lVar, 90, 138, com.donglh.narutoninjasaga.e.aw.c(this.b.f), (byte) 0);
            }
            if (this.b.g > 0) {
                i3 += 15;
                kk.a(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.c(this.b.g), 90, i3, 0, -1, 0);
                a(lVar, 90, i3 - 2, com.donglh.narutoninjasaga.e.aw.c(this.b.g), (byte) 1);
            }
            if (this.b.h > 0) {
                i3 += 15;
                kk.a(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.c(this.b.h), 90, i3, 0, -1, 0);
                a(lVar, 90, i3 - 2, com.donglh.narutoninjasaga.e.aw.c(this.b.h), (byte) 2);
            }
            if (this.b.i > 0) {
                i3 += 15;
                kk.a(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.c(this.b.i), 90, i3, 0, -1, 0);
                a(lVar, 90, i3 - 2, com.donglh.narutoninjasaga.e.aw.c(this.b.i), (byte) 3);
            }
            if (this.b.j > 0) {
                int i4 = i3 + 15;
                kk.a(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.c(this.b.j), 90, i4, 0, -1, 0);
                a(lVar, 90, i4 - 2, com.donglh.narutoninjasaga.e.aw.c(this.b.j), (byte) 6);
            }
            a(lVar, (int) this.f.aY, (int) this.f.aZ, this.b.e, false);
        }
        a(lVar, 2, this.e.aZ - 5, this.aM - 4, this.e.aN + 10, 3, 55, 56);
        a(lVar, this.e);
        for (int i5 = 0; i5 < this.g.length; i5++) {
            if (this.e.b(i5)) {
                kk.a(kk.c, lVar, this.g[i5], 10, 7 + (i5 * this.e.f), 0, -1, 0);
            }
        }
        b(lVar);
        a(lVar, i, i2);
        a(lVar, this.c, i, i2);
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void b() {
        this.e.a();
        this.f.a();
        if (!this.b.a()) {
            this.c[3] = null;
        }
        a(this.c, this.d);
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        Vector vector = new Vector();
        for (int i = 0; i < this.d.length; i++) {
            if (this.d[i] != null && this.c[i] != null) {
                vector.addElement(this.d[i].a(this.c[i].b, this));
            }
        }
        vector.addElement(this.e.a(1010, this));
        vector.addElement(this.f.a(1011, this));
        vector.addElement(new gu(1500, 0, 0, this.aM, this.aN, null, this));
        return vector;
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        switch (guVar.b) {
            case 1000:
                a();
                ((ji) this.l).a(this.b);
                return;
            case 1001:
                a();
                return;
            case 1002:
                a();
                c(true);
                return;
            case 1003:
                a();
                ke keVar = this.b;
                try {
                    if (keVar.a()) {
                        com.donglh.narutoninjasaga.e.ak akVar = new com.donglh.narutoninjasaga.e.ak((byte) 95);
                        akVar.b(keVar.a);
                        akVar.l();
                        return;
                    }
                    return;
                } catch (Exception unused) {
                    return;
                }
            case 1011:
                this.l.n = a(this.l, guVar.j.aY, guVar.j.aZ - 68, 28, this.b.e);
                return;
            case 2002:
                c(false);
                return;
            default:
                return;
        }
    }

    private void c(boolean z) {
        try {
            if (this.b.a()) {
                com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.gl, -65536);
            } else if (z) {
                com.donglh.narutoninjasaga.e.f.c().am.a(com.donglh.narutoninjasaga.c.a.gm, 2002, this);
            } else {
                Vector vector = new Vector();
                vector.add(this.b);
                ji.b(vector);
            }
        } catch (Exception unused) {
        }
    }

    public final void a() {
        this.l.b(this);
    }
}
