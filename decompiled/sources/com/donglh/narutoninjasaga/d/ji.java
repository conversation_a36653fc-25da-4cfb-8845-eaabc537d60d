package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.Input;
import java.util.Vector;
/* compiled from: LangLa_t.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/ji.class */
public final class ji extends jh {
    private fw h;
    private fw[] B;
    private cb[] C;
    private cb D;
    private boolean E;

    public ji(com.donglh.narutoninjasaga.e.ai aiVar) {
        super(aiVar, new String[]{com.donglh.narutoninjasaga.c.a.y});
        this.s = aiVar;
        d(320, 222);
        this.u = false;
        this.D = a(5, a_() + 5, com.donglh.narutoninjasaga.c.a.gy, 1000);
        a(this.D, 0);
        dg a = a(68, a_() + 2, com.donglh.narutoninjasaga.c.a.gz, this, 1001, 7);
        a.a_(55, 20);
        a(a, 0);
        dg a2 = a(128, a_() + 2, com.donglh.narutoninjasaga.c.a.gA, this, 1002, 7);
        a2.a_(55, 20);
        a(a2, 0);
        dg a3 = a(188, a_() + 2, com.donglh.narutoninjasaga.c.a.gB, this, 1003, 7);
        a3.a_(55, 20);
        a(a3, 0);
        dg a4 = a(Input.Keys.F5, a_() + 2, com.donglh.narutoninjasaga.c.a.oH, this, 1004, 7);
        a4.a_(55, 20);
        a(a4, 0);
        e();
        a(0);
    }

    public final void e() {
        this.h = new fw((byte) 1, 4, a_() + 25, (this.aM - 8) - 5, Input.Keys.NUMPAD_6, 30, com.donglh.narutoninjasaga.e.n.n().h.size());
        this.C = new cb[com.donglh.narutoninjasaga.e.n.n().h.size()];
        this.B = new fw[com.donglh.narutoninjasaga.e.n.n().h.size()];
        for (int i = 0; i < com.donglh.narutoninjasaga.e.n.n().h.size(); i++) {
            this.C[i] = new cb(1, 7 + (i * this.h.f), "", this, 0);
            this.B[i] = new gb(0, 0, this.C[i].aM, this.C[i].aN, this.C[i].aN, this.h);
        }
    }

    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.cn
    public final void a(int i, Object obj, co coVar) {
        if (this.i.b == 0) {
            switch (i) {
                case 1000:
                    int i2 = 0;
                    for (int i3 = 0; i3 < this.C.length; i3++) {
                        if (this.C[i3].a) {
                            i2++;
                        }
                    }
                    boolean z = i2 != this.C.length;
                    for (int i4 = 0; i4 < this.C.length; i4++) {
                        this.C[i4].a = z;
                    }
                    return;
                default:
                    return;
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.jh, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.cn
    public final void a_(int i) {
    }

    /* JADX WARN: Removed duplicated region for block: B:22:0x0151 A[Catch: Exception -> 0x0269, Exception -> 0x02bb, TryCatch #0 {Exception -> 0x02bb, blocks: (B:2:0x0000, B:4:0x0011, B:6:0x005d, B:8:0x0069, B:9:0x0075, B:11:0x007f, B:13:0x009a, B:15:0x00d1, B:19:0x012c, B:20:0x014a, B:22:0x0151, B:24:0x0197, B:26:0x0206, B:27:0x022d, B:23:0x0176, B:16:0x00ff, B:18:0x010b, B:30:0x026a, B:31:0x0270, B:5:0x0019), top: B:36:0x0000 }] */
    /* JADX WARN: Removed duplicated region for block: B:23:0x0176 A[Catch: Exception -> 0x0269, Exception -> 0x02bb, TryCatch #0 {Exception -> 0x02bb, blocks: (B:2:0x0000, B:4:0x0011, B:6:0x005d, B:8:0x0069, B:9:0x0075, B:11:0x007f, B:13:0x009a, B:15:0x00d1, B:19:0x012c, B:20:0x014a, B:22:0x0151, B:24:0x0197, B:26:0x0206, B:27:0x022d, B:23:0x0176, B:16:0x00ff, B:18:0x010b, B:30:0x026a, B:31:0x0270, B:5:0x0019), top: B:36:0x0000 }] */
    /* JADX WARN: Removed duplicated region for block: B:26:0x0206 A[Catch: Exception -> 0x0269, Exception -> 0x02bb, TryCatch #0 {Exception -> 0x02bb, blocks: (B:2:0x0000, B:4:0x0011, B:6:0x005d, B:8:0x0069, B:9:0x0075, B:11:0x007f, B:13:0x009a, B:15:0x00d1, B:19:0x012c, B:20:0x014a, B:22:0x0151, B:24:0x0197, B:26:0x0206, B:27:0x022d, B:23:0x0176, B:16:0x00ff, B:18:0x010b, B:30:0x026a, B:31:0x0270, B:5:0x0019), top: B:36:0x0000 }] */
    /* JADX WARN: Type inference failed for: r0v20, types: [com.donglh.narutoninjasaga.d.fw, java.lang.Exception] */
    @Override // com.donglh.narutoninjasaga.d.jh, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void a(com.donglh.narutoninjasaga.d.l r13) {
        /*
            Method dump skipped, instructions count: 705
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: com.donglh.narutoninjasaga.d.ji.a(com.donglh.narutoninjasaga.d.l):void");
    }

    /* JADX WARN: Type inference failed for: r0v8, types: [java.lang.Exception] */
    @Override // com.donglh.narutoninjasaga.d.jh, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void b() {
        ?? r0;
        try {
            super.b();
            if (this.i.b == 0) {
                this.h.a();
                int i = 0;
                while (true) {
                    r0 = i;
                    if (r0 < this.C.length) {
                        this.C[i].b();
                        this.B[i].a();
                        if (this.B[i].j != -1) {
                            this.C[i].b = true;
                        } else {
                            this.C[i].b = false;
                        }
                        i++;
                    } else {
                        return;
                    }
                }
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v1, types: [java.util.Vector] */
    /* JADX WARN: Type inference failed for: r0v11, types: [java.util.Vector] */
    /* JADX WARN: Type inference failed for: r0v2, types: [java.lang.Exception] */
    @Override // com.donglh.narutoninjasaga.d.jh, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        ?? c = super.c();
        try {
            if (!this.E) {
                c.removeElementAt(0);
            }
            if (this.i.b == 0) {
                for (int i = 0; i < this.B.length; i++) {
                    int i2 = this.h.aY + this.C[i].aY;
                    int i3 = (this.h.aZ + this.C[i].aZ) - this.h.d;
                    if (com.donglh.narutoninjasaga.e.aw.a((int) this.h.aY, (int) this.h.aZ, this.h.aY + this.h.aM, this.h.aZ + this.h.aN, i2, i3) && com.donglh.narutoninjasaga.e.aw.a((int) this.h.aY, (int) this.h.aZ, this.h.aY + this.h.aM, this.h.aZ + this.h.aN, i2 + this.C[i].aM, i3 + this.C[i].aN)) {
                        this.B[i].aY = (short) i2;
                        this.B[i].aZ = (short) i3;
                        c.addElement(this.B[i].a(i + 8000, this));
                    }
                }
                c = c;
                c.addElement(this.h.a(1005, this));
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) c);
        }
        return c;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v18, types: [com.donglh.narutoninjasaga.d.ji, com.donglh.narutoninjasaga.d.cn] */
    /* JADX WARN: Type inference failed for: r0v19, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v48, types: [com.donglh.narutoninjasaga.e.ak] */
    @Override // com.donglh.narutoninjasaga.d.jh, com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        super.a(guVar, i, i2);
        if (this.i.b == 0) {
            if (guVar.b >= 8000 && guVar.b < 9000) {
                int i3 = guVar.b - 8000;
                this.C[i3].a = !this.C[i3].a;
                int i4 = 0;
                for (int i5 = 0; i5 < this.C.length; i5++) {
                    if (this.C[i5].a) {
                        i4++;
                    }
                }
                if (i4 == this.C.length) {
                    this.D.a = true;
                } else {
                    this.D.a = false;
                }
            }
            switch (guVar.b) {
                case 1001:
                    a(true);
                    return;
                case 1002:
                    b(true);
                    return;
                case 1003:
                    a((ke) null);
                    return;
                case 1004:
                    this.s.a(com.donglh.narutoninjasaga.c.a.pF, 2003, this);
                    return;
                case 1005:
                    if (guVar.j.i >= 0) {
                        ?? r0 = this;
                        try {
                            ke keVar = (ke) com.donglh.narutoninjasaga.e.n.n().h.elementAt(guVar.j.i);
                            hf hfVar = new hf(r0.aY + 30, r0.aZ, 0, r0, keVar);
                            if (hfVar.aY > (com.donglh.narutoninjasaga.e.f.c().o - hfVar.aM) - r0.aY) {
                                hfVar.aY = (short) ((com.donglh.narutoninjasaga.e.f.c().o - hfVar.aM) - r0.aY);
                            }
                            if (hfVar.aY < (-r0.aY)) {
                                hfVar.aY = (short) (-r0.aY);
                            }
                            if (hfVar.aZ > (com.donglh.narutoninjasaga.e.f.c().p - hfVar.aN) - r0.aZ) {
                                hfVar.aZ = (short) ((com.donglh.narutoninjasaga.e.f.c().p - hfVar.aN) - r0.aZ);
                            }
                            for (int size = r0.k.size() - 1; size >= 0; size--) {
                                cl clVar = (cl) r0.k.get(size);
                                if (clVar instanceof hf) {
                                    r0.b(clVar);
                                }
                            }
                            r0.a(hfVar);
                            r0.n = hfVar;
                            if (!keVar.k) {
                                keVar.k = true;
                                com.donglh.narutoninjasaga.e.ak akVar = new com.donglh.narutoninjasaga.e.ak((byte) 96);
                                akVar.b(keVar.a);
                                r0 = akVar;
                                r0.l();
                            }
                            return;
                        } catch (Exception e) {
                            com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                            return;
                        }
                    }
                    return;
                case 2001:
                    a(false);
                    return;
                case 2002:
                    b(false);
                    return;
                case 2003:
                    try {
                        com.donglh.narutoninjasaga.e.ak.b((byte) -50).l();
                        return;
                    } catch (Exception unused) {
                        return;
                    }
                default:
                    return;
            }
        }
    }

    public final void a(ke keVar) {
        this.E = true;
        a(q() - 1);
        if (keVar != null) {
            this.c.a(keVar.b);
            if (!keVar.c.contains("Re: ")) {
                this.b.a("Re: " + keVar.c);
            }
        }
    }

    private void a(boolean z) {
        Vector vector = new Vector();
        int i = 0;
        for (int i2 = 0; i2 < com.donglh.narutoninjasaga.e.n.n().h.size(); i2++) {
            ke keVar = (ke) com.donglh.narutoninjasaga.e.n.n().h.elementAt(i2);
            if (this.C[i2].a) {
                if (!keVar.a()) {
                    vector.add(keVar);
                } else {
                    i++;
                }
            }
        }
        if (i > 0) {
            com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.gE, -65536);
        } else if (vector.size() <= 0) {
            com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.gF, -65536);
        } else if (z) {
            com.donglh.narutoninjasaga.e.f.c().am.a(com.donglh.narutoninjasaga.c.a.gm, 2001, this);
        } else {
            b(vector);
        }
    }

    private void b(boolean z) {
        Vector vector = new Vector();
        for (int i = 0; i < com.donglh.narutoninjasaga.e.n.n().h.size(); i++) {
            ke keVar = (ke) com.donglh.narutoninjasaga.e.n.n().h.elementAt(i);
            if (keVar.k && !keVar.a()) {
                vector.add(keVar);
            }
        }
        if (vector.size() <= 0) {
            com.donglh.narutoninjasaga.e.f.c().am.b(com.donglh.narutoninjasaga.c.a.gF, -65536);
        } else if (z) {
            com.donglh.narutoninjasaga.e.f.c().am.a(com.donglh.narutoninjasaga.c.a.gm, 2002, this);
        } else {
            b(vector);
        }
    }

    @Override // com.donglh.narutoninjasaga.d.jh, com.donglh.narutoninjasaga.d.ej
    public final void d() {
        super.d();
    }

    /* JADX WARN: Type inference failed for: r0v1, types: [com.donglh.narutoninjasaga.e.ak, java.lang.Exception] */
    public static void b(Vector vector) {
        ?? akVar;
        try {
            akVar = new com.donglh.narutoninjasaga.e.ak((byte) 88);
            akVar.b(vector.size());
            for (int i = 0; i < vector.size(); i++) {
                akVar.b(((ke) vector.elementAt(i)).a);
            }
            akVar.l();
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) akVar);
        }
    }

    public final void a(String str) {
        a((ke) null);
        this.c.a(str);
    }
}
