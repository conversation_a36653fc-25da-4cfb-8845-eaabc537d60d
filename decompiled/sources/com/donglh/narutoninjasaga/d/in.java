package com.donglh.narutoninjasaga.d;

import java.util.Vector;
/* compiled from: LangLa_md.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/in.class */
public final class in extends com.donglh.narutoninjasaga.e.ai {
    private int d;
    private int e;
    private float f;
    public int a;
    private int g;
    private int b = 625;
    private int c = 344;
    private com.donglh.narutoninjasaga.e.az h = a.c("a");

    public in(int i, String str, String str2) {
        this.f = 1.0f;
        this.a = i;
        this.d = this.b;
        this.e = this.c;
        float f = com.donglh.narutoninjasaga.e.f.c().o / this.b;
        float f2 = com.donglh.narutoninjasaga.e.f.c().p / this.c;
        this.f = f > f2 ? f : f2;
        this.e = (int) (this.c * this.f * com.donglh.narutoninjasaga.e.f.c().u);
        this.d = (int) (this.b * this.f * com.donglh.narutoninjasaga.e.f.c().u);
        Vector vector = new Vector();
        String[] a = com.donglh.narutoninjasaga.e.aw.a(str2, "\\n");
        vector.addElement(new ha(new Vector(), "c#yellow" + str + "\nc#white", (byte) 2));
        for (int i2 = 0; i2 < a.length; i2++) {
            String[] split = a[i2].split("@");
            if (split.length == 1) {
                vector.addElement(new ha(d.a().ah, a[i2].replaceAll("#", "c#green" + d.a().N + "c#white").replaceAll("\\$", "c#blue" + com.donglh.narutoninjasaga.e.f.c().R[d.a().H].b + "c#white").replaceAll("\\*", com.donglh.narutoninjasaga.c.a.sk[d.a().G]).replaceAll("\\&", com.donglh.narutoninjasaga.c.a.sl[d.a().G]).replaceAll("\\^", com.donglh.narutoninjasaga.c.a.sm[d.a().G]).replaceAll("\\%", com.donglh.narutoninjasaga.c.a.sn[d.a().G]).replaceAll("\\~", com.donglh.narutoninjasaga.c.a.so[d.a().G]).replaceAll("\\_", com.donglh.narutoninjasaga.c.a.sp[d.a().G]), (byte) 2));
            } else {
                String replaceAll = split[1].replaceAll("#", "c#green" + d.a().N + "c#white").replaceAll("\\$", "c#blue" + com.donglh.narutoninjasaga.e.f.c().R[d.a().H].b + "c#white").replaceAll("\\*", com.donglh.narutoninjasaga.c.a.sk[d.a().G]).replaceAll("\\&", com.donglh.narutoninjasaga.c.a.sl[d.a().G]).replaceAll("\\^", com.donglh.narutoninjasaga.c.a.sm[d.a().G]).replaceAll("\\%", com.donglh.narutoninjasaga.c.a.sn[d.a().G]).replaceAll("\\~", com.donglh.narutoninjasaga.c.a.so[d.a().G]).replaceAll("\\_", com.donglh.narutoninjasaga.c.a.sp[d.a().G]);
                int parseInt = Integer.parseInt(split[0]);
                if (parseInt >= 0) {
                    vector.addElement(new ha(new jt((byte) 0, parseInt, 0, 0).a().b[0], replaceAll));
                } else {
                    vector.addElement(new ha(d.a().K(), replaceAll, (byte) 1));
                }
            }
        }
        a(new bk(this, vector, -3));
    }

    @Override // com.donglh.narutoninjasaga.e.ai
    public final void a() {
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a(int i, Object obj, co coVar) {
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a_(int i) {
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        if (com.donglh.narutoninjasaga.e.a.f().h > 0) {
            com.donglh.narutoninjasaga.e.a.f().h--;
            if (com.donglh.narutoninjasaga.e.a.f().h == 0) {
                com.donglh.narutoninjasaga.e.f.c().f();
                return;
            }
        }
        if (this.h != null) {
            lVar.a(this.h, 0, 0, this.h.c, this.h.d, 0, com.donglh.narutoninjasaga.e.f.c().o / 2, com.donglh.narutoninjasaga.e.f.c().p / 2, com.donglh.narutoninjasaga.e.f.c().q, com.donglh.narutoninjasaga.e.f.c().r, 3);
        }
        if (this.g > 0 && this.h != null) {
            this.h.a();
            this.h = null;
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v12, types: [com.donglh.narutoninjasaga.e.f] */
    /* JADX WARN: Type inference failed for: r0v13, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v17, types: [com.donglh.narutoninjasaga.e.ak] */
    @Override // com.donglh.narutoninjasaga.d.co
    public final void b() {
        com.donglh.narutoninjasaga.e.n.n().b();
        if (this.g > 0) {
            this.g--;
            if (this.g == 0) {
                ?? c = com.donglh.narutoninjasaga.e.f.c();
                c.a(com.donglh.narutoninjasaga.e.n.n());
                try {
                    if (this.a == 0) {
                        c = com.donglh.narutoninjasaga.e.ak.b((byte) -123);
                        c.l();
                    }
                } catch (Exception e) {
                    com.donglh.narutoninjasaga.e.aw.a((Exception) c);
                }
            }
        } else if (!(this.cg instanceof bk)) {
            this.g = 1;
        }
        com.donglh.narutoninjasaga.e.f.c().aE.a = false;
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void b_() {
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
    }

    @Override // com.donglh.narutoninjasaga.e.ai
    public final void e() {
    }
}
