package com.donglh.narutoninjasaga.d;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
/* compiled from: LangLa_ma.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/il.class */
public final class il implements Runnable {
    private com.donglh.narutoninjasaga.e.ak a;
    private com.donglh.narutoninjasaga.e.n b;

    public il(com.donglh.narutoninjasaga.e.n nVar, com.donglh.narutoninjasaga.e.ak akVar) {
        this.b = nVar;
        this.a = akVar;
    }

    /* JADX WARN: Type inference failed for: r0v16, types: [java.lang.Thread, java.lang.Exception] */
    @Override // java.lang.Runnable
    public final void run() {
        ?? thread;
        try {
            byte[] j = com.donglh.narutoninjasaga.e.aw.j(this.a.b.d());
            if (j != null) {
                short[] sArr = new short[j.length / 2];
                ByteBuffer.wrap(j).order(ByteOrder.LITTLE_ENDIAN).asShortBuffer().get(sArr);
                thread = new Thread(new dd(new dc(sArr)));
                thread.start();
            }
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) thread);
        }
    }
}
