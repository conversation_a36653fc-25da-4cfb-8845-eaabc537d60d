package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.Input;
import com.badlogic.gdx.net.HttpStatus;
import java.util.Vector;
/* compiled from: LangLa_o.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/jd.class */
public final class jd extends et {
    private fx b;
    private dg c;
    public q[] a;

    public jd(com.donglh.narutoninjasaga.e.ai aiVar) {
        super(aiVar, com.donglh.narutoninjasaga.c.a.sU);
        this.a = new q[16];
        this.c = a((this.aM / 2) - 35, this.aN - 33, com.donglh.narutoninjasaga.c.a.aE, this, 5002, -8);
        a(this.c, 0);
        this.b = new fx((byte) 1, 7, a_() + 3, 128, 128, 32, com.donglh.narutoninjasaga.e.aw.c(this.a.length, 4), 4);
    }

    @Override // com.donglh.narutoninjasaga.d.et
    public final void a() {
        this.A = new q[d.a().W.length];
        int i = 0;
        for (int i2 = 0; i2 < d.a().W.length; i2++) {
            q qVar = d.a().W[i2];
            if (qVar != null && (qVar.c == 171 || qVar.c == 172 || qVar.c == 173 || qVar.c == 355 || qVar.c == 356 || qVar.c == 357 || qVar.c == 358 || qVar.c == 359 || qVar.c == 360 || qVar.c == 361 || qVar.c == 362 || qVar.c == 363)) {
                this.A[i] = d.a().W[i2];
                i++;
            }
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v10, types: [java.lang.Exception] */
    /* JADX WARN: Type inference failed for: r0v19, types: [com.donglh.narutoninjasaga.e.ak] */
    /* JADX WARN: Type inference failed for: r0v9, types: [com.donglh.narutoninjasaga.d.jd] */
    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        if (this.i.b == q() && guVar.b == 0) {
            d();
        }
        super.a(guVar, i, i2);
        if (this.i.b == 0) {
            switch (guVar.b) {
                case 5001:
                    this.n = a(guVar, this, this.a[guVar.j.i]);
                    return;
                case 5002:
                    ?? r0 = this;
                    try {
                        com.donglh.narutoninjasaga.e.ak b = com.donglh.narutoninjasaga.e.ak.b((byte) -53);
                        int i3 = 0;
                        for (int i4 = 0; i4 < r0.a.length; i4++) {
                            if (r0.a[i4] != null) {
                                i3++;
                            }
                        }
                        b.a(i3);
                        for (int i5 = 0; i5 < r0.a.length; i5++) {
                            if (r0.a[i5] != null) {
                                b.b(r0.a[i5].e);
                            }
                        }
                        r0 = b;
                        r0.l();
                        return;
                    } catch (Exception e) {
                        com.donglh.narutoninjasaga.e.aw.a((Exception) r0);
                        return;
                    }
                default:
                    return;
            }
        }
    }

    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.cn
    public final void a_(int i) {
    }

    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        super.a(lVar);
        if (this.i.b == 0) {
            kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.oc, Input.Keys.NUMPAD_0, a_() + 20, 0, -1, -16777216);
            kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.od, Input.Keys.NUMPAD_0, a_() + 36, 0, -1, -16777216);
            kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.oe, Input.Keys.NUMPAD_0, a_() + 52, 0, -1, -16777216);
            kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.of, Input.Keys.NUMPAD_0, a_() + 68, 0, -1, -16777216);
            a(lVar, this.b);
            for (int i = 0; i < this.b.g; i++) {
                for (int i2 = 0; i2 < this.b.o; i2++) {
                    if (this.b.b(i)) {
                        b(lVar, i2 * this.b.f, i * this.b.f, this.a[(i * this.b.o) + i2], (i * this.b.o) + i2 == this.b.i);
                    }
                }
            }
            b(lVar);
            a(lVar, this.aY + 4, this.aZ + a_());
        }
    }

    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.co
    public final void b() {
        super.b();
        if (this.i.b == 0) {
            this.b.a();
        } else {
            a();
        }
        if (com.donglh.narutoninjasaga.e.n.n().aC <= 12 && this.s.cf.lastElement().equals(this)) {
            if (com.donglh.narutoninjasaga.e.n.n().aC == 1 && com.donglh.narutoninjasaga.e.n.n().aD == 1) {
                b(378);
            } else if (com.donglh.narutoninjasaga.e.n.n().aC == 2 && com.donglh.narutoninjasaga.e.n.n().aD == 1) {
                b(194);
            } else if (com.donglh.narutoninjasaga.e.n.n().aC == 3 && com.donglh.narutoninjasaga.e.n.n().aD == 4) {
                b(379);
            } else if (com.donglh.narutoninjasaga.e.n.n().aC == 6 && com.donglh.narutoninjasaga.e.n.n().aD == 3) {
                b(HttpStatus.SC_CREATED);
            } else if (com.donglh.narutoninjasaga.e.n.n().aC == 7 && com.donglh.narutoninjasaga.e.n.n().aD == 3) {
                if (this.i.b != 0) {
                    if (this.n == null) {
                        if (d.a().e(HttpStatus.SC_MULTI_STATUS) > 0) {
                            int[] c = d.a().c(HttpStatus.SC_MULTI_STATUS);
                            if (c != null) {
                                com.donglh.narutoninjasaga.e.f.c().aE.a(this.aY + c[0] + 15, this.aZ + 25 + c[1] + 15);
                            }
                        } else if (d.a().e(208) > 0) {
                            int[] c2 = d.a().c(208);
                            if (c2 != null) {
                                com.donglh.narutoninjasaga.e.f.c().aE.a(this.aY + c2[0] + 15, this.aZ + 25 + c2[1] + 15);
                            }
                        } else {
                            com.donglh.narutoninjasaga.e.f.c().aE.a(this.aY + 70, this.aZ + 10);
                        }
                        com.donglh.narutoninjasaga.e.f.c().aE.a = true;
                        return;
                    }
                    return;
                }
                if (f(HttpStatus.SC_MULTI_STATUS) > 0) {
                    com.donglh.narutoninjasaga.e.f.c().aE.a(this.aY + (this.aM / 2), (this.aZ + this.aN) - 20);
                } else {
                    com.donglh.narutoninjasaga.e.f.c().aE.a(this.aY + 160, this.aZ + 10);
                }
                com.donglh.narutoninjasaga.e.f.c().aE.a = true;
            } else if (com.donglh.narutoninjasaga.e.n.n().aC == 10 && com.donglh.narutoninjasaga.e.n.n().aD == 5) {
                b(381);
            } else if (com.donglh.narutoninjasaga.e.n.n().aC == 11 && com.donglh.narutoninjasaga.e.n.n().aD == 5) {
                if (this.i.b == 0) {
                    if (f(382) == 0) {
                        com.donglh.narutoninjasaga.e.f.c().aE.a(this.aY + 40, (this.aZ + this.aN) - 35);
                    } else {
                        com.donglh.narutoninjasaga.e.f.c().aE.a(this.aY + (this.aM / 2), (this.aZ + this.aN) - 20);
                    }
                    com.donglh.narutoninjasaga.e.f.c().aE.a = true;
                    return;
                }
                com.donglh.narutoninjasaga.e.f.c().aE.a(this.aY + 70, this.aZ + 10);
                com.donglh.narutoninjasaga.e.f.c().aE.a = true;
            }
        }
    }

    private void b(int i) {
        if (this.i.b == 0) {
            if (f(i) > 0) {
                com.donglh.narutoninjasaga.e.f.c().aE.a(this.aY + (this.aM / 2), (this.aZ + this.aN) - 20);
            } else {
                com.donglh.narutoninjasaga.e.f.c().aE.a(this.aY + 160, this.aZ + 10);
            }
            com.donglh.narutoninjasaga.e.f.c().aE.a = true;
        } else if (this.n == null) {
            if (d.a().e(i) > 0) {
                int[] c = d.a().c(i);
                if (c != null) {
                    com.donglh.narutoninjasaga.e.f.c().aE.a(this.aY + c[0] + 15, this.aZ + 25 + c[1] + 15);
                }
            } else {
                com.donglh.narutoninjasaga.e.f.c().aE.a(this.aY + 70, this.aZ + 10);
            }
            com.donglh.narutoninjasaga.e.f.c().aE.a = true;
        }
    }

    private int f(int i) {
        int i2 = 0;
        for (int i3 = 0; i3 < this.a.length; i3++) {
            if (this.a[i3] != null && this.a[i3].c == i) {
                i2 += this.a[i3].O();
            }
        }
        return i2;
    }

    @Override // com.donglh.narutoninjasaga.d.et, com.donglh.narutoninjasaga.d.el, com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        Vector c = super.c();
        if (this.i.b == 0) {
            c.addElement(this.b.a(5001, this));
        }
        return c;
    }

    @Override // com.donglh.narutoninjasaga.d.ej
    public final void d() {
        super.d();
        for (int i = 0; i < this.a.length; i++) {
            if (this.a[i] != null) {
                d.a().W[this.a[i].e] = this.a[i];
                this.a[i] = null;
            }
        }
    }
}
