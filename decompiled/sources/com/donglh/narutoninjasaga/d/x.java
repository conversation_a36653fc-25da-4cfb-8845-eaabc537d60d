package com.donglh.narutoninjasaga.d;

import com.badlogic.gdx.Input;
import java.util.Vector;
/* compiled from: LangLa_ab.java */
/* loaded from: naruto-game.jar:com/donglh/narutoninjasaga/d/x.class */
public final class x extends em {
    public ib[] a = new ib[0];
    private ib[] b = new ib[0];
    private hw c;
    private fw d;
    private cd e;
    private static int f;

    public x(com.donglh.narutoninjasaga.e.ai aiVar) {
        this.s = aiVar;
        d(316, 213);
        this.u = false;
        this.c = new hw(new gz(1001, com.donglh.narutoninjasaga.c.a.rU), 4, a_(), this.aM - 8, 76, this);
        this.e = a(225, this.c.d() + 4, 80, 3, new gz(1002, com.donglh.narutoninjasaga.c.a.rV), this, 0);
        a();
        b(f);
    }

    public final void a() {
        if (this.e.b.b == 0) {
            this.b = this.a;
        } else if (this.e.b.b == 1) {
            Vector vector = new Vector();
            for (int i = 0; i < this.a.length; i++) {
                if (this.a[i].e == 6) {
                    vector.add(this.a[i]);
                }
            }
            this.b = new ib[vector.size()];
            for (int i2 = 0; i2 < this.b.length; i2++) {
                this.b[i2] = (ib) vector.get(i2);
            }
        } else if (this.e.b.b == 2) {
            Vector vector2 = new Vector();
            for (int i3 = 0; i3 < this.a.length; i3++) {
                if (this.a[i3].e == 7) {
                    vector2.add(this.a[i3]);
                }
            }
            this.b = new ib[vector2.size()];
            for (int i4 = 0; i4 < this.b.length; i4++) {
                this.b[i4] = (ib) vector2.get(i4);
            }
        }
        this.d = new fw((byte) 1, 4, this.c.d() + 28, this.aM - 8, 126, 18, this.b.length);
    }

    private void b(int i) {
        this.c.a(i);
        switch (i) {
            case 0:
                this.e.o = false;
                break;
            default:
                this.e.o = true;
                break;
        }
        e();
    }

    /* JADX WARN: Type inference failed for: r0v1, types: [com.donglh.narutoninjasaga.e.ak, java.lang.Exception] */
    private void e() {
        ?? akVar;
        try {
            akVar = new com.donglh.narutoninjasaga.e.ak((byte) -53);
            akVar.a(this.c.b.b);
            akVar.a(this.e.b.b);
            akVar.l();
        } catch (Exception e) {
            com.donglh.narutoninjasaga.e.aw.a((Exception) akVar);
        }
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void b() {
        this.c.b();
        this.d.a();
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a(int i, Object obj, co coVar) {
        switch (i) {
            case 1002:
                a();
                return;
            case 2002:
                gz gzVar = (gz) obj;
                try {
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.h(this.b[gzVar.a].b);
                    return;
                } catch (Exception unused) {
                    return;
                }
            case 2003:
                gz gzVar2 = (gz) obj;
                try {
                    com.donglh.narutoninjasaga.e.n.n();
                    com.donglh.narutoninjasaga.e.n.e(this.b[gzVar2.a].b);
                    return;
                } catch (Exception unused2) {
                    return;
                }
            case 2004:
                gz gzVar3 = (gz) obj;
                try {
                    ho.c = this.b[gzVar3.a].b;
                    com.donglh.narutoninjasaga.e.n.n().a(this.b[gzVar3.a].b, new hk(com.donglh.narutoninjasaga.e.n.n(), 6));
                    return;
                } catch (Exception unused3) {
                    return;
                }
            default:
                return;
        }
    }

    @Override // com.donglh.narutoninjasaga.d.cn
    public final void a_(int i) {
    }

    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final Vector c() {
        Vector c = super.c();
        c.addElement(this.c.c());
        c.addElement(this.d.a(1003, this));
        return c;
    }

    @Override // com.donglh.narutoninjasaga.d.co
    public final void a(l lVar) {
        a(lVar, (int) this.aY, (int) this.aZ, (int) this.aM, (int) this.aN, com.donglh.narutoninjasaga.c.a.bT, (byte) 2, false);
        this.c.a(lVar);
        b(lVar, this.d);
        a(lVar, 0, -28, this.d.aM, 28, -11, 55, 56);
        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.bU, 20, -15, 2, -6488, -10275328);
        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.bV, 70, -15, 2, -6488, -10275328);
        if (this.c.b.b == 0) {
            kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.bW, Input.Keys.CONTROL_RIGHT, -15, 2, -6488, -10275328);
        }
        kk.b(kk.d, lVar, com.donglh.narutoninjasaga.c.a.C, 190, -15, 2, -6488, -10275328);
        a(lVar, this.d);
        for (int i = 0; i < this.d.g; i++) {
            if (this.d.b(i)) {
                if (i == this.d.i) {
                    lVar.e(13136426);
                    lVar.c(0, i * this.d.f, this.d.aM, this.d.f);
                }
                ib ibVar = this.b[i];
                kk.b(kk.c, lVar, new StringBuilder().append(i + 1).toString(), 20, 8 + (i * this.d.f), 2, -3604601, -16777216);
                kk.b(kk.c, lVar, ibVar.b, 70, 8 + (i * this.d.f), 2, -3604601, -16777216);
                if (this.c.b.b == 0) {
                    kk.b(kk.c, lVar, com.donglh.narutoninjasaga.e.aw.c(ibVar.d), Input.Keys.CONTROL_RIGHT, 8 + (i * this.d.f), 2, -3604601, -16777216);
                }
                kk.b(kk.c, lVar, ibVar.f, 190, 8 + (i * this.d.f), 2, -3604601, -16777216);
                if (ibVar.e == 6) {
                    kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.bY, 255, 8 + (i * this.d.f), 2, -3604601, -16777216);
                } else if (ibVar.e == 7) {
                    kk.b(kk.c, lVar, com.donglh.narutoninjasaga.c.a.bZ, 255, 8 + (i * this.d.f), 2, -3604601, -16777216);
                }
            }
        }
        b(lVar);
    }

    @Override // com.donglh.narutoninjasaga.d.ej, com.donglh.narutoninjasaga.d.co
    public final void a(gu guVar, int i, int i2) {
        switch (guVar.b) {
            case 1001:
                if (guVar.j.i >= 0) {
                    f = guVar.j.i;
                    b(guVar.j.i);
                    return;
                }
                return;
            case 1003:
                if (guVar.j.i >= 0) {
                    int i3 = guVar.j.i;
                    Vector vector = new Vector();
                    vector.addElement(new hz(2002, com.donglh.narutoninjasaga.c.a.ca));
                    vector.addElement(new hz(2003, com.donglh.narutoninjasaga.c.a.cb));
                    vector.addElement(new hz(2004, com.donglh.narutoninjasaga.c.a.cc));
                    if (vector.size() > 0) {
                        String[] strArr = new String[vector.size()];
                        short[] sArr = new short[vector.size()];
                        for (int i4 = 0; i4 < vector.size(); i4++) {
                            hz hzVar = (hz) vector.elementAt(i4);
                            strArr[i4] = hzVar.b;
                            sArr[i4] = (short) hzVar.a;
                        }
                        this.n = a(this, i + 25, i2, new gz(i3, sArr, strArr));
                        return;
                    }
                    return;
                }
                return;
            default:
                return;
        }
    }
}
